/**
 * Type Definitions - SubPrime Pro
 * 
 * Comprehensive type system for the car dealership management application.
 * Provides strong typing for all entities including deals, customers, vehicles,
 * inventory, locations, and deal flow notes.
 * 
 * Enhanced with deal flow notes system for step-by-step documentation
 * and comprehensive approval entity management for Canadian auto credit logic.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { LucideIcon } from 'lucide-react';

// ===== CORE ENTITY TYPES =====

/**
 * Deal Entity - Core business object for vehicle sales transactions
 * Enhanced to capture comprehensive deal data from 5-step form process
 */
export interface Deal {
  id: string;
  customerName: string;
  vehicleInfo: string;
  dealValue: number;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  location: string;
  createdDate: string;
  approvalEntities?: ApprovalEntity[];
  tradeInVehicle?: TradeInVehicle | null;
  financingDetails?: FinancingDetails;
  notes?: DealNote[];
  
  // Enhanced deal data from comprehensive form capture
  customerDetails?: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    phoneNumber: string;
    emailAddress: string;
    fullAddress: string;
    province: string;
    city: string;
    customerPhoto?: string;
    idType: string;
    status: 'Yes' | 'No';
    idImageUpload?: string;
  };
  
  selectedVehicleDetails?: Array<{
    id: number;
    make: string;
    model: string;
    year: number;
    price: string;
    location: string;
    vin: string;
    mileage: string;
    exterior: string;
    interior: string;
    transmission: string;
    fuelType: string;
    features: string[];
  }>;
  
  transactionSummary?: {
    cashPrice: number;
    tradeInAllowance: number;
    totalDealValue: number;
    province: string;
    hasTradeIn: boolean;
    approvalCount: number;
  };
  
  dealNotes?: string;
  createdBy?: string;
  lastModified?: string;
  dealType?: string;
  priority?: string;
  
  // Allow for additional form data
  [key: string]: any;
}

/**
 * Deal Note Entity - Step-based note system for deal documentation
 * 
 * Provides comprehensive note tracking throughout the 5-step deal creation process.
 * Each note is tagged with its originating step and includes full CRUD capabilities.
 */
export interface DealNote {
  id: string;
  step: number;
  stepName: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Deal Note Step Names - Human-readable step identifiers
 * 
 * Made more robust to avoid import issues and provide consistent step naming.
 */
export const DEAL_STEP_NAMES: Readonly<Record<number, string>> = {
  1: 'Customer Information',
  2: 'Approvals configuration', 
  3: 'Trade-in Details',
  4: 'Vehicle Selection',
  5: 'Deal Summary'
} as const;

/**
 * Step Name Utility Function - Safe step name retrieval
 * 
 * Provides a safe way to get step names with proper error handling.
 */
export function getStepName(step: number | undefined | null): string {
  try {
    if (typeof step !== 'number' || isNaN(step) || step < 1 || step > 5) {
      console.warn('Invalid step value:', step);
      return 'Unknown Step';
    }
    return DEAL_STEP_NAMES[step] || `Step ${step}`;
  } catch (error) {
    console.error('Error getting step name:', error);
    return 'Unknown Step';
  }
}

/**
 * Customer Entity - Customer information and contact details
 */
export interface Customer {
  id: string;
  name: string;
  photo?: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  drivingLicenseNumber?: string;
  customerType: 'Individual' | 'Business';
  assignedDealer?: string;
  salesRep?: string;
  leadSource?: string;
  creditScore?: number;
  creditRating?: string;
  totalDeals?: number;
  totalValue?: number;
  totalPurchases?: number;
  lastDealDate?: string;
  lastContact?: string;
  joinDate?: string;
  status: 'active' | 'inactive' | 'prospect' | 'Active' | 'Inactive' | 'Prospect' | 'VIP' | 'Corporate';
  profilePhoto?: string;
  lifetimeValue?: string;
  vehicleInterests?: string[];
}

/**
 * Vehicle Entity - Vehicle inventory and specifications
 */
export interface Vehicle {
  id: number | string;
  make: string;
  model: string;
  year: number;
  trim?: string;
  vin: string;
  price: number | string;
  cost: number | string;
  mileage: string;
  status: 'available' | 'reserved' | 'sold' | 'maintenance' | 'Available' | 'Reserved' | 'Sold';
  location: string;
  exterior: string;
  interior: string;
  transmission: string;
  fuelType: string;
  daysOnLot: number;
  photos: string[];
  features: string[];
  condition?: 'new' | 'used' | 'certified';
  bodyType?: string;
  engine?: string;
  drivetrain?: string;
}

/**
 * Location Entity - Dealership locations and inventory distribution
 */
export interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  phone: string;
  manager: string;
  vehicleCount: number;
  dealCount: number;
  revenue: number;
  status: 'active' | 'inactive';
  count?: number; // For compatibility with existing code
}

// ===== DASHBOARD AND ANALYTICS TYPES =====

/**
 * Dashboard Statistics - High-level metrics for dashboard display
 */
export interface DashboardStat {
  title: string;
  value: string;
  icon: LucideIcon;
  trend: string;
}

/**
 * Statistics Card - Enhanced dashboard metrics with icons and trends
 */
export interface StatCard {
  title: string;
  value: string;
  trend: string;
  trendLabel: string;
  icon: LucideIcon;
  iconBg: string;
  iconColor: string;
}

/**
 * Recent Sales Data - Transaction history for dashboard
 */
export interface RecentSale {
  id: number;
  customer: string;
  vehicle: string;
  amount: string;
  date: string;
  salesperson: string;
  location: string;
}

/**
 * Popular Model Analytics - Vehicle model performance metrics
 */
export interface PopularModel {
  model: string;
  sales: number;
  trend: string;
  percentage: number;
}

/**
 * Gross Earnings Chart Data - Financial performance tracking
 */
export interface GrossEarningsDataPoint {
  month: string;
  earnings: number;
  target: number;
}

/**
 * Deals Closed Chart Data - Sales activity tracking
 */
export interface DealsClosedDataPoint {
  date: string;
  deals: number;
}

// ===== APPROVAL ENTITY SYSTEM =====

/**
 * Approval Entity - Canadian auto credit approval system
 * 
 * Supports multiple approval entities per deal with comprehensive
 * relationship tracking and Canadian-specific credit requirements.
 * Enhanced with streamlined approval details for efficient deal processing.
 */
export interface ApprovalEntity {
  id: string;
  type: 'Primary Applicant' | 'Co-Applicant' | 'Co-Signer' | 'Guarantor';
  name: string;
  email: string;
  phone: string;
  drivingLicenseNumber: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  dateOfBirth: string;
  sin: string;
  employmentStatus: 'Employed' | 'Self-Employed' | 'Unemployed' | 'Retired' | 'Student';
  employer: string;
  jobTitle: string;
  annualIncome: string;
  monthsAtJob: string;
  creditScore: string;
  approvalAmount: string;
  interestRate: string;
  monthlyPayment: string;
  downPayment: string;
  lenderName: string;
  approvalDate: string;
  termLength: string;
  creditHistory: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'No Credit';
  bankruptcyHistory: boolean;
  bankruptcyDate: string;
  notes: string;
  createdDate: string;
  updatedDate: string;
  
  // New streamlined approval fields
  tier?: string;
  maxFinance?: string;
  reserve?: string;
  holdback?: string;
  gpsEnabled?: boolean;
  gpsFee?: string;
}

/**
 * Banking Information - Financial institution details for approval entities
 */
export interface BankingInformation {
  bankName: string;
  accountType: 'Checking' | 'Savings' | 'Business';
  accountNumber: string;
  transitNumber: string;
  institutionNumber: string;
  monthsWithBank: number;
}

/**
 * Credit History - Previous credit experience tracking
 */
export interface CreditHistory {
  type: 'Auto Loan' | 'Mortgage' | 'Credit Card' | 'Personal Loan' | 'Line of Credit';
  lender: string;
  amount: number;
  monthlyPayment: number;
  remainingBalance: number;
  paymentHistory: 'Excellent' | 'Good' | 'Fair' | 'Poor';
  openDate: string;
  status: 'Active' | 'Paid Off' | 'Default';
}

/**
 * Document - Required documentation for credit approval
 */
export interface Document {
  id: string;
  type: 'ID' | 'Proof of Income' | 'Bank Statement' | 'Employment Letter' | 'Insurance' | 'Other';
  name: string;
  url?: string;
  uploadDate: string;
  status: 'Pending' | 'Approved' | 'Rejected';
}

// ===== TRADE-IN VEHICLE SYSTEM =====

/**
 * Trade-In Vehicle - Customer trade-in vehicle information
 */
export interface TradeInVehicle {
  make: string;
  model: string;
  year: number;
  trim?: string;
  vin?: string;
  mileage: string;
  color: string;
  condition: 'Excellent' | 'Good' | 'Fair' | 'Poor';
  tradeAllowance: number;
  fairCashValue: number;
  outstandingLoan?: number;
  lienHolder?: string;
  photos?: string[];
  notes?: string;
}

// ===== FINANCING DETAILS =====

/**
 * Financing Details - Deal financing structure and terms
 */
export interface FinancingDetails {
  lender?: string;
  loanAmount: number;
  downPayment: number;
  interestRate: number;
  termMonths: number;
  monthlyPayment: number;
  financingType: 'Bank' | 'Dealership' | 'Manufacturer' | 'Credit Union' | 'Alternative';
  approvalStatus: 'Pending' | 'Approved' | 'Conditional' | 'Declined';
  conditions?: string[];
}

// ===== DASHBOARD STATISTICS =====

/**
 * Dashboard Statistics - Key performance metrics
 */
export interface DashboardStats {
  totalDeals: number;
  pendingDeals: number;
  approvedDeals: number;
  totalRevenue: number;
  averageDealValue: number;
  conversionRate: number;
  topPerformingLocation: string;
  monthlyGrowth: number;
}

/**
 * Inventory Statistics - Vehicle inventory metrics
 */
export interface InventoryStats {
  totalVehicles: number;
  availableVehicles: number;
  reservedVehicles: number;
  soldThisMonth: number;
  averageDaysOnLot: number;
  totalInventoryValue: number;
  lowStockAlerts: number;
  fastMovingModels: string[];
}

// ===== TABLE CONFIGURATION TYPES =====

/**
 * Table Column Configuration - Flexible table system
 */
export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: any) => React.ReactNode;
}

/**
 * Table Configuration - Complete table setup
 */
export interface TableConfig {
  columns: TableColumn[];
  defaultSort?: {
    key: string;
    direction: 'asc' | 'desc';
  };
  pageSize?: number;
  searchable?: boolean;
  exportable?: boolean;
  selectable?: boolean;
}

// ===== FORM DATA TYPES =====

/**
 * Deal Form Data - Complete deal creation form structure
 * 
 * Enhanced with notes support for step-by-step documentation
 * throughout the deal creation process. Updated with improved
 * customer information fields based on user feedback.
 */
export interface DealFormData {
  // Step 1: Customer Information - Personal Details
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  phoneNumber: string;
  emailAddress: string;
  customerPhoto?: string;
  
  // Step 1: ID Documentation
  idType: string;
  idImageUpload?: string;
  
  // Step 1: Residential Information
  suiteNumber?: string;
  address: string;
  streetName: string;
  city: string;
  province: string;
  
  // Step 1: Status
  status: 'Yes' | 'No';
  
  // Step 2: Approval Entities
  approvalEntities: ApprovalEntity[];
  
  // Step 3: Trade-in Details
  hasTradeIn: boolean;
  tradeInMake?: string;
  tradeInModel?: string;
  tradeInYear?: string;
  tradeInTrim?: string;
  tradeInVin?: string;
  tradeInMileage?: string;
  tradeInColor?: string;
  tradeInAllowance?: string;
  tradeInFcv?: string;
  tradeInLoan?: string;
  
  // Step 4: Vehicle Selection
  selectedVehicles: number[];
  
  // Step 5: Deal Summary
  dealNotes: string;
  
  // Enhanced Notes System
  notes: DealNote[];
}

// ===== UTILITY TYPES =====

/**
 * Sort Direction - Table sorting direction
 */
export type SortDirection = 'asc' | 'desc';

/**
 * Filter Operator - Table filtering operators
 */
export type FilterOperator = 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan';

/**
 * Table Filter - Individual filter configuration
 */
export interface TableFilter {
  key: string;
  operator: FilterOperator;
  value: any;
}

/**
 * Export Format - Supported export formats
 */
export type ExportFormat = 'csv' | 'excel' | 'pdf';

/**
 * Status Colors - Consistent status color mapping
 */
export const STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  approved: 'bg-green-100 text-green-800 border-green-200', 
  rejected: 'bg-red-100 text-red-800 border-red-200',
  completed: 'bg-blue-100 text-blue-800 border-blue-200',
  active: 'bg-green-100 text-green-800 border-green-200',
  inactive: 'bg-gray-100 text-gray-800 border-gray-200',
  available: 'bg-green-100 text-green-800 border-green-200',
  reserved: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  sold: 'bg-blue-100 text-blue-800 border-blue-200',
  maintenance: 'bg-orange-100 text-orange-800 border-orange-200'
} as const;

/**
 * Province Options - Canadian provinces and territories
 */
export const CANADIAN_PROVINCES = [
  { value: 'AB', label: 'Alberta' },
  { value: 'BC', label: 'British Columbia' },
  { value: 'MB', label: 'Manitoba' },
  { value: 'NB', label: 'New Brunswick' },
  { value: 'NL', label: 'Newfoundland and Labrador' },
  { value: 'NS', label: 'Nova Scotia' },
  { value: 'ON', label: 'Ontario' },
  { value: 'PE', label: 'Prince Edward Island' },
  { value: 'QC', label: 'Quebec' },
  { value: 'SK', label: 'Saskatchewan' },
  { value: 'NT', label: 'Northwest Territories' },
  { value: 'NU', label: 'Nunavut' },
  { value: 'YT', label: 'Yukon' }
] as const;

/**
 * Relationship Types - Approval entity relationships
 */
export const RELATIONSHIP_TYPES = [
  'Primary',
  'Co-signer', 
  'Guarantor',
  'Joint',
  'Spouse',
  'Parent',
  'Business Partner'
] as const;

/**
 * Employment Status Options - For approval entities
 */
export const EMPLOYMENT_STATUS_OPTIONS = [
  'Employed',
  'Self-employed', 
  'Unemployed',
  'Retired',
  'Student'
] as const;

/**
 * Lead Source Options - Customer acquisition channels
 */
export const LEAD_SOURCE_OPTIONS = [
  'Website',
  'Referral',
  'Social Media',
  'Trade Show', 
  'Google Ads',
  'Email Campaign',
  'Walk-in',
  'B2B Sales'
] as const;

/**
 * ID Type Options - Valid identification document types
 */
export const ID_TYPE_OPTIONS = [
  'Driver\'s License',
  'Passport',
  'Health Card',
  'Social Insurance Number Card',
  'Birth Certificate',
  'Permanent Resident Card',
  'Citizenship Certificate',
  'Other Government ID'
] as const;

// ===== RE-EXPORTS FOR CONVENIENCE =====
export type { 
  ApprovalEntity as Approval,
  TradeInVehicle as TradeIn,
  FinancingDetails as Financing,
  DashboardStats as Stats,
  InventoryStats as Inventory
};