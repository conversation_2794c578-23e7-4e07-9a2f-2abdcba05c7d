/**
 * String Utility Functions - SubPrime Pro
 * 
 * Safe string manipulation functions with proper null/undefined handling
 * to prevent charAt and other string method errors.
 */

/**
 * Safely capitalizes the first letter of a string
 * @param str - String to capitalize
 * @returns Capitalized string or empty string if input is invalid
 */
export function capitalize(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') {
    return '';
  }
  
  if (str.length === 0) {
    return '';
  }
  
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Safely capitalizes each word in a string
 * @param str - String to capitalize
 * @returns String with each word capitalized
 */
export function capitalizeWords(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') {
    return '';
  }
  
  return str
    .split(' ')
    .map(word => capitalize(word))
    .join(' ');
}

/**
 * Safely formats a string for display
 * @param str - String to format
 * @param fallback - Fallback value if string is invalid
 * @returns Formatted string or fallback
 */
export function safeString(str: string | undefined | null, fallback: string = ''): string {
  if (!str || typeof str !== 'string') {
    return fallback;
  }
  
  return str.trim();
}

/**
 * Safely gets the first character of a string
 * @param str - Input string
 * @returns First character or empty string
 */
export function safeFirstChar(str: string | undefined | null): string {
  if (!str || typeof str !== 'string' || str.length === 0) {
    return '';
  }
  
  return str.charAt(0);
}

/**
 * Safely truncates a string to a specified length
 * @param str - String to truncate
 * @param maxLength - Maximum length
 * @param suffix - Suffix to add when truncated
 * @returns Truncated string
 */
export function safeTruncate(
  str: string | undefined | null, 
  maxLength: number, 
  suffix: string = '...'
): string {
  if (!str || typeof str !== 'string') {
    return '';
  }
  
  if (str.length <= maxLength) {
    return str;
  }
  
  return str.slice(0, maxLength - suffix.length) + suffix;
}

/**
 * Safely formats a name for display
 * @param firstName - First name
 * @param lastName - Last name
 * @returns Formatted full name
 */
export function formatName(
  firstName: string | undefined | null, 
  lastName: string | undefined | null
): string {
  const first = safeString(firstName);
  const last = safeString(lastName);
  
  if (!first && !last) {
    return 'Unknown';
  }
  
  if (!first) {
    return capitalize(last);
  }
  
  if (!last) {
    return capitalize(first);
  }
  
  return `${capitalize(first)} ${capitalize(last)}`;
}

/**
 * Safely extracts initials from a name
 * @param firstName - First name
 * @param lastName - Last name
 * @returns Formatted initials
 */
export function getInitials(
  firstName: string | undefined | null, 
  lastName: string | undefined | null
): string {
  const first = safeFirstChar(firstName);
  const last = safeFirstChar(lastName);
  
  if (!first && !last) {
    return '??';
  }
  
  if (!first) {
    return last.toUpperCase();
  }
  
  if (!last) {
    return first.toUpperCase();
  }
  
  return `${first.toUpperCase()}${last.toUpperCase()}`;
}

/**
 * Safely formats phone numbers
 * @param phone - Phone number string
 * @returns Formatted phone number
 */
export function formatPhone(phone: string | undefined | null): string {
  const cleanPhone = safeString(phone).replace(/\D/g, '');
  
  if (cleanPhone.length === 10) {
    return `(${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
  }
  
  if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
    return `+1 (${cleanPhone.slice(1, 4)}) ${cleanPhone.slice(4, 7)}-${cleanPhone.slice(7)}`;
  }
  
  return safeString(phone, 'N/A');
}

/**
 * Safely formats email addresses
 * @param email - Email address
 * @returns Formatted email or fallback
 */
export function formatEmail(email: string | undefined | null): string {
  const cleanEmail = safeString(email);
  
  if (!cleanEmail) {
    return 'N/A';
  }
  
  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(cleanEmail)) {
    return cleanEmail; // Return as-is if invalid format
  }
  
  return cleanEmail.toLowerCase();
}

/**
 * Safely formats currency values
 * @param value - Numeric or string value
 * @param currency - Currency symbol
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: string | number | undefined | null, 
  currency: string = '$'
): string {
  if (value === undefined || value === null) {
    return 'N/A';
  }
  
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^\d.-]/g, '')) : value;
  
  if (isNaN(numValue)) {
    return 'N/A';
  }
  
  return `${currency}${numValue.toLocaleString('en-CA', { 
    minimumFractionDigits: 0, 
    maximumFractionDigits: 0 
  })}`;
}