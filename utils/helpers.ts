import { Customer, Vehicle, Deal, DealsClosedDataPoint } from '../types';

/**
 * Utility Functions for SubPrime Pro
 * 
 * Helper functions for data processing, filtering, and UI state management.
 */

export const getStatusColor = (status: string): string => {
  const statusMap: Record<string, string> = {
    'available': 'border-green-500 text-green-500 font-medium px-3 py-1.5',
    'reserved': 'border-yellow-500 text-yellow-500 font-medium px-3 py-1.5',
    'sold': 'border-gray-500 text-gray-500 font-medium px-3 py-1.5',
    'active': 'border-green-500 text-green-500 font-medium px-3 py-1.5',
    'prospect': 'border-blue-500 text-blue-500 font-medium px-3 py-1.5',
    'inactive': 'border-gray-500 text-gray-500 font-medium px-3 py-1.5',
    'vip': 'border-purple-500 text-purple-500 font-medium px-3 py-1.5',
    'corporate': 'border-brand-orange text-brand-orange font-medium px-3 py-1.5',
    'pending': 'border-blue-500 text-blue-500 font-medium px-3 py-1.5',
    'in_progress': 'border-yellow-500 text-yellow-500 font-medium px-3 py-1.5',
    'finalized': 'border-green-500 text-green-500 font-medium px-3 py-1.5',
    'cancelled': 'border-red-500 text-red-500 font-medium px-3 py-1.5'
  };
  
  return statusMap[status.toLowerCase()] || 'border-gray-500 text-gray-500 font-medium px-3 py-1.5';
};

export const getCreditColor = (rating: string): string => {
  const ratingMap: Record<string, string> = {
    'A+': 'text-green-700',
    'A': 'text-green-600',
    'A-': 'text-green-500',
    'B+': 'text-yellow-600',
    'B': 'text-yellow-500'
  };
  
  return ratingMap[rating] || 'text-gray-500';
};

export const getDaysOnLotColor = (days: number): string => {
  if (days <= 14) return 'text-green-600';
  if (days <= 30) return 'text-yellow-600';
  return 'text-red-600';
};

/**
 * Data Filtering Functions
 */
export const filterCustomers = (
  customers: Customer[],
  customerFilter: string,
  customerSearch: string
): Customer[] => {
  return customers.filter(customer => {
    // Filter by status/type
    let statusMatch = true;
    if (customerFilter === 'individual') statusMatch = customer.customerType === 'Individual';
    else if (customerFilter === 'business') statusMatch = customer.customerType === 'Business';
    else if (customerFilter === 'vip') statusMatch = customer.status === 'VIP' || customer.status === 'Corporate';
    else if (customerFilter !== 'all') statusMatch = customer.status.toLowerCase() === customerFilter;

    // Filter by search
    const searchMatch = customerSearch === '' || 
      customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
      customer.email.toLowerCase().includes(customerSearch.toLowerCase()) ||
      customer.phone.includes(customerSearch) ||
      customer.assignedDealer.toLowerCase().includes(customerSearch.toLowerCase()) ||
      customer.salesRep.toLowerCase().includes(customerSearch.toLowerCase());

    return statusMatch && searchMatch;
  });
};

export const filterInventory = (
  inventory: Vehicle[],
  inventoryFilter: string,
  inventorySearch: string
): Vehicle[] => {
  return inventory.filter(vehicle => {
    // Status filtering removed since status is not fetched by system
    // All vehicles are now included regardless of status
    
    // Filter by search only
    const searchMatch = inventorySearch === '' ||
      `${vehicle.year} ${vehicle.make} ${vehicle.model}`.toLowerCase().includes(inventorySearch.toLowerCase()) ||
      vehicle.vin.toLowerCase().includes(inventorySearch.toLowerCase()) ||
      vehicle.location.toLowerCase().includes(inventorySearch.toLowerCase()) ||
      vehicle.exterior.toLowerCase().includes(inventorySearch.toLowerCase());

    return searchMatch;
  });
};

/**
 * Helper Functions for Matching
 */
export const getVehicleMatchCount = (customerId: string, deals: Deal[]): number => {
  return deals.filter(deal => deal.customerId === customerId).reduce((total, deal) => {
    return total + (deal.vehicleOptions ? deal.vehicleOptions.length : 0);
  }, 0);
};

export const getCustomerMatchCount = (vehicleId: number, deals: Deal[]): number => {
  return deals.filter(deal => 
    deal.vehicleOptions && deal.vehicleOptions.some(vehicle => vehicle.id === vehicleId)
  ).length;
};

export const getReservedCustomer = (vehicleId: number, deals: Deal[], customers: Customer[]): Customer | null => {
  const deal = deals.find(deal => deal.selectedVehicle === vehicleId);
  if (deal) {
    return customers.find(customer => customer.id === deal.customerId) || null;
  }
  return null;
};

/**
 * Computed Values
 */
export const computeDealsMetrics = (dealsClosedData: DealsClosedDataPoint[]) => {
  const totalDealsThisMonth = dealsClosedData.reduce((sum, day) => sum + day.deals, 0);
  const avgDealsPerDay = (totalDealsThisMonth / dealsClosedData.length).toFixed(1);
  
  return { totalDealsThisMonth, avgDealsPerDay };
};

/**
 * Deal Creation Helpers
 */
export const createDealOpportunities = (
  vehicle: Vehicle,
  customers: Customer[],
  selectedCustomerIds: string[]
) => {
  const matchedCustomers = customers.filter(c => selectedCustomerIds.includes(c.id));
  
  return matchedCustomers.map(customer => ({
    id: `DEAL-${Date.now()}-${customer.id}`,
    type: 'deal' as const,
    vehicleId: vehicle.id,
    vehicle: `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
    vehiclePrice: vehicle.price,
    vehicleLocation: vehicle.location,
    customerId: customer.id,
    customerName: customer.name,
    customerPhoto: customer.photo,
    customerType: customer.customerType,
    customerEmail: customer.email,
    customerPhone: customer.phone,
    customerLocation: `${customer.city}, ${customer.province}`,
    vehicleOptions: [{
      id: vehicle.id,
      details: `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
      price: vehicle.price,
      location: vehicle.location,
      vin: vehicle.vin,
      photos: vehicle.photos,
      status: vehicle.status
    }],
    status: 'Pending' as const,
    priority: (customer.status === 'VIP' || customer.status === 'Corporate') ? 'High' as const : 'Medium' as const,
    salesRep: customer.salesRep,
    createdDate: new Date().toISOString().split('T')[0],
    lastContact: null,
    selectedVehicle: null,
    notes: `Deal created for ${customer.name} - interested in ${vehicle.year} ${vehicle.make} ${vehicle.model}`
  }));
};

export const createVehicleDeal = (
  customer: Customer,
  vehicles: Vehicle[],
  selectedVehicleIds: number[]
): Deal => {
  const matchedVehicles = vehicles.filter(v => selectedVehicleIds.includes(v.id));
  
  return {
    id: `DEAL-${Date.now()}-${customer.id}`,
    type: 'deal',
    customerId: customer.id,
    customerName: customer.name,
    customerPhoto: customer.photo,
    customerType: customer.customerType,
    customerEmail: customer.email,
    customerPhone: customer.phone,
    customerLocation: `${customer.city}, ${customer.province}`,
    vehicleOptions: matchedVehicles.map(vehicle => ({
      id: vehicle.id,
      details: `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
      price: vehicle.price,
      location: vehicle.location,
      vin: vehicle.vin,
      photos: vehicle.photos,
      status: vehicle.status
    })),
    status: 'Pending',
    priority: (customer.status === 'VIP' || customer.status === 'Corporate') ? 'High' : 'Medium',
    salesRep: customer.salesRep,
    createdDate: new Date().toISOString().split('T')[0],
    lastContact: null,
    selectedVehicle: null,
    notes: `Deal created with ${matchedVehicles.length} vehicle options`
  };
};

/**
 * Form Submission Helpers
 */
export const generateCustomerId = (currentCount: number): string => {
  return `CU${String(currentCount + 1).padStart(3, '0')}`;
};

export const generateDealId = (): string => {
  return `DEAL-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};