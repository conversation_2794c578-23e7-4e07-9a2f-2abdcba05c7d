/**
 * SubPrime Pro - Deal Builder Focus
 * 
 * Concept & Design by <PERSON><PERSON><PERSON><PERSON>
 * 
 * Streamlined deal builder application with 5-step accordion workflow
 * Optimized for 1366x768 resolution with Material Design 3
 * 
 * Core Features:
 * - Deal creation form with vertical accordion layout
 * - Deal management table
 * - Deal details view
 * - Step-level notes system
 * - Canadian auto credit logic
 * 
 * Built by Figma Make - Claude Sonnet
 */

import { useState } from 'react';
import { Deal, DealNote } from './types';
import { locations } from './data/mockData';
import { mockDeals } from './data/mockDeals';

// UI Components
import { TooltipProvider } from './components/ui/tooltip';
import { Toaster } from './components/ui/sonner';
import { Button } from './components/ui/button';
import { Badge } from './components/ui/badge';

// Deal Builder Components
import { AddDealForm } from './components/AddDealForm';
import { NotesSlider } from './components/NotesSlider';
import { BrandIcon } from './components/BrandIcon';
import { DealsBuiltTable } from './components/DealsBuiltTable';
import { DealDetailsView } from './components/DealDetailsView';

import { ArrowLeft } from 'lucide-react';
import { toast } from 'sonner@2.0.3';

type ViewType = 'main' | 'builder' | 'deal-details';

// Mock vehicle data that matches AccordionDealForm
const availableVehicles = [
  { 
    id: 1, make: 'Toyota', model: 'Camry', year: 2024, price: '$32,999',
    location: 'Toronto Downtown', vin: '1HGCM82633A123456', mileage: '12,450 km',
    exterior: 'Midnight Black', interior: 'Black Leather', transmission: 'CVT', fuelType: 'Hybrid',
    daysOnLot: 23, cost: '$28,500', 
    photos: ['https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=400&h=300&fit=crop'],
    features: ['Navigation', 'Backup Camera', 'Heated Seats']
  },
  { 
    id: 2, make: 'Honda', model: 'Civic', year: 2024, price: '$28,499',
    location: 'Mississauga', vin: '2HGCM82633A123457', mileage: '8,750 km',
    exterior: 'Pearl White', interior: 'Black Cloth', transmission: 'CVT', fuelType: 'Gasoline',
    daysOnLot: 15, cost: '$24,800', 
    photos: ['https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop'],
    features: ['Honda Sensing', 'Sunroof', 'Wireless Charging']
  },
  { 
    id: 3, make: 'Ford', model: 'F-150', year: 2023, price: '$45,999',
    location: 'Vancouver', vin: '3HGCM82633A123458', mileage: '25,100 km',
    exterior: 'Agate Black', interior: 'Medium Earth Gray', transmission: '10-Speed Automatic', fuelType: 'Gasoline',
    daysOnLot: 41, cost: '$41,200', 
    photos: ['https://images.unsplash.com/photo-1563720223185-11003d516935?w=400&h=300&fit=crop'],
    features: ['4WD', 'Tow Package', 'B&O Sound']
  },
  { 
    id: 4, make: 'BMW', model: 'X3', year: 2024, price: '$52,999',
    location: 'Toronto Downtown', vin: '6HGCM82633A123461', mileage: '15,200 km',
    exterior: 'Alpine White', interior: 'Black SensaTec', transmission: '8-Speed Automatic', fuelType: 'Gasoline',
    daysOnLot: 28, cost: '$47,500', 
    photos: ['https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=300&fit=crop'],
    features: ['xDrive AWD', 'Panoramic Roof', 'Harman Kardon Audio']
  },
  { 
    id: 5, make: 'Audi', model: 'Q5', year: 2024, price: '$48,999',
    location: 'Vancouver', vin: '7HGCM82633A123462', mileage: '9,800 km',
    exterior: 'Quantum Gray', interior: 'Black Leather', transmission: '7-Speed S tronic', fuelType: 'Gasoline',
    daysOnLot: 19, cost: '$43,800', 
    photos: ['https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop'],
    features: ['quattro AWD', 'Virtual Cockpit', 'Bang & Olufsen Audio']
  }
];

export default function App() {
  // State Management
  const [deals, setDeals] = useState<Deal[]>(mockDeals);
  const [currentView, setCurrentView] = useState<ViewType>('main');
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Notes System State
  const [notes, setNotes] = useState<DealNote[]>([]);
  const [isNotesOpen, setIsNotesOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  // Navigation Handlers
  const handleStartDealBuilder = (): void => {
    setCurrentView('builder');
    setSelectedDeal(null);
    setNotes([]);
    setCurrentStep(1);
    toast.success('Deal Builder Started', {
      description: 'Begin creating your new deal with our step-by-step process.',
      duration: 3000,
    });
  };

  const handleBackToMain = (): void => {
    setCurrentView('main');
    setSelectedDeal(null);
    setNotes([]);
    setCurrentStep(1);
  };

  const handleViewDeal = (deal: Deal): void => {
    setSelectedDeal(deal);
    setCurrentView('deal-details');
    setNotes(deal.notes || []);
    toast.info('Deal Details', {
      description: `Viewing details for deal ${deal.id}`,
      duration: 2000,
    });
  };

  // Notes System Handlers
  const handleNotesToggle = (): void => {
    setIsNotesOpen(!isNotesOpen);
  };

  const handleNotesChange = (updatedNotes: DealNote[]): void => {
    setNotes(updatedNotes);
  };

  const handleCurrentStepChange = (step: number): void => {
    setCurrentStep(step);
  };

  // Enhanced Deal Submission Handler with Complete Data Capture
  const handleDealSubmit = async (dealData: any): Promise<void> => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Get selected vehicle details
      const selectedVehicleDetails = availableVehicles.filter(v => 
        dealData.selectedVehicles?.includes(v.id)
      );
      
      const selectedVehicle = selectedVehicleDetails[0];
      
      // Calculate transaction values (same logic as AccordionDealForm)
      const calculateDealValue = () => {
        if (!selectedVehicle) return 0;
        
        const cashPrice = parseFloat(selectedVehicle.price.replace(/[$,]/g, '')) || 0;
        const tradeInValue = parseFloat((dealData.tradeInAllowance || '0').replace(/[$,]/g, '')) || 0;
        
        // Tax calculations based on province
        const pstRate = dealData.province === 'BC' ? 0.07 : dealData.province === 'ON' ? 0.08 : 0.05;
        const gstRate = 0.05;
        
        const taxableAmount = cashPrice - tradeInValue;
        const pstAmount = taxableAmount * pstRate;
        const gstAmount = taxableAmount * gstRate;
        const totalTaxes = pstAmount + gstAmount;
        
        // Standard fees
        const ppsa = 100.51;
        const registrationFee = 38.50;
        const adminFee = 598.43;
        const totalFees = ppsa + registrationFee + adminFee;
        
        const netSellingPrice = cashPrice + totalTaxes + totalFees - tradeInValue;
        
        return netSellingPrice;
      };
      
      // Create comprehensive vehicle information string
      const vehicleInfo = selectedVehicle 
        ? `${selectedVehicle.year} ${selectedVehicle.make} ${selectedVehicle.model} - ${selectedVehicle.exterior} - VIN: ${selectedVehicle.vin}`
        : 'No Vehicle Selected';
      
      // Create comprehensive customer address
      const fullAddress = [
        dealData.suiteNumber && `Unit ${dealData.suiteNumber}`,
        dealData.address,
        dealData.streetName,
        dealData.city,
        dealData.province
      ].filter(Boolean).join(', ');
      
      // Create trade-in vehicle object if applicable
      const tradeInVehicle = dealData.hasTradeIn ? {
        make: dealData.tradeInMake || '',
        model: dealData.tradeInModel || '',
        year: parseInt(dealData.tradeInYear) || 0,
        trim: dealData.tradeInTrim,
        vin: dealData.tradeInVin,
        mileage: dealData.tradeInMileage || '',
        color: dealData.tradeInColor || '',
        condition: 'Good' as const,
        tradeAllowance: parseFloat((dealData.tradeInAllowance || '0').replace(/[$,]/g, '')) || 0,
        fairCashValue: parseFloat((dealData.tradeInFcv || '0').replace(/[$,]/g, '')) || 0,
        outstandingLoan: parseFloat((dealData.tradeInLoan || '0').replace(/[$,]/g, '')) || 0,
        notes: `Trade-in processed on ${new Date().toLocaleDateString()}`
      } : null;
      
      // Create financing details
      const financingDetails = {
        lender: dealData.approvalEntities?.[0]?.lenderName || 'To Be Determined',
        loanAmount: calculateDealValue(),
        downPayment: parseFloat((dealData.approvalEntities?.[0]?.downPayment || '0').replace(/[$,]/g, '')) || 0,
        interestRate: parseFloat(dealData.approvalEntities?.[0]?.interestRate || '16.49') || 16.49,
        termMonths: parseInt(dealData.approvalEntities?.[0]?.termLength || '84') || 84,
        monthlyPayment: parseFloat((dealData.approvalEntities?.[0]?.monthlyPayment || '0').replace(/[$,]/g, '')) || 0,
        financingType: 'Dealership' as const,
        approvalStatus: 'Pending' as const,
        conditions: [
          'Subject to final credit approval',
          'Vehicle inspection required',
          'Insurance verification required'
        ]
      };
      
      // Create new comprehensive deal object
      const newDeal: Deal = {
        id: `deal-${Date.now()}`,
        customerName: `${dealData.firstName} ${dealData.lastName}`.trim() || 'New Customer',
        vehicleInfo: vehicleInfo,
        dealValue: calculateDealValue(),
        status: 'pending',
        location: selectedVehicle?.location || dealData.location || locations[0]?.name || 'Main Location',
        createdDate: new Date().toISOString().split('T')[0],
        approvalEntities: dealData.approvalEntities || [],
        tradeInVehicle: tradeInVehicle,
        financingDetails: financingDetails,
        notes: notes,
        
        // Additional comprehensive deal data for future use
        ...dealData,
        
        // Customer information summary
        customerDetails: {
          firstName: dealData.firstName,
          lastName: dealData.lastName,
          dateOfBirth: dealData.dateOfBirth,
          phoneNumber: dealData.phoneNumber,
          emailAddress: dealData.emailAddress,
          fullAddress: fullAddress,
          province: dealData.province,
          city: dealData.city,
          customerPhoto: dealData.customerPhoto,
          idType: dealData.idType,
          status: dealData.status,
          idImageUpload: dealData.idImageUpload
        },
        
        // Vehicle details
        selectedVehicleDetails: selectedVehicleDetails,
        
        // Transaction summary
        transactionSummary: {
          cashPrice: selectedVehicle ? parseFloat(selectedVehicle.price.replace(/[$,]/g, '')) : 0,
          tradeInAllowance: parseFloat((dealData.tradeInAllowance || '0').replace(/[$,]/g, '')) || 0,
          totalDealValue: calculateDealValue(),
          province: dealData.province,
          hasTradeIn: dealData.hasTradeIn,
          approvalCount: dealData.approvalEntities?.length || 0
        },
        
        // Deal notes
        dealNotes: dealData.dealNotes || '',
        
        // Metadata
        createdBy: 'SubPrime Pro System',
        lastModified: new Date().toISOString(),
        dealType: dealData.hasTradeIn ? 'Purchase with Trade-in' : 'Purchase',
        priority: dealData.approvalEntities?.length > 1 ? 'High' : 'Normal'
      };

      setDeals(prev => [newDeal, ...prev]);

      // Enhanced success message with deal details
      toast.success('Deal Created Successfully!', {
        description: `Deal #${newDeal.id.slice(-6)} for ${newDeal.customerName} - ${vehicleInfo.split(' - ')[0]} - Value: $${newDeal.dealValue.toLocaleString()}`,
        duration: 5000,
      });

      // Reset to main view
      setCurrentView('main');
      setNotes([]);
      setCurrentStep(1);
      
    } catch (error) {
      console.error('Error creating deal:', error);
      toast.error('Failed to Create Deal', {
        description: 'An error occurred while creating the deal. Please try again.',
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Unified Header Component
  const UnifiedHeader = () => {
    const getViewConfig = () => {
      switch (currentView) {
        case 'main':
          return {
            title: 'SubPrime Pro',
            subtitle: 'Deal Management System',
            showBack: false,
            rightElement: null
          };
        case 'builder':
          return {
            title: 'Deal Builder',
            subtitle: 'Step-by-step deal creation process',
            showBack: true,
            rightElement: (
              <Badge variant="outline" className="px-3 py-1">
                Step {currentStep} of 5
              </Badge>
            )
          };
        case 'deal-details':
          return {
            title: 'Deal Details',
            subtitle: selectedDeal ? `${selectedDeal.customerName} - Deal #${selectedDeal.id.slice(-6)}` : 'Deal information',
            showBack: true,
            rightElement: selectedDeal ? (
              <Badge variant="outline" className="px-3 py-1">
                {selectedDeal.status.charAt(0).toUpperCase() + selectedDeal.status.slice(1)}
              </Badge>
            ) : null
          };
        default:
          return {
            title: 'SubPrime Pro',
            subtitle: '',
            showBack: false,
            rightElement: null
          };
      }
    };

    const config = getViewConfig();

    return (
      <div className="bg-card border-b border-outline-variant">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {config.showBack && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToMain}
                    className="text-muted-foreground hover:text-foreground"
                    disabled={isSubmitting}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                  <div className="h-6 w-px bg-border" />
                </>
              )}
              
              <div className="flex items-center space-x-3">
                <BrandIcon className="h-8 w-auto" />
                <div>
                  <h1 className="text-2xl font-bold text-on-surface">{config.title}</h1>
                  {config.subtitle && (
                    <p className="text-sm text-muted-foreground">{config.subtitle}</p>
                  )}
                </div>
              </div>
            </div>

            {config.rightElement && (
              <div className="flex items-center">
                {config.rightElement}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-background">
        <a href="#main-content" className="skip-link">Skip to main content</a>
        
        <UnifiedHeader />

        <main id="main-content" className="min-h-screen">
          {currentView === 'main' && (
            <div className="bg-gradient-to-br from-primary-99 to-primary-95 px-6 py-6">
              <DealsBuiltTable 
                deals={deals} 
                onStartNewDeal={handleStartDealBuilder}
                onViewDeal={handleViewDeal}
                isSubmitting={isSubmitting}
              />
            </div>
          )}

          {currentView === 'builder' && (
            <div className="p-4">
              <AddDealForm
                onBack={handleBackToMain}
                onSubmit={handleDealSubmit}
                locations={locations}
                isSubmitting={isSubmitting}
                notes={notes}
                onNotesChange={handleNotesChange}
                currentStep={currentStep}
                onCurrentStepChange={handleCurrentStepChange}
                onNotesToggle={handleNotesToggle}
              />
            </div>
          )}

          {currentView === 'deal-details' && selectedDeal && (
            <div className="p-4">
              <DealDetailsView
                deal={selectedDeal}
              />
            </div>
          )}
        </main>

        {/* Notes Panel - Only show in builder view */}
        {currentView === 'builder' && (
          <NotesSlider
            isOpen={isNotesOpen}
            onClose={() => setIsNotesOpen(false)}
            notes={notes}
            onNotesChange={handleNotesChange}
            currentStep={currentStep}
            disabled={isSubmitting}
          />
        )}

        <Toaster 
          position="bottom-right"
          closeButton
          richColors
          expand={false}
          visibleToasts={2}
        />
      </div>
    </TooltipProvider>
  );
}