# SubPrime Pro - Comprehensive Developer Guide

**Vision and Design by <PERSON><PERSON><PERSON><PERSON> | Built by Figma Make - Claude <PERSON>**

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture & Design System](#architecture--design-system)
3. [Component System](#component-system)
4. [Features & Functionality](#features--functionality)
5. [Material Design 3 Integration](#material-design-3-integration)
6. [Canadian Auto Finance Logic](#canadian-auto-finance-logic)
7. [Button Hierarchy & UI Standards](#button-hierarchy--ui-standards)
8. [State Management](#state-management)
9. [Data Flow & TypeScript](#data-flow--typescript)
10. [Development Standards](#development-standards)
11. [Performance Optimizations](#performance-optimizations)
12. [Accessibility Features](#accessibility-features)

---

## Project Overview

SubPrime Pro is a comprehensive dealership management system designed specifically for Canadian auto dealership chains. The application provides complete customer relationship management, inventory tracking, multi-approval entity deal creation, and business analytics.

### Core Technologies
- **React 18** with TypeScript for type safety and modern development
- **Tailwind CSS v4** for utility-first styling with custom brand theming
- **Material Design 3** component system for modern, accessible UI
- **shadcn/ui** component library as foundation with custom enhancements
- **Recharts** for business analytics and data visualization
- **Sonner** for toast notifications with enhanced user feedback

### Design Philosophy
- **Canadian-First**: Built specifically for Canadian auto finance regulations and practices
- **Material Design 3**: Modern, accessible design language with tonal color system
- **Orange Brand Identity**: Primary orange (#f16700) with strict button hierarchy
- **Accessibility**: WCAG AA compliance with proper contrast ratios and focus management
- **Modular Architecture**: Separation of concerns with reusable components

---

## Architecture & Design System

### Color System (Material Design 3 Tonal Palettes)

```css
/* Primary Palette - Orange Brand */
--primary-0: #000000    /* Pure black */
--primary-10: #3c1d01   /* Very dark brown */
--primary-20: #b44600   /* Dark orange - Active nav states */
--primary-30: #ca5200   /* Medium-dark orange */
--primary-40: #da5a00   /* Medium orange */
--primary-50: #f16700   /* Brand orange - Primary CTAs ONLY */
--primary-60: #f37a35   /* Light orange */
--primary-70: #f6af8b   /* Very light orange */
--primary-80: #facfbb   /* Pale orange */
--primary-90: #ffdecd   /* Very pale orange */
--primary-95: #ffeee5  /* Near white orange */
```

### Button Hierarchy (STRICT ENFORCEMENT)

1. **Primary CTA (Orange Filled)**: ONE per page maximum
   - Reserved for main actions: "Add Vehicle", "Build New Deal", "Create Deal"
   - Uses `bg-primary-50` (#f16700)
   - Exception: Floating "Build New Deal" button (always primary)

2. **Secondary Buttons (Orange Outline)**: All other actions
   - White background with orange border/text
   - Uses `border-primary-50 text-primary-50`
   - Examples: "Edit", "Export", "Cancel"

3. **Active Navigation States**: Dark orange
   - Uses `bg-primary-20` (#b44600) - Darkest orange for subtle active states
   - Maintains readability while showing active state

### Typography System

- **Font Family**: Montserrat throughout (Display, Heading, Body)
- **Minimum Font Size**: 14px enforced globally
- **Font Weights**: 300 (Light), 400 (Normal), 500 (Medium), 600 (Semibold), 700 (Bold)
- **Line Heights**: 1.25 (Tight), 1.5 (Normal), 1.625 (Relaxed)

---

## Component System

### Enhanced Material Table (`EnhancedMaterialTable`)

**Location**: `/components/ui/material/table.tsx`

**Features**:
- Column-based sorting with visual indicators
- Global search with real-time filtering
- Advanced column-specific filtering
- Row selection with bulk actions
- Pagination with customizable page sizes
- Export functionality (CSV, JSON)
- Responsive design with mobile optimization
- Dark theme support

**Usage**:
```tsx
<EnhancedMaterialTable
  data={filteredInventory}
  columns={inventoryColumns}
  rowKey="id"
  selectable={true}
  sortable={true}
  filterable={true}
  searchable={true}
  paginated={true}
  pageSize={12}
  pageSizeOptions={[10, 12, 20, 50]}
  exportable={true}
  onRowClick={handleVehicleClick}
  bulkActions={handleBulkActions}
  containerClassName="h-[calc(100vh-16rem)]"
/>
```

### Approval Entity Manager (`ApprovalEntityManager`)

**Location**: `/components/ApprovalEntityManager.tsx`

**Features**:
- Multi-entity support: Primary Applicant, Co-Applicant, Co-Signer, Guarantor
- Canadian-specific fields: SIN, provinces, employment status
- Credit assessment with visual scoring
- Bankruptcy history tracking
- Form validation with Material Design 3 components
- Read-only mode for deal summaries

**Entity Types**:
- **Primary Applicant**: Main borrower (required)
- **Co-Applicant**: Joint applicant with equal responsibility
- **Co-Signer**: Backup guarantor for payment
- **Guarantor**: Asset-backed guarantee

### Step Tracker (`StepTracker`)

**Location**: `/components/StepTracker.tsx`

**Features**:
- Accessible navigation with keyboard support
- Visual state management: Pending, Active, Complete
- Pale orange hover effects with blur
- Green completion states with checkmarks
- Progress line spanning from first to last step
- Consistent dark grey styling for pending states

**Implementation**:
```tsx
const steps: Step[] = [
  {
    id: 'customer-info',
    title: 'Customer Info',
    subtitle: 'Step 1',
    icon: User,
    state: getStepState(1),
    stepNumber: 1
  }
  // ... more steps
];

<StepTracker steps={steps} onStepClick={handleStepClick} />
```

### Deal Builder Form (`AddDealForm`)

**Location**: `/components/AddDealForm.tsx`

**5-Step Process**:
1. **Customer Information**: Basic contact details and demographics
2. **Approval Entities**: Multi-applicant credit assessment
3. **Trade-in Details**: Optional vehicle trade-in with valuation
4. **Vehicle Selection**: Multi-vehicle selection from inventory
5. **Deal Summary**: Read-only preview with all entities and vehicles

### Material Design 3 Components

**Location**: `/components/ui/material/`

**Components**:
- `TextField`: Floating label text inputs with validation
- `SelectField`: Dropdown with Material styling and focus states
- `Checkbox`: Material checkbox with proper ripple effects
- `Button`: Consistent button styling with brand hierarchy
- `Textarea`: Multi-line text input with floating labels
- `Label`: Accessible labels with proper contrast

---

## Features & Functionality

### Dashboard Analytics

**Comprehensive Business Intelligence**:
- Real-time dealership metrics (5-card layout)
- Inventory snapshot with trend analysis
- Top 5 vehicle models with percentages
- Gross earnings visualization (6-month area chart)
- Deal closure tracking (30-day bar chart)
- Recent sales table with location breakdown

### Customer Management

**Full Lifecycle Management**:
- Comprehensive customer profiles with Canadian addresses
- Credit rating tracking and history
- Lead source attribution and ROI analysis
- Purchase history and lifetime value calculation
- Multi-location customer assignment
- Bulk operations (edit, email, export)

### Inventory Management

**Multi-Location Vehicle Tracking**:
- Real-time inventory across dealership network
- Advanced filtering by make, model, year, location, status
- Vehicle matching to customer preferences
- Days on lot tracking and alerts
- Cost vs. price margin analysis
- Bulk inventory operations

### Deal Creation & Management

**Multi-Approval Entity System**:
- Support for complex approval scenarios
- Canadian credit assessment with SIN validation
- Employment verification and income tracking
- Bankruptcy history management
- Multi-vehicle deal options
- Trade-in valuation and processing

### Customer-Vehicle Matching

**Intelligent Matching System**:
- Sliding panels from right side for seamless workflow
- Visual continuity with action icons in panel headers
- Multi-customer to single vehicle matching
- Multi-vehicle to single customer matching
- Deal opportunity creation with automatic notifications

---

## Canadian Auto Finance Logic

### Regulatory Compliance

**Canadian-Specific Features**:
- **Social Insurance Number (SIN)**: Format validation and storage
- **Provincial Selection**: All 13 provinces and territories
- **Employment Verification**: Canadian employment status categories
- **Credit Scoring**: Canadian credit bureau integration ready
- **Bankruptcy Tracking**: Discharge dates and rehabilitation status

### Credit Assessment Framework

**Multi-Applicant Support**:
```typescript
interface ApprovalEntity {
  type: 'Primary Applicant' | 'Co-Applicant' | 'Co-Signer' | 'Guarantor';
  creditScore: string;
  approvalAmount: string;
  employmentStatus: 'Employed' | 'Self-Employed' | 'Unemployed' | 'Retired' | 'Student';
  annualIncome: string;
  bankruptcyHistory: boolean;
  bankruptcyDate?: string;
  // ... additional fields
}
```

### Lender Integration

**Canadian Financial Institutions**:
- Big 6 Banks (RBC, TD, BMO, Scotiabank, CIBC, National Bank)
- Credit Unions (Desjardins)
- Captive Finance (Honda Financial, Toyota Credit Canada)
- Term lengths: 24-84 months standard
- Interest rate tracking and comparison

---

## Button Hierarchy & UI Standards

### Implementation Guidelines

**Primary CTA Usage**:
```tsx
// ✅ CORRECT - One primary CTA per page
<Button className="bg-primary-50 text-white hover:bg-primary-60">
  <Plus className="h-4 w-4 mr-2" />
  Add Vehicle
</Button>

// ✅ CORRECT - All other buttons use secondary styling
<Button variant="outline" className="border-primary-50 text-primary-50">
  <Edit className="h-4 w-4 mr-2" />
  Edit
</Button>
```

**Active Navigation States**:
```tsx
// ✅ CORRECT - Subtle dark orange for active nav
<div className={cn(
  "nav-item",
  activeTab === 'dashboard' 
    ? "bg-primary-20 text-white shadow-lg"  // Dark orange for active
    : "text-neutral-95 hover:bg-brand-orange/20"
)}>
```

### Form Input Standards

**Consistent White Backgrounds**:
- All form inputs use white backgrounds (`bg-neutral-100`)
- Orange reserved for focus states and accents only
- Enhanced contrast for accessibility (WCAG AA)
- Material Design 3 floating labels

---

## State Management

### Application State Structure

```typescript
// Primary state management in App.tsx
const [activeTab, setActiveTab] = useState('dashboard');
const [currentView, setCurrentView] = useState('dashboard');
const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

// Data management
const [inventory, setInventory] = useState<Vehicle[]>(initialInventory);
const [customers, setCustomers] = useState<Customer[]>(initialCustomers);
const [deals, setDeals] = useState<Deal[]>([]);

// UI state
const [matchingPanelOpen, setMatchingPanelOpen] = useState(false);
const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
```

### Data Flow Patterns

**Event Handlers**:
- Centralized event handling in main App component
- Props drilling for component communication
- Toast notifications for user feedback
- State updates with immutable patterns

**Form Submission Flow**:
1. Form validation in component
2. Data transformation and sanitization
3. State update with optimistic UI
4. Success/error notification
5. Navigation and cleanup

---

## Development Standards

### TypeScript Best Practices

**Strict Type Safety**:
```typescript
// ✅ Comprehensive interface definitions
interface DealFormData {
  customerName: string;
  email: string;
  approvalEntities: ApprovalEntity[];
  selectedVehicles: number[];
  dealNotes: string;
}

// ✅ Proper event handler typing
const handleSubmit = (dealData: DealFormData): void => {
  // Implementation
};
```

### Component Structure

**Consistent Organization**:
```typescript
/**
 * Component documentation with purpose and features
 */

// External imports
import { useState } from 'react';

// Internal type imports
import { Customer, Vehicle } from '../types';

// Component interface
interface ComponentProps {
  // Props definition
}

// Main component with clear sections
export function Component({ props }: ComponentProps) {
  // State management
  // Event handlers
  // Computed values
  // Render logic
}
```

### Code Organization

**File Structure Standards**:
- `/components` - React components (organized by feature)
- `/components/ui` - Base UI components (shadcn/ui)
- `/components/ui/material` - Material Design 3 components
- `/types` - TypeScript interfaces and types
- `/utils` - Helper functions and utilities
- `/data` - Mock data and constants
- `/config` - Configuration files (table columns, etc.)

---

## Performance Optimizations

### React Best Practices

**Optimization Techniques**:
- Proper key props for list rendering
- Event handler memoization where appropriate
- Lazy loading for large datasets
- Efficient state updates with functional updates
- Minimal re-renders through proper dependency arrays

### CSS Optimizations

**Tailwind CSS v4**:
- Utility-first approach for minimal CSS bundle
- Custom color system with CSS variables
- Responsive design with mobile-first approach
- Dark mode support with CSS custom properties

---

## Accessibility Features

### WCAG AA Compliance

**Implementation Details**:
- Minimum 14px font size enforcement
- Enhanced contrast ratios for all text
- Focus management with visible focus indicators
- Screen reader support with proper ARIA labels
- Keyboard navigation for all interactive elements

### Material Design 3 Accessibility

**Built-in Features**:
- Proper focus states with ring indicators
- Color contrast compliance across all tonal palettes
- Touch target sizing (minimum 44px)
- Screen reader announcements for state changes
- High contrast mode support

---

## Future Development Guidelines

### Extensibility Patterns

**Adding New Features**:
1. Create TypeScript interfaces in `/types`
2. Add mock data in `/data/mockData.ts`
3. Build reusable components in `/components`
4. Integrate with main App state management
5. Follow established button hierarchy and styling

### Code Quality Standards

**Maintenance Guidelines**:
- Comprehensive JSDoc comments for complex functions
- Consistent naming conventions (camelCase, PascalCase)
- Error boundary implementation for production
- Unit testing for critical business logic
- Performance monitoring and optimization

---

## Conclusion

SubPrime Pro represents a comprehensive, Canadian-focused dealership management system built with modern web technologies and accessibility in mind. The codebase follows strict design patterns, maintains consistency through Material Design 3 principles, and provides a solid foundation for future enhancements.

The multi-approval entity system, enhanced table functionality, and integrated deal builder create a powerful platform for managing complex automotive finance scenarios while maintaining an intuitive user experience.

**Key Success Factors**:
- Strict button hierarchy enforcement
- Material Design 3 consistency
- Canadian regulatory compliance
- Comprehensive type safety
- Accessible design patterns
- Modular component architecture

For questions or clarifications on any aspect of the system, refer to the component-specific documentation or the established patterns in the existing codebase.