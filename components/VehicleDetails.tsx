import * as React from 'react';
import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Separator } from './ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from './ui/breadcrumb';
import { 
  Car, 
  MapPin, 
  Calendar, 
  Gauge, 
  Fuel, 
  Settings, 
  Shield, 
  DollarSign, 
  Clock, 
  FileText, 
  Camera, 
  Target, 
  TrendingUp,
  Edit,
  MessageSquare,
  Phone,
  Mail,
  ArrowLeft,
  Image as ImageIcon,
  CheckCircle,
  AlertTriangle,
  Info,
  Star,
  Users,
  History,
  Activity,
  Eye,
  Wrench,
  Award,
  Flag,
  ChevronLeft,
  ChevronRight,
  X,
  ZoomIn,
  Download
} from 'lucide-react';

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  price: string;
  status: string;
  location: string;
  vin: string;
  mileage: string;
  exterior: string;
  interior: string;
  transmission: string;
  fuelType: string;
  daysOnLot: number;
  cost: string;
  photos: string[];
  features: string[];
}

interface VehicleDetailsProps {
  vehicle: Vehicle | null;
  onBack: () => void;
}

export function VehicleDetails({ vehicle, onBack }: VehicleDetailsProps) {
  if (!vehicle) return null;

  // Photo viewer state
  const [photoViewerOpen, setPhotoViewerOpen] = useState(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [photoZoomed, setPhotoZoomed] = useState(false);

  // Enhanced vehicle data for Canadian car market
  const enhancedVehicleData = {
    ...vehicle,
    // Canadian regulatory compliance
    transportCanadaApproved: true,
    emissionsCompliant: true,
    safetyStandard: 'CMVSS',
    drlCompliant: true,
    metricSpeedometer: true,
    
    // Canadian market specifics
    winterPackage: vehicle.features.includes('Heated Seats') || vehicle.features.includes('Remote Start'),
    allWeatherTires: true,
    blockHeater: vehicle.fuelType === 'Gasoline' || vehicle.fuelType === 'Hybrid',
    
    // Enhanced specifications
    engineSize: getEngineSize(vehicle.make, vehicle.model),
    fuelEconomy: getFuelEconomy(vehicle.make, vehicle.model, vehicle.fuelType),
    drivetrain: getDrivetrain(vehicle.make, vehicle.model),
    bodyStyle: getBodyStyle(vehicle.model),
    
    // Pricing and incentives
    msrp: vehicle.price,
    rebates: getCanadianRebates(vehicle.fuelType, vehicle.year),
    financing: {
      rate: '4.99%',
      term: '84 months',
      payment: calculatePayment(vehicle.price)
    },
    
    // Documentation
    carfaxReport: true,
    serviceRecords: true,
    ownershipHistory: '2 previous owners',
    accidentHistory: 'No accidents reported',
    
    // Warranty
    manufacturerWarranty: '3 years/60,000 km',
    powertrain: '5 years/100,000 km',
    corrosion: '7 years/unlimited km',
    
    // Canadian features
    languages: ['English', 'French'],
    radioStations: 'AM/FM/SiriusXM',
    navigationMaps: 'Canada/US',
    
    // Market analysis
    marketValue: vehicle.price,
    depreciation: calculateDepreciation(vehicle.year),
    competitiveAnalysis: getCompetitiveVehicles(vehicle.make, vehicle.model),
    
    // Dealer information
    certifiedPreOwned: vehicle.mileage !== '0 km',
    inspection: '150-point inspection completed',
    detailing: 'Professional detailing included',
    
    // Recent activity
    inquiries: Math.floor(Math.random() * 25) + 5,
    viewings: Math.floor(Math.random() * 15) + 3,
    testDrives: Math.floor(Math.random() * 8) + 1
  };

  function getEngineSize(make: string, model: string): string {
    const engines = {
      'Toyota Camry': '2.5L 4-Cylinder',
      'Honda Civic': '2.0L 4-Cylinder',
      'Ford F-150': '3.5L V6 EcoBoost',
      'Chevrolet Equinox': '1.5L Turbo 4-Cylinder',
      'Nissan Altima': '2.5L 4-Cylinder',
      'BMW X3': '2.0L Turbo 4-Cylinder',
      'Audi Q5': '2.0L TFSI Turbo',
      'Mercedes-Benz GLC': '2.0L Turbo 4-Cylinder'
    };
    return engines[`${make} ${model}`] || '2.0L 4-Cylinder';
  }

  function getFuelEconomy(make: string, model: string, fuelType: string): string {
    if (fuelType === 'Hybrid') return '5.8L/100km city, 6.0L/100km highway';
    if (fuelType === 'Electric') return '120 km range, 15.6 kWh/100km';
    
    const economies = {
      'Toyota Camry': '7.8L/100km city, 5.7L/100km highway',
      'Honda Civic': '7.6L/100km city, 5.0L/100km highway',
      'Ford F-150': '12.1L/100km city, 8.9L/100km highway',
      'Chevrolet Equinox': '8.2L/100km city, 6.5L/100km highway',
      'Nissan Altima': '7.4L/100km city, 5.8L/100km highway',
      'BMW X3': '9.1L/100km city, 7.4L/100km highway',
      'Audi Q5': '8.7L/100km city, 7.1L/100km highway',
      'Mercedes-Benz GLC': '9.4L/100km city, 7.8L/100km highway'
    };
    return economies[`${make} ${model}`] || '8.0L/100km city, 6.5L/100km highway';
  }

  function getDrivetrain(make: string, model: string): string {
    const drivetrains = {
      'Ford F-150': 'Intelligent 4WD',
      'Chevrolet Equinox': 'AWD',
      'BMW X3': 'xDrive AWD',
      'Audi Q5': 'quattro AWD',
      'Mercedes-Benz GLC': '4MATIC AWD'
    };
    return drivetrains[`${make} ${model}`] || 'FWD';
  }

  function getBodyStyle(model: string): string {
    const styles = {
      'Camry': 'Sedan',
      'Civic': 'Sedan',
      'F-150': 'Pickup Truck',
      'Equinox': 'Compact SUV',
      'Altima': 'Sedan',
      'X3': 'Luxury SUV',
      'Q5': 'Luxury SUV',
      'GLC': 'Luxury SUV'
    };
    return styles[model] || 'Sedan';
  }

  function getCanadianRebates(fuelType: string, year: number): string[] {
    const rebates = [];
    if (fuelType === 'Electric' && year >= 2023) {
      rebates.push('Federal iZEV Rebate: $5,000');
    }
    if (fuelType === 'Hybrid' && year >= 2023) {
      rebates.push('Federal iZEV Rebate: $2,500');
    }
    return rebates;
  }

  function calculatePayment(price: string): string {
    const amount = parseFloat(price.replace(/[$,]/g, ''));
    const monthlyPayment = (amount * 0.85) / 84; // Assuming 15% down, 84 months
    return `$${Math.round(monthlyPayment).toLocaleString()}/month`;
  }

  function calculateDepreciation(year: number): string {
    const age = new Date().getFullYear() - year;
    const depreciationRate = age * 15; // Rough estimate
    return `${Math.min(depreciationRate, 60)}% from MSRP`;
  }

  function getCompetitiveVehicles(make: string, model: string): string[] {
    const competitors = {
      'Toyota Camry': ['Honda Accord', 'Nissan Altima', 'Mazda6'],
      'Honda Civic': ['Toyota Corolla', 'Nissan Sentra', 'Hyundai Elantra'],
      'Ford F-150': ['Chevrolet Silverado', 'Ram 1500', 'GMC Sierra'],
      'Chevrolet Equinox': ['Honda CR-V', 'Toyota RAV4', 'Nissan Rogue'],
      'BMW X3': ['Audi Q5', 'Mercedes-Benz GLC', 'Volvo XC60'],
    };
    return competitors[`${make} ${model}`] || ['Similar vehicles in class'];
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'reserved': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'sold': return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getDaysOnLotColor = (days: number) => {
    if (days <= 14) return 'text-green-600 dark:text-green-400';
    if (days <= 30) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  // Photo viewer handlers
  const openPhotoViewer = (photoIndex: number) => {
    setCurrentPhotoIndex(photoIndex);
    setPhotoViewerOpen(true);
    setPhotoZoomed(false);
  };

  const closePhotoViewer = () => {
    setPhotoViewerOpen(false);
    setPhotoZoomed(false);
  };

  const nextPhoto = () => {
    setCurrentPhotoIndex((prev) => 
      prev === vehicle.photos.length - 1 ? 0 : prev + 1
    );
    setPhotoZoomed(false);
  };

  const prevPhoto = () => {
    setCurrentPhotoIndex((prev) => 
      prev === 0 ? vehicle.photos.length - 1 : prev - 1
    );
    setPhotoZoomed(false);
  };

  const toggleZoom = () => {
    setPhotoZoomed(!photoZoomed);
  };

  // Keyboard navigation for photo viewer
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!photoViewerOpen) return;
    
    switch (e.key) {
      case 'Escape':
        closePhotoViewer();
        break;
      case 'ArrowLeft':
        prevPhoto();
        break;
      case 'ArrowRight':
        nextPhoto();
        break;
      case 'z':
      case 'Z':
        toggleZoom();
        break;
    }
  };

  // Add keyboard event listener
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [photoViewerOpen]);

  // Prevent body scroll when photo viewer is open
  React.useEffect(() => {
    if (photoViewerOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [photoViewerOpen]);

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink onClick={onBack} className="flex items-center space-x-1 cursor-pointer hover:text-brand-orange transition-colors">
                <Car className="h-4 w-4" />
                <span>Inventory</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="font-medium">{vehicle.year} {vehicle.make} {vehicle.model}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Inventory</span>
        </Button>
      </div>

      {/* Header Section */}
      <div className="bg-gradient-to-r from-brand-orange to-brand-orange-light rounded-lg p-6 text-white">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-6">
            <div className="relative">
              <ImageWithFallback
                src={vehicle.photos[0]}
                alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                className="w-32 h-24 object-cover rounded-lg ring-4 ring-white shadow-lg"
              />
              <div className="absolute -top-2 -right-2">
                <Badge variant="outline" className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="h-3 w-3 mr-1" />
                  {vehicle.photos.length}
                </Badge>
              </div>
            </div>
            <div className="text-white">
              <h1 className="text-3xl font-bold mb-1">{vehicle.year} {vehicle.make} {vehicle.model}</h1>
              <div className="flex items-center space-x-3 mb-2">
                <Badge className={getStatusColor(vehicle.status)} variant="secondary">
                  {vehicle.status}
                </Badge>
                <Badge variant="outline" className="bg-white/20 text-white border-white/30">
                  {enhancedVehicleData.bodyStyle}
                </Badge>
                <span className="text-white/90 text-sm">VIN: {vehicle.vin}</span>
              </div>
              <div className="flex items-center space-x-4 text-sm text-white/80">
                <div className="flex items-center space-x-1">
                  <Gauge className="h-4 w-4" />
                  <span>{vehicle.mileage}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>{vehicle.location}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span className={getDaysOnLotColor(vehicle.daysOnLot)}>
                    {vehicle.daysOnLot} days on lot
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-4xl font-bold text-white mb-1">{vehicle.price}</div>
            <div className="text-sm text-white/80">
              {enhancedVehicleData.financing.payment}
            </div>
            <div className="flex space-x-2 mt-3">
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <Target className="h-4 w-4 mr-2" />
                Match Customers
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="space-y-6">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-muted/30 p-1 rounded-lg">
            <TabsTrigger 
              value="overview" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <Car className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger 
              value="specifications" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <Settings className="h-4 w-4" />
              <span>Specifications</span>
            </TabsTrigger>
            <TabsTrigger 
              value="features" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <Star className="h-4 w-4" />
              <span>Features</span>
            </TabsTrigger>
            <TabsTrigger 
              value="pricing" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <DollarSign className="h-4 w-4" />
              <span>Pricing</span>
            </TabsTrigger>
            <TabsTrigger 
              value="history" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <History className="h-4 w-4" />
              <span>History</span>
            </TabsTrigger>
            <TabsTrigger 
              value="activity" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <Activity className="h-4 w-4" />
              <span>Activity</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Vehicle Photos */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Camera className="h-5 w-5 text-brand-orange" />
                      <span>Vehicle Photos ({vehicle.photos.length})</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openPhotoViewer(0)}
                      className="text-brand-orange border-brand-orange hover:bg-brand-orange hover:text-white"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View All
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    {vehicle.photos.map((photo, index) => (
                      <div
                        key={index}
                        className="relative group cursor-pointer overflow-hidden rounded-lg border border-outline-variant hover:border-brand-orange transition-all duration-200"
                        onClick={() => openPhotoViewer(index)}
                      >
                        <ImageWithFallback
                          src={photo}
                          alt={`${vehicle.year} ${vehicle.make} ${vehicle.model} - Image ${index + 1}`}
                          className="w-full h-32 object-cover transition-transform duration-200 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <div className="bg-white/90 rounded-full p-2">
                              <Eye className="h-4 w-4 text-brand-orange" />
                            </div>
                          </div>
                        </div>
                        {index === 0 && (
                          <div className="absolute top-2 left-2 bg-brand-orange text-white text-xs px-2 py-1 rounded-full font-medium">
                            Main
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 text-center">
                    <p className="text-sm text-muted-foreground">
                      Click any photo to view in full screen
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Key Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-brand-orange" />
                    <span>Key Features</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    {vehicle.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Separator className="my-4" />
                  
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Canadian Winter Package</h4>
                    <div className="grid grid-cols-1 gap-2">
                      {enhancedVehicleData.winterPackage && (
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Winter Package Included</span>
                        </div>
                      )}
                      {enhancedVehicleData.blockHeater && (
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Block Heater</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">All-Weather Tires</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Info className="h-5 w-5 text-brand-orange" />
                    <span>Basic Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Year</p>
                      <p className="font-medium">{vehicle.year}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Make & Model</p>
                      <p className="font-medium">{vehicle.make} {vehicle.model}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Mileage</p>
                      <p className="font-medium">{vehicle.mileage}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Body Style</p>
                      <p className="font-medium">{enhancedVehicleData.bodyStyle}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Drivetrain</p>
                      <p className="font-medium">{enhancedVehicleData.drivetrain}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Transmission</p>
                      <p className="font-medium">{vehicle.transmission}</p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Exterior Color</p>
                      <p className="font-medium">{vehicle.exterior}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Interior Color</p>
                      <p className="font-medium">{vehicle.interior}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Fuel Type</p>
                      <p className="font-medium">{vehicle.fuelType}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Engine</p>
                      <p className="font-medium">{enhancedVehicleData.engineSize}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Canadian Compliance */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Flag className="h-5 w-5 text-brand-orange" />
                    <span>Canadian Compliance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Transport Canada Approved</span>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">CMVSS Safety Standard</span>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">DRL Compliant</span>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Metric Speedometer</span>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Emissions Compliant</span>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </div>
                  </div>
                  
                  <Separator className="my-4" />
                  
                  <div>
                    <p className="text-sm text-muted-foreground">Languages</p>
                    <div className="flex space-x-2 mt-1">
                      {enhancedVehicleData.languages.map((lang) => (
                        <Badge key={lang} variant="outline">{lang}</Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Specifications Tab */}
          <TabsContent value="specifications" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5 text-brand-orange" />
                    <span>Engine & Performance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Engine</p>
                      <p className="font-medium">{enhancedVehicleData.engineSize}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Transmission</p>
                      <p className="font-medium">{vehicle.transmission}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Drivetrain</p>
                      <p className="font-medium">{enhancedVehicleData.drivetrain}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Fuel Type</p>
                      <p className="font-medium">{vehicle.fuelType}</p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">Fuel Economy (L/100km)</p>
                    <p className="font-medium">{enhancedVehicleData.fuelEconomy}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5 text-brand-orange" />
                    <span>Safety & Security</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {vehicle.features
                      .filter(feature => 
                        feature.toLowerCase().includes('safety') || 
                        feature.toLowerCase().includes('airbag') ||
                        feature.toLowerCase().includes('camera') ||
                        feature.toLowerCase().includes('sensor')
                      )
                      .map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span className="text-sm">5-Star Safety Rating</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Advanced Driver Assistance</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Features Tab */}
          <TabsContent value="features" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Vehicle Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-brand-orange" />
                    <span>Vehicle Features ({vehicle.features.length})</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {vehicle.features.map((feature, index) => (
                      <Badge 
                        key={index} 
                        variant="secondary" 
                        className="bg-primary-95 text-primary-30 hover:bg-primary-90 transition-colors cursor-default border border-primary-80"
                      >
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Canadian Winter Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5 text-brand-orange" />
                    <span>Canadian Winter Package</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {enhancedVehicleData.winterPackage && (
                      <Badge 
                        variant="secondary" 
                        className="bg-green-100 text-green-800 hover:bg-green-200 transition-colors cursor-default border border-green-300"
                      >
                        Winter Package
                      </Badge>
                    )}
                    {enhancedVehicleData.blockHeater && (
                      <Badge 
                        variant="secondary" 
                        className="bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors cursor-default border border-blue-300"
                      >
                        Block Heater
                      </Badge>
                    )}
                    <Badge 
                      variant="secondary" 
                      className="bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors cursor-default border border-blue-300"
                    >
                      All-Weather Tires
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Technology Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5 text-brand-orange" />
                    <span>Technology & Connectivity</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {vehicle.features
                      .filter(feature => 
                        feature.toLowerCase().includes('carplay') || 
                        feature.toLowerCase().includes('android') ||
                        feature.toLowerCase().includes('navigation') ||
                        feature.toLowerCase().includes('wireless') ||
                        feature.toLowerCase().includes('bluetooth') ||
                        feature.toLowerCase().includes('wifi') ||
                        feature.toLowerCase().includes('usb') ||
                        feature.toLowerCase().includes('charging') ||
                        feature.toLowerCase().includes('infotainment') ||
                        feature.toLowerCase().includes('display') ||
                        feature.toLowerCase().includes('screen')
                      )
                      .map((feature, index) => (
                        <Badge 
                          key={index} 
                          variant="secondary" 
                          className="bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors cursor-default border border-purple-300"
                        >
                          {feature}
                        </Badge>
                      ))}
                    {/* Canadian-specific tech features */}
                    <Badge 
                      variant="secondary" 
                      className="bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors cursor-default border border-purple-300"
                    >
                      Canadian Maps
                    </Badge>
                    <Badge 
                      variant="secondary" 
                      className="bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors cursor-default border border-purple-300"
                    >
                      Bilingual Interface
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Safety Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5 text-brand-orange" />
                    <span>Safety & Driver Assistance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {vehicle.features
                      .filter(feature => 
                        feature.toLowerCase().includes('safety') || 
                        feature.toLowerCase().includes('camera') ||
                        feature.toLowerCase().includes('sensor') ||
                        feature.toLowerCase().includes('assist') ||
                        feature.toLowerCase().includes('brake') ||
                        feature.toLowerCase().includes('collision') ||
                        feature.toLowerCase().includes('lane') ||
                        feature.toLowerCase().includes('cruise') ||
                        feature.toLowerCase().includes('blind') ||
                        feature.toLowerCase().includes('emergency')
                      )
                      .map((feature, index) => (
                        <Badge 
                          key={index} 
                          variant="secondary" 
                          className="bg-green-100 text-green-800 hover:bg-green-200 transition-colors cursor-default border border-green-300"
                        >
                          {feature}
                        </Badge>
                      ))}
                    {/* Additional safety features */}
                    <Badge 
                      variant="secondary" 
                      className="bg-green-100 text-green-800 hover:bg-green-200 transition-colors cursor-default border border-green-300"
                    >
                      5-Star Safety Rating
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Comfort & Convenience */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Award className="h-5 w-5 text-brand-orange" />
                    <span>Comfort & Convenience</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {vehicle.features
                      .filter(feature => 
                        feature.toLowerCase().includes('heated') || 
                        feature.toLowerCase().includes('cooled') ||
                        feature.toLowerCase().includes('seat') ||
                        feature.toLowerCase().includes('climate') ||
                        feature.toLowerCase().includes('air') ||
                        feature.toLowerCase().includes('leather') ||
                        feature.toLowerCase().includes('power') ||
                        feature.toLowerCase().includes('memory') ||
                        feature.toLowerCase().includes('sunroof') ||
                        feature.toLowerCase().includes('moonroof') ||
                        feature.toLowerCase().includes('keyless') ||
                        feature.toLowerCase().includes('remote')
                      )
                      .map((feature, index) => (
                        <Badge 
                          key={index} 
                          variant="secondary" 
                          className="bg-orange-100 text-orange-800 hover:bg-orange-200 transition-colors cursor-default border border-orange-300"
                        >
                          {feature}
                        </Badge>
                      ))}
                  </div>
                </CardContent>
              </Card>

              {/* Audio & Entertainment */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Activity className="h-5 w-5 text-brand-orange" />
                    <span>Audio & Entertainment</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {vehicle.features
                      .filter(feature => 
                        feature.toLowerCase().includes('audio') || 
                        feature.toLowerCase().includes('sound') ||
                        feature.toLowerCase().includes('speaker') ||
                        feature.toLowerCase().includes('radio') ||
                        feature.toLowerCase().includes('music') ||
                        feature.toLowerCase().includes('entertainment') ||
                        feature.toLowerCase().includes('premium') ||
                        feature.toLowerCase().includes('bose') ||
                        feature.toLowerCase().includes('harman') ||
                        feature.toLowerCase().includes('bang')
                      )
                      .map((feature, index) => (
                        <Badge 
                          key={index} 
                          variant="secondary" 
                          className="bg-pink-100 text-pink-800 hover:bg-pink-200 transition-colors cursor-default border border-pink-300"
                        >
                          {feature}
                        </Badge>
                      ))}
                    {/* Canadian audio features */}
                    <Badge 
                      variant="secondary" 
                      className="bg-pink-100 text-pink-800 hover:bg-pink-200 transition-colors cursor-default border border-pink-300"
                    >
                      SiriusXM Ready
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Pricing Tab */}
          <TabsContent value="pricing" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5 text-brand-orange" />
                    <span>Pricing Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">Asking Price</span>
                      <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {vehicle.price}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">Dealer Cost</span>
                      <span className="text-lg font-medium text-blue-600 dark:text-blue-400">
                        {vehicle.cost}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">Market Value</span>
                      <span className="text-lg font-medium text-purple-600 dark:text-purple-400">
                        {enhancedVehicleData.marketValue}
                      </span>
                    </div>
                  </div>
                  
                  {enhancedVehicleData.rebates.length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-medium mb-2">Available Rebates</h4>
                        <div className="space-y-2">
                          {enhancedVehicleData.rebates.map((rebate, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-sm">{rebate}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-brand-orange" />
                    <span>Financing Options</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Interest Rate</span>
                      <span className="font-medium">{enhancedVehicleData.financing.rate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Term</span>
                      <span className="font-medium">{enhancedVehicleData.financing.term}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Monthly Payment</span>
                      <span className="font-bold text-brand-orange">{enhancedVehicleData.financing.payment}</span>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <p className="text-sm text-muted-foreground">*Financing available through approved lenders. Rates and terms subject to credit approval. Down payment may be required.</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-brand-orange" />
                    <span>Vehicle History</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Ownership History</p>
                      <p className="font-medium">{enhancedVehicleData.ownershipHistory}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Accident History</p>
                      <p className="font-medium text-green-600">{enhancedVehicleData.accidentHistory}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">CARFAX Report Available</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Service Records Available</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Award className="h-5 w-5 text-brand-orange" />
                    <span>Warranty Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <Shield className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <p className="font-medium">Manufacturer</p>
                      <p className="text-sm text-muted-foreground">{enhancedVehicleData.manufacturerWarranty}</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <Wrench className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <p className="font-medium">Powertrain</p>
                      <p className="text-sm text-muted-foreground">{enhancedVehicleData.powertrain}</p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <Shield className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <p className="font-medium">Corrosion</p>
                      <p className="text-sm text-muted-foreground">{enhancedVehicleData.corrosion}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Activity className="h-5 w-5 text-brand-orange" />
                    <span>Customer Interest</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <Eye className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-blue-600">{enhancedVehicleData.inquiries}</p>
                      <p className="text-xs text-muted-foreground">Inquiries</p>
                    </div>
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-green-600">{enhancedVehicleData.viewings}</p>
                      <p className="text-xs text-muted-foreground">Viewings</p>
                    </div>
                    <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <Car className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-purple-600">{enhancedVehicleData.testDrives}</p>
                      <p className="text-xs text-muted-foreground">Test Drives</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-brand-orange" />
                    <span>Market Analysis</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Depreciation</p>
                      <p className="font-medium">{enhancedVehicleData.depreciation}</p>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Competitive Vehicles</p>
                      <div className="space-y-1">
                        {enhancedVehicleData.competitiveAnalysis.map((competitor, index) => (
                          <div key={index} className="text-sm">• {competitor}</div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <History className="h-5 w-5 text-brand-orange" />
                  <span>Recent Activity</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-3 border-l-4 border-brand-orange bg-orange-50 dark:bg-orange-900/20 rounded-r-lg">
                    <Eye className="h-5 w-5 text-brand-orange mt-0.5" />
                    <div>
                      <p className="font-medium">Vehicle Listed</p>
                      <p className="text-sm text-muted-foreground">
                        Listed for sale {vehicle.daysOnLot} days ago at {vehicle.location}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 p-3 border-l-4 border-green-500 bg-green-50 dark:bg-green-900/20 rounded-r-lg">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Inspection Completed</p>
                      <p className="text-sm text-muted-foreground">
                        {enhancedVehicleData.inspection} - All systems checked
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-3 border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-r-lg">
                    <Wrench className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Detailing Completed</p>
                      <p className="text-sm text-muted-foreground">
                        {enhancedVehicleData.detailing} - Ready for sale
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Photo Viewer Modal */}
      {photoViewerOpen && (
        <div className="fixed inset-0 z-50 bg-black/95 flex items-center justify-center">
          {/* Header with controls */}
          <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent p-6">
            <div className="flex items-center justify-between text-white">
              <div className="flex items-center space-x-4">
                <h3 className="text-lg font-semibold">
                  {vehicle.year} {vehicle.make} {vehicle.model}
                </h3>
                <Badge variant="outline" className="border-white/30 text-white">
                  {currentPhotoIndex + 1} of {vehicle.photos.length}
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleZoom}
                  className="text-white hover:bg-white/20"
                >
                  <ZoomIn className="h-4 w-4 mr-2" />
                  {photoZoomed ? 'Zoom Out' : 'Zoom In'}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white/20"
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = vehicle.photos[currentPhotoIndex];
                    link.download = `${vehicle.year}-${vehicle.make}-${vehicle.model}-photo-${currentPhotoIndex + 1}.jpg`;
                    link.click();
                  }}
                >
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={closePhotoViewer}
                  className="text-white hover:bg-white/20"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Navigation arrows */}
          {vehicle.photos.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="lg"
                onClick={prevPhoto}
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full w-12 h-12 p-0"
              >
                <ChevronLeft className="h-6 w-6" />
              </Button>
              <Button
                variant="ghost"
                size="lg"
                onClick={nextPhoto}
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full w-12 h-12 p-0"
              >
                <ChevronRight className="h-6 w-6" />
              </Button>
            </>
          )}

          {/* Main photo */}
          <div className="w-full h-full flex items-center justify-center p-8">
            <div className={`relative max-w-full max-h-full transition-transform duration-300 ${photoZoomed ? 'scale-150 cursor-zoom-out' : 'cursor-zoom-in'}`}>
              <ImageWithFallback
                src={vehicle.photos[currentPhotoIndex]}
                alt={`${vehicle.year} ${vehicle.make} ${vehicle.model} - Photo ${currentPhotoIndex + 1}`}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                onClick={toggleZoom}
              />
            </div>
          </div>

          {/* Thumbnail strip */}
          {vehicle.photos.length > 1 && (
            <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/50 to-transparent p-6">
              <div className="flex items-center justify-center space-x-2 overflow-x-auto">
                {vehicle.photos.map((photo, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentPhotoIndex(index)}
                    className={`relative flex-shrink-0 w-16 h-12 rounded-md overflow-hidden border-2 transition-all duration-200 ${
                      index === currentPhotoIndex
                        ? 'border-brand-orange scale-110'
                        : 'border-white/30 hover:border-white/60 hover:scale-105'
                    }`}
                  >
                    <ImageWithFallback
                      src={photo}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    {index === currentPhotoIndex && (
                      <div className="absolute inset-0 bg-brand-orange/20" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Keyboard shortcuts hint */}
          <div className="absolute bottom-4 left-4 text-white/70 text-xs">
            <p>Use ← → arrow keys to navigate • ESC to close • Z to zoom</p>
          </div>
        </div>
      )}
    </div>
  );
}