import * as React from "react";
import { cn } from "../utils";

export interface TextFieldProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  error?: boolean;
  leadingIcon?: React.ReactNode;
  trailingIcon?: React.ReactNode;
  variant?: "outlined" | "filled";
  required?: boolean;
  compact?: boolean;
}

const TextField = React.forwardRef<HTMLInputElement, TextFieldProps>(
  (
    {
      className,
      type,
      label,
      helperText,
      error,
      leadingIcon,
      trailingIcon,
      variant = "outlined",
      value,
      defaultValue,
      required,
      placeholder,
      compact = false,
      ...props
    },
    ref
  ) => {
    const [focused, setFocused] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(false);
    const inputRef = React.useRef<HTMLInputElement>(null);

    // Combine refs
    const combinedRef = React.useCallback((node: HTMLInputElement) => {
      inputRef.current = node;
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    }, [ref]);

    // Check for value on mount and when value prop changes
    React.useEffect(() => {
      const checkValue = () => {
        if (inputRef.current) {
          const currentValue = inputRef.current.value;
          setHasValue(currentValue && currentValue.length > 0);
        } else {
          // Fallback to checking props
          const propValue = value !== undefined ? value : defaultValue;
          setHasValue(propValue !== undefined && propValue !== null && String(propValue).length > 0);
        }
      };

      checkValue();
    }, [value, defaultValue]);

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(false);
      // Update hasValue on blur to ensure it's current
      setHasValue(e.target.value.length > 0);
      props.onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e);
    };

    const inputId = React.useId();
    const isLabelFloating = focused || hasValue || (placeholder && !label);
    
    // Don't show placeholder when label is present and not floating
    const shouldShowPlaceholder = !label || isLabelFloating;

    return (
      <div className="relative w-full">
        <div
          className={cn(
            "relative flex items-center transition-all duration-200",
            variant === "outlined" && [
              "rounded-lg border bg-white",
              !error && !focused && "border-neutral-variant-80",
              !error && focused && "border-primary-50 border-2 shadow-sm",
              error && !focused && "border-error-50",
              error && focused && "border-error-50 border-2",
              "hover:border-neutral-variant-60",
              focused && "hover:border-current"
            ],
            variant === "filled" && [
              "rounded-t-lg border-b-2 bg-neutral-95",
              "hover:bg-neutral-90",
              !error && !focused && "border-b-neutral-variant-50",
              !error && focused && "border-b-primary-50",
              error && "border-b-error-50"
            ]
          )}
        >
          {leadingIcon && (
            <div className={cn(
              "flex items-center justify-center text-neutral-50 flex-shrink-0",
              compact ? "w-10 h-10" : "w-12 h-14"
            )}>
              {leadingIcon}
            </div>
          )}
          
          <div className="relative flex-1 min-w-0">
            <input
              id={inputId}
              type={type}
              ref={combinedRef}
              value={value}
              defaultValue={defaultValue}
              placeholder={shouldShowPlaceholder ? placeholder : ""}
              required={required}
              className={cn(
                "flex w-full bg-transparent transition-colors text-input-text caret-primary-50",
                "file:border-0 file:bg-transparent file:text-sm file:font-medium",
                "focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
                "placeholder:text-input-placeholder placeholder:opacity-100",
                variant === "outlined" && [
                  compact ? "h-10 px-3" : "h-14 px-4",
                  compact && label && isLabelFloating ? "pt-4 pb-1" : "",
                  compact && label && !isLabelFloating ? "py-2" : "",
                  !compact && label && isLabelFloating ? "pt-6 pb-2" : "",
                  !compact && !label ? "py-4" : "",
                  compact && !label ? "py-2" : ""
                ],
                variant === "filled" && [
                  compact ? "h-10 px-3" : "h-14 px-4",
                  compact && label && isLabelFloating ? "pt-5 pb-1" : "",
                  compact && label && !isLabelFloating ? "py-2" : "",
                  !compact && label && isLabelFloating ? "pt-7 pb-1" : "",
                  !compact && !label ? "py-4" : "",
                  compact && !label ? "py-2" : ""
                ],
                leadingIcon && "pl-0",
                trailingIcon && "pr-0",
                className
              )}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={handleChange}
              {...props}
            />

            {label && (
              <label
                htmlFor={inputId}
                className={cn(
                  "absolute transition-all duration-200 pointer-events-none select-none origin-left",
                  "text-input-label font-medium",
                  compact ? "left-3" : "left-4",
                  // Positioning logic
                  isLabelFloating ? [
                    // Floating state
                    compact ? "top-1 scale-75 transform text-xs" : "top-2 scale-75 transform text-xs",
                    variant === "outlined" && "bg-white px-1 -translate-y-1/2",
                    variant === "filled" && "translate-y-0",
                    !error && focused && "text-primary-50",
                    error && "text-error-50"
                  ] : [
                    // Resting state
                    "top-1/2 scale-100 -translate-y-1/2",
                    compact ? "text-xs" : "text-sm",
                    "text-neutral-variant-40"
                  ],
                  leadingIcon && isLabelFloating && "left-0",
                  leadingIcon && !isLabelFloating && (compact ? "left-10" : "left-12")
                )}
              >
                {label}
                {required && (
                  <span className="text-error-50 ml-1">*</span>
                )}
              </label>
            )}
          </div>

          {trailingIcon && (
            <div className={cn(
              "flex items-center justify-center text-neutral-50 flex-shrink-0",
              compact ? "w-10 h-10" : "w-12 h-14"
            )}>
              {trailingIcon}
            </div>
          )}
        </div>

        {helperText && (
          <p
            className={cn(
              "mt-1 text-xs px-4 transition-colors font-normal",
              error ? "text-error-50" : "text-neutral-variant-50"
            )}
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);
TextField.displayName = "TextField";

export { TextField };