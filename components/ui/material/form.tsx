import * as React from "react";
import { cn } from "../utils";
import { TextField, type TextFieldProps } from "./text-field";
import { SelectField, SelectFieldTrigger, SelectFieldContent, SelectFieldItem, SelectFieldValue, type SelectFieldTrigger as SelectFieldTriggerType } from "./select-field";

interface FormFieldProps {
  children: React.ReactNode;
  className?: string;
  error?: boolean;
}

export const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ children, className, error }, ref) => {
    return (
      <div 
        ref={ref}
        className={cn(
          "form-field space-y-1",
          error && "error",
          className
        )}
      >
        {children}
      </div>
    );
  }
);
FormField.displayName = "FormField";

interface FormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
  error?: boolean;
}

export const FormLabel = React.forwardRef<HTMLLabelElement, FormLabelProps>(
  ({ className, required, error, children, ...props }, ref) => {
    return (
      <label
        ref={ref}
        className={cn(
          "form-label text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
          "text-input-label",
          error && "text-error-50",
          className
        )}
        {...props}
      >
        {children}
        {required && (
          <span className="text-error-50 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
    );
  }
);
FormLabel.displayName = "FormLabel";

interface FormMessageProps extends React.HTMLAttributes<HTMLParagraphElement> {
  error?: boolean;
}

export const FormMessage = React.forwardRef<HTMLParagraphElement, FormMessageProps>(
  ({ className, error, children, ...props }, ref) => {
    if (!children) return null;
    
    return (
      <p
        ref={ref}
        className={cn(
          "text-xs font-normal transition-colors",
          error ? "text-error-50" : "text-neutral-variant-50",
          className
        )}
        {...props}
      >
        {children}
      </p>
    );
  }
);
FormMessage.displayName = "FormMessage";

interface FormDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const FormDescription = React.forwardRef<HTMLParagraphElement, FormDescriptionProps>(
  ({ className, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn("text-xs text-neutral-variant-50 font-normal", className)}
        {...props}
      />
    );
  }
);
FormDescription.displayName = "FormDescription";

// Enhanced Material TextField with form integration
interface MaterialTextFieldProps extends TextFieldProps {
  name: string;
  description?: string;
}

export const MaterialTextField = React.forwardRef<HTMLInputElement, MaterialTextFieldProps>(
  ({ name, label, required, error, helperText, description, className, ...props }, ref) => {
    const fieldId = React.useId();
    
    return (
      <FormField error={error} className={className}>
        <TextField
          ref={ref}
          id={fieldId}
          name={name}
          label={label}
          required={required}
          error={error}
          helperText={helperText}
          aria-describedby={description ? `${fieldId}-description` : undefined}
          {...props}
        />
        {description && (
          <FormDescription id={`${fieldId}-description`}>
            {description}
          </FormDescription>
        )}
      </FormField>
    );
  }
);
MaterialTextField.displayName = "MaterialTextField";

// Enhanced Material SelectField with form integration
interface MaterialSelectFieldProps {
  name: string;
  label?: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  description?: string;
  placeholder?: string;
  children: React.ReactNode;
  value?: string;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
  className?: string;
}

export const MaterialSelectField = React.forwardRef<
  React.ElementRef<typeof SelectFieldTriggerType>,
  MaterialSelectFieldProps
>(({ 
  name, 
  label, 
  required, 
  error, 
  helperText, 
  description, 
  placeholder, 
  children, 
  value, 
  defaultValue, 
  onValueChange, 
  className,
  ...props 
}, ref) => {
  const fieldId = React.useId();
  
  return (
    <FormField error={error} className={className}>
      <SelectField value={value} defaultValue={defaultValue} onValueChange={onValueChange} name={name}>
        <SelectFieldTrigger
          ref={ref}
          id={fieldId}
          label={label}
          required={required}
          error={error}
          helperText={helperText}
          placeholder={placeholder}
          aria-describedby={description ? `${fieldId}-description` : undefined}
          {...props}
        >
          <SelectFieldValue placeholder={placeholder} />
        </SelectFieldTrigger>
        <SelectFieldContent>
          {children}
        </SelectFieldContent>
      </SelectField>
      {description && (
        <FormDescription id={`${fieldId}-description`}>
          {description}
        </FormDescription>
      )}
    </FormField>
  );
});
MaterialSelectField.displayName = "MaterialSelectField";

// Form container with proper spacing and structure
interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  spacing?: "compact" | "normal" | "relaxed";
}

export const Form = React.forwardRef<HTMLFormElement, FormProps>(
  ({ className, spacing = "normal", children, ...props }, ref) => {
    const spacingClasses = {
      compact: "space-y-4",
      normal: "space-y-6",
      relaxed: "space-y-8",
    };
    
    return (
      <form
        ref={ref}
        className={cn(
          "w-full",
          spacingClasses[spacing],
          className
        )}
        {...props}
      >
        {children}
      </form>
    );
  }
);
Form.displayName = "Form";

// Form section for grouping related fields
interface FormSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  description?: string;
}

export const FormSection = React.forwardRef<HTMLDivElement, FormSectionProps>(
  ({ className, title, description, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("space-y-4", className)}
        {...props}
      >
        {(title || description) && (
          <div className="space-y-1">
            {title && (
              <h3 className="text-lg font-semibold text-on-surface">{title}</h3>
            )}
            {description && (
              <p className="text-sm text-neutral-variant-50">{description}</p>
            )}
          </div>
        )}
        <div className="space-y-4">
          {children}
        </div>
      </div>
    );
  }
);
FormSection.displayName = "FormSection";

export {
  TextField as MaterialTextFieldBase,
  SelectField as MaterialSelectFieldBase,
  SelectFieldTrigger,
  SelectFieldContent,
  SelectFieldItem,
  SelectFieldValue,
};