import * as React from "react";
import * as SelectPrimitive from "@radix-ui/react-select";
import { Check, ChevronDown } from "lucide-react";
import { cn } from "../utils";

const SelectField = SelectPrimitive.Root;

const SelectFieldGroup = SelectPrimitive.Group;

const SelectFieldValue = SelectPrimitive.Value;

const SelectFieldTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
    label?: string;
    error?: boolean;
    helperText?: string;
    placeholder?: string;
    required?: boolean;
    compact?: boolean;
  }
>(({ className, children, label, error, helperText, placeholder, required, compact = false, ...props }, ref) => {
  const [focused, setFocused] = React.useState(false);
  const [hasValue, setHasValue] = React.useState(false);
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  // Combine refs
  const combinedRef = React.useCallback((node: HTMLButtonElement) => {
    triggerRef.current = node;
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
  }, [ref]);

  // Monitor value changes using MutationObserver
  React.useEffect(() => {
    if (!triggerRef.current) return;

    const checkValue = () => {
      if (triggerRef.current) {
        const valueElement = triggerRef.current.querySelector('[data-radix-select-value]');
        const hasContent = valueElement && 
          valueElement.textContent && 
          valueElement.textContent.trim() !== '' &&
          !valueElement.hasAttribute('data-placeholder');
        setHasValue(Boolean(hasContent));
      }
    };

    // Initial check
    checkValue();

    // Create mutation observer to watch for value changes
    const observer = new MutationObserver(checkValue);
    
    observer.observe(triggerRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: true,
      attributeFilter: ['data-placeholder', 'data-state']
    });

    return () => observer.disconnect();
  }, []);

  const triggerId = React.useId();
  const isLabelFloating = focused || hasValue;

  return (
    <div className="relative w-full">
      <div
        className={cn(
          "relative flex items-center rounded-lg border transition-all duration-200 bg-white",
          !error && !focused && "border-neutral-variant-80",
          !error && focused && "border-primary-50 border-2 shadow-sm",
          error && !focused && "border-error-50",
          error && focused && "border-error-50 border-2",
          "hover:border-neutral-variant-60",
          focused && "hover:border-current"
        )}
      >
        <div className="relative flex-1 min-w-0">
          <SelectPrimitive.Trigger
            id={triggerId}
            ref={combinedRef}
            className={cn(
              "flex w-full items-center justify-between bg-transparent text-select-text",
              "text-sm ring-offset-background focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",
              "[&>span]:line-clamp-1 [&>span]:text-left [&>span]:text-input-text",
              compact ? "h-10 px-3" : "h-14 px-4",
              compact && label && isLabelFloating ? "pt-4 pb-1" : "",
              compact && label && !isLabelFloating ? "py-2" : "",
              !compact && label && isLabelFloating ? "pt-6 pb-2" : "",
              !compact && !label ? "py-4" : "",
              compact && !label ? "py-2" : "",
              className
            )}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            {...props}
          >
            <SelectPrimitive.Value 
              placeholder={placeholder}
              className="text-input-text placeholder:text-input-placeholder"
            />
            <SelectPrimitive.Icon asChild>
              <ChevronDown className="h-5 w-5 opacity-50 flex-shrink-0 text-neutral-variant-60" />
            </SelectPrimitive.Icon>
          </SelectPrimitive.Trigger>

          {label && (
            <label
              htmlFor={triggerId}
              className={cn(
                "absolute transition-all duration-200 pointer-events-none select-none origin-left",
                "text-input-label font-medium",
                compact ? "left-3" : "left-4",
                // Positioning logic
                isLabelFloating ? [
                  // Floating state
                  compact ? "top-1 scale-75 transform bg-white px-1 -translate-y-1/2 text-xs" : "top-2 scale-75 transform bg-white px-1 -translate-y-1/2 text-xs",
                  !error && focused && "text-primary-50",
                  error && "text-error-50"
                ] : [
                  // Resting state
                  "top-1/2 scale-100 -translate-y-1/2",
                  compact ? "text-xs" : "text-sm",
                  "text-neutral-variant-40"
                ]
              )}
            >
              {label}
              {required && (
                <span className="text-error-50 ml-1">*</span>
              )}
            </label>
          )}
        </div>
      </div>

      {helperText && (
        <p
          className={cn(
            "mt-1 text-xs px-4 transition-colors font-normal",
            error ? "text-error-50" : "text-neutral-variant-50"
          )}
        >
          {helperText}
        </p>
      )}
    </div>
  );
});
SelectFieldTrigger.displayName = SelectPrimitive.Trigger.displayName;

const SelectFieldScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4 rotate-180" />
  </SelectPrimitive.ScrollUpButton>
));
SelectFieldScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;

const SelectFieldScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
));
SelectFieldScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName;

const SelectFieldContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = "popper", ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        "relative z-50 max-h-96 min-w-[8rem] overflow-hidden",
        "rounded-lg border border-neutral-variant-80 bg-white text-select-text shadow-lg",
        "data-[state=open]:animate-in data-[state=closed]:animate-out",
        "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
        "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
        "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
        "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      position={position}
      {...props}
    >
      <SelectFieldScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          "p-1",
          position === "popper" &&
            "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectFieldScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
SelectFieldContent.displayName = SelectPrimitive.Content.displayName;

const SelectFieldLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("py-1.5 pl-8 pr-2 text-sm font-semibold text-neutral-variant-30", className)}
    {...props}
  />
));
SelectFieldLabel.displayName = SelectPrimitive.Label.displayName;

const SelectFieldItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex w-full cursor-default select-none items-center",
      "rounded-sm py-2.5 pl-8 pr-2 text-sm outline-none transition-colors",
      "focus:bg-primary-95 focus:text-primary-40",
      "data-[state=checked]:bg-primary-90 data-[state=checked]:text-primary-30 data-[state=checked]:font-medium",
      "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      "hover:bg-neutral-95",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="h-4 w-4 text-primary-50" />
      </SelectPrimitive.ItemIndicator>
    </span>

    <SelectPrimitive.ItemText className="text-select-text">{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
));
SelectFieldItem.displayName = SelectPrimitive.Item.displayName;

const SelectFieldSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-neutral-variant-90", className)}
    {...props}
  />
));
SelectFieldSeparator.displayName = SelectPrimitive.Separator.displayName;

export {
  SelectField,
  SelectFieldGroup,
  SelectFieldValue,
  SelectFieldTrigger,
  SelectFieldContent,
  SelectFieldLabel,
  SelectFieldItem,
  SelectFieldSeparator,
  SelectFieldScrollUpButton,
  SelectFieldScrollDownButton,
};