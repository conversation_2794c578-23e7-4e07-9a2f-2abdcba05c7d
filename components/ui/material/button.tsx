import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "../utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        // Primary button - Orange filled (ONE per page for main CTAs)
        filled: "bg-primary-50 text-primary-100 hover:bg-primary-60 focus:bg-primary-60 active:bg-primary-40 shadow-md hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0",
        
        // Secondary button - Orange outline (for all other actions)
        outlined: "border-2 border-primary-50 bg-white text-primary-50 hover:bg-primary-95 hover:text-primary-40 focus:bg-primary-95 focus:text-primary-40 active:bg-primary-90 active:text-primary-30 hover:-translate-y-0.5 active:translate-y-0 shadow-sm hover:shadow-md",
        
        // Tonal button - For subtle actions
        tonal: "bg-primary-90 text-primary-30 hover:bg-primary-80 hover:text-primary-40 focus:bg-primary-80 focus:text-primary-40 active:bg-primary-70 active:text-primary-50",
        
        // Text/Ghost button - For minimal actions
        text: "bg-transparent text-primary-50 hover:bg-primary-95 hover:text-primary-40 focus:bg-primary-95 focus:text-primary-40 active:bg-primary-90 active:text-primary-30",
        
        // Destructive button - For dangerous actions
        destructive: "bg-error-50 text-error-95 hover:bg-error-60 focus:bg-error-60 active:bg-error-40 shadow-md hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0",
        
        // Ghost variant for minimal styling
        ghost: "bg-transparent text-on-surface hover:bg-primary-95 hover:text-primary-40 focus:bg-primary-95 focus:text-primary-40 active:bg-primary-90 active:text-primary-30",
      },
      size: {
        sm: "h-9 px-4 py-2 text-xs",
        default: "h-12 px-6 py-3 text-sm",
        lg: "h-14 px-8 py-4 text-base",
        icon: "h-12 w-12",
      },
      elevation: {
        none: "",
        low: "shadow-sm",
        medium: "shadow-md",
        high: "shadow-lg",
      },
    },
    compoundVariants: [
      {
        variant: "filled",
        className: "shadow-md hover:shadow-lg",
      },
      {
        variant: "outlined",
        className: "shadow-sm hover:shadow-md",
      },
      {
        variant: "destructive",
        className: "shadow-md hover:shadow-lg",
      },
    ],
    defaultVariants: {
      variant: "filled",
      size: "default",
      elevation: "none",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, elevation, asChild = false, loading, children, disabled, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size, elevation, className }),
          loading && "relative text-transparent",
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          </div>
        )}
        {children}
      </Comp>
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };

// Helper components for specific use cases that align with your design system

export const PrimaryButton = React.forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button variant="filled" ref={ref} {...props} />
);
PrimaryButton.displayName = "PrimaryButton";

export const SecondaryButton = React.forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button variant="outlined" ref={ref} {...props} />
);
SecondaryButton.displayName = "SecondaryButton";

export const GhostButton = React.forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button variant="ghost" ref={ref} {...props} />
);
GhostButton.displayName = "GhostButton";

export const DestructiveButton = React.forwardRef<HTMLButtonElement, Omit<ButtonProps, 'variant'>>(
  (props, ref) => <Button variant="destructive" ref={ref} {...props} />
);
DestructiveButton.displayName = "DestructiveButton";