import * as React from "react";
import { cn } from "../utils";

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  helperText?: string;
  error?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, label, helperText, error, value, defaultValue, ...props }, ref) => {
    const [focused, setFocused] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(false);
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);

    // Combine refs
    const combinedRef = React.useCallback((node: HTMLTextAreaElement) => {
      textareaRef.current = node;
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    }, [ref]);

    // Check for value on mount and when value prop changes
    React.useEffect(() => {
      const checkValue = () => {
        if (textareaRef.current) {
          const currentValue = textareaRef.current.value;
          setHasValue(currentValue && currentValue.length > 0);
        } else {
          // Fallback to checking props
          const propValue = value !== undefined ? value : defaultValue;
          setHasValue(propValue !== undefined && propValue !== null && String(propValue).length > 0);
        }
      };

      checkValue();
    }, [value, defaultValue]);

    const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setFocused(false);
      // Update hasValue on blur to ensure it's current
      setHasValue(e.target.value.length > 0);
      props.onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e);
    };

    const textareaId = React.useId();
    const isLabelFloating = focused || hasValue;

    return (
      <div className="relative w-full">
        <div
          className={cn(
            "relative rounded-lg border transition-all duration-200 bg-transparent",
            !error && !focused && "border-neutral-variant-50",
            !error && focused && "border-primary-50 border-2",
            error && !focused && "border-error-50",
            error && focused && "border-error-50 border-2",
            "hover:border-neutral-variant-60",
            focused && "hover:border-current"
          )}
        >
          <div className="relative">
            <textarea
              id={textareaId}
              ref={combinedRef}
              value={value}
              defaultValue={defaultValue}
              className={cn(
                "flex min-h-[80px] w-full bg-transparent px-4 text-neutral-10",
                "text-sm ring-offset-background placeholder:text-transparent",
                "focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
                "resize-none caret-primary-50",
                label ? "pt-6 pb-3" : "py-3",
                className
              )}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={handleChange}
              {...props}
            />

            {label && (
              <label
                htmlFor={textareaId}
                className={cn(
                  "absolute left-4 transition-all duration-200 pointer-events-none select-none origin-left",
                  "text-neutral-60",
                  // Positioning logic
                  isLabelFloating ? [
                    // Floating state
                    "top-2 scale-75 transform bg-background px-1 -translate-y-1/2",
                    !error && focused && "text-primary-50",
                    error && "text-error-50"
                  ] : [
                    // Resting state - positioned higher for textarea
                    "top-6 scale-100 -translate-y-1/2",
                    "text-neutral-60"
                  ]
                )}
              >
                {label}
              </label>
            )}
          </div>
        </div>

        {helperText && (
          <p
            className={cn(
              "mt-1 text-xs px-4 transition-colors",
              error ? "text-error-50" : "text-neutral-60"
            )}
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);
Textarea.displayName = "Textarea";

export { Textarea };