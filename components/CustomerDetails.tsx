import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Separator } from './ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from './ui/breadcrumb';
import { 
  User, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  CreditCard, 
  DollarSign, 
  Car, 
  Calendar, 
  Star, 
  FileText, 
  Target, 
  TrendingUp,
  Clock,
  Edit,
  MessageSquare,
  UserPlus,
  History,
  Activity,
  Home,
  Briefcase,
  Shield,
  ArrowLeft,
  Users
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  photo: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  customerType: string;
  status: string;
  creditRating: string;
  joinDate: string;
  lastContact: string;
  assignedDealer: string;
  salesRep: string;
  lifetimeValue: string;
  totalPurchases: number;
  vehicleInterests: string[];
  leadSource: string;
}

interface CustomerDetailsProps {
  customer: Customer | null;
  onBack: () => void;
}

export function CustomerDetails({ customer, onBack }: CustomerDetailsProps) {
  if (!customer) return null;

  // Enhanced customer data for Canadian car market context
  const enhancedCustomerData = {
    ...customer,
    // Additional Canadian-specific data
    preferredLanguage: customer.province === 'QC' ? 'French' : 'English',
    hstNumber: customer.customerType === 'Business' ? 'HST123456789RT0001' : null,
    driverLicenseProvince: customer.province,
    insuranceProvider: 'Intact Insurance',
    previousVehicles: [
      { year: '2019', make: 'Honda', model: 'CR-V', soldDate: '2023-03-15', salePrice: '$24,500' },
      { year: '2016', make: 'Toyota', model: 'Corolla', soldDate: '2021-08-20', salePrice: '$18,900' }
    ].slice(0, customer.totalPurchases),
    financingHistory: {
      currentFinancing: customer.totalPurchases > 0 ? 'RBC Auto Finance' : null,
      creditScore: getCreditScore(customer.creditRating),
      approvedAmount: '$75,000',
      interestRate: '4.99%'
    },
    communicationPreferences: {
      email: true,
      phone: true,
      sms: false,
      mail: customer.customerType === 'Business'
    },
    serviceHistory: [
      { date: '2024-01-10', type: 'Oil Change', location: customer.assignedDealer, cost: '$89' },
      { date: '2023-11-15', type: 'Winter Tire Install', location: customer.assignedDealer, cost: '$120' },
      { date: '2023-08-22', type: 'Safety Inspection', location: customer.assignedDealer, cost: '$145' }
    ].slice(0, customer.totalPurchases * 2),
    marketingConsent: {
      promotionalEmails: true,
      newModelAlerts: true,
      serviceReminders: true,
      eventInvitations: customer.status === 'VIP' || customer.status === 'Corporate'
    }
  };

  function getCreditScore(rating: string): string {
    switch (rating) {
      case 'A+': return '780-850';
      case 'A': return '720-779';
      case 'A-': return '680-719';
      case 'B+': return '640-679';
      case 'B': return '580-639';
      default: return '500-579';
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'prospect': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'vip': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'corporate': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getCreditColor = (rating: string) => {
    switch (rating) {
      case 'A+': return 'text-green-600 dark:text-green-400';
      case 'A': return 'text-green-500 dark:text-green-400';
      case 'A-': return 'text-green-400 dark:text-green-300';
      case 'B+': return 'text-yellow-600 dark:text-yellow-400';
      case 'B': return 'text-yellow-500 dark:text-yellow-300';
      default: return 'text-red-500 dark:text-red-400';
    }
  };

  const formatPhoneNumber = (phone: string) => {
    // Format Canadian phone numbers
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink onClick={onBack} className="flex items-center space-x-1 cursor-pointer hover:text-brand-orange transition-colors">
                <Users className="h-4 w-4" />
                <span>Customers</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="font-medium">{customer.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Customers</span>
        </Button>
      </div>

      {/* Header Section */}
      <div className="bg-gradient-to-r from-brand-orange to-brand-orange-light rounded-lg p-6 text-white">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-20 w-20 ring-4 ring-white shadow-lg">
              {customer.customerType === 'Business' ? (
                <AvatarImage src={customer.photo} alt={customer.name} />
              ) : (
                <ImageWithFallback 
                  src={customer.photo} 
                  alt={customer.name}
                  className="h-full w-full object-cover rounded-full"
                />
              )}
              <AvatarFallback className="text-lg bg-white text-brand-orange">
                {customer.customerType === 'Business' ? (
                  <Building className="h-8 w-8" />
                ) : (
                  customer.name.split(' ').map(n => n[0]).join('')
                )}
              </AvatarFallback>
            </Avatar>
            <div className="text-white">
              <h1 className="text-3xl font-bold mb-1">{customer.name}</h1>
              <div className="flex items-center space-x-3 mb-2">
                <Badge className={getStatusColor(customer.status)} variant="secondary">
                  {customer.status}
                </Badge>
                <Badge variant="outline" className="bg-white/20 text-white border-white/30">
                  {customer.customerType}
                </Badge>
                <span className="text-white/90 text-sm">ID: {customer.id}</span>
              </div>
              <div className="flex items-center space-x-4 text-sm text-white/80">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>Customer since {new Date(customer.joinDate).toLocaleDateString('en-CA')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>{customer.assignedDealer}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
              <MessageSquare className="h-4 w-4 mr-2" />
              Contact
            </Button>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="space-y-6">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-muted/30 p-1 rounded-lg">
            <TabsTrigger 
              value="overview" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <User className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger 
              value="financial" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <DollarSign className="h-4 w-4" />
              <span>Financial</span>
            </TabsTrigger>
            <TabsTrigger 
              value="history" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <History className="h-4 w-4" />
              <span>Purchase History</span>
            </TabsTrigger>
            <TabsTrigger 
              value="activity" 
              className="flex items-center space-x-2 data-[state=active]:bg-brand-orange data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-brand-orange/10 hover:text-brand-orange transition-all duration-200"
            >
              <Activity className="h-4 w-4" />
              <span>Activity</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Mail className="h-5 w-5 text-brand-orange" />
                    <span>Contact Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{customer.email}</p>
                        <p className="text-xs text-muted-foreground">Primary email</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{formatPhoneNumber(customer.phone)}</p>
                        <p className="text-xs text-muted-foreground">Mobile</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <Home className="h-4 w-4 text-muted-foreground mt-1" />
                      <div>
                        <p className="font-medium">{customer.address}</p>
                        <p className="text-sm text-muted-foreground">
                          {customer.city}, {customer.province} {customer.postalCode}
                        </p>
                        <p className="text-xs text-muted-foreground">Preferred language: {enhancedCustomerData.preferredLanguage}</p>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h4 className="font-medium mb-2">Communication Preferences</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {Object.entries(enhancedCustomerData.communicationPreferences).map(([key, value]) => (
                        <div key={key} className="flex items-center space-x-2">
                          <div className={`h-2 w-2 rounded-full ${value ? 'bg-green-500' : 'bg-gray-300'}`} />
                          <span className="text-sm capitalize">{key}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Customer Classification */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5 text-brand-orange" />
                    <span>Customer Classification</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Customer Type</p>
                      <div className="flex items-center space-x-2 mt-1">
                        {customer.customerType === 'Business' ? (
                          <Briefcase className="h-4 w-4 text-brand-orange" />
                        ) : (
                          <User className="h-4 w-4 text-brand-orange" />
                        )}
                        <span className="font-medium">{customer.customerType}</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Status</p>
                      <Badge className={getStatusColor(customer.status)} variant="secondary">
                        {customer.status}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Lead Source</p>
                      <p className="font-medium">{customer.leadSource}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Assigned Dealer</p>
                      <p className="font-medium text-brand-orange">{customer.assignedDealer}</p>
                    </div>
                  </div>
                  
                  {customer.customerType === 'Business' && enhancedCustomerData.hstNumber && (
                    <>
                      <Separator />
                      <div>
                        <p className="text-sm text-muted-foreground">HST Number</p>
                        <p className="font-mono text-sm">{enhancedCustomerData.hstNumber}</p>
                      </div>
                    </>
                  )}

                  <Separator />
                  
                  <div>
                    <p className="text-sm text-muted-foreground">Sales Representative</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <User className="h-4 w-4 text-brand-orange" />
                      <span className="font-medium">{customer.salesRep}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Vehicle Interests */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Car className="h-5 w-5 text-brand-orange" />
                    <span>Vehicle Interests</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Preferred Vehicle Types</p>
                      <div className="flex flex-wrap gap-2">
                        {customer.vehicleInterests.map((interest, index) => (
                          <Badge key={index} variant="outline" className="bg-brand-orange/10 text-brand-orange border-brand-orange/30">
                            {interest}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Separator />
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Additional Information</p>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Driver's License Province:</span>
                          <span className="font-medium">{enhancedCustomerData.driverLicenseProvince}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Insurance Provider:</span>
                          <span className="font-medium">{enhancedCustomerData.insuranceProvider}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-brand-orange" />
                    <span>Quick Stats</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {customer.lifetimeValue}
                      </p>
                      <p className="text-xs text-muted-foreground">Lifetime Value</p>
                    </div>
                    <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {customer.totalPurchases}
                      </p>
                      <p className="text-xs text-muted-foreground">Total Purchases</p>
                    </div>
                    <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="flex items-center justify-center space-x-1">
                        <CreditCard className="h-4 w-4" />
                        <span className={`font-bold text-lg ${getCreditColor(customer.creditRating)}`}>
                          {customer.creditRating}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">Credit Rating</p>
                    </div>
                    <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                      <div className="flex items-center justify-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span className="font-bold text-lg text-orange-600 dark:text-orange-400">
                          {Math.floor((new Date() - new Date(customer.lastContact)) / (1000 * 60 * 60 * 24))}d
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">Since Last Contact</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Financial Tab */}
          <TabsContent value="financial" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="h-5 w-5 text-brand-orange" />
                    <span>Credit Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Credit Rating</p>
                      <p className={`text-2xl font-bold ${getCreditColor(customer.creditRating)}`}>
                        {customer.creditRating}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Credit Score Range</p>
                      <p className="text-lg font-medium">{enhancedCustomerData.financingHistory.creditScore}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Approved Amount</p>
                      <p className="text-lg font-medium text-green-600">
                        {enhancedCustomerData.financingHistory.approvedAmount}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Interest Rate</p>
                      <p className="text-lg font-medium">{enhancedCustomerData.financingHistory.interestRate}</p>
                    </div>
                  </div>
                  {enhancedCustomerData.financingHistory.currentFinancing && (
                    <>
                      <Separator />
                      <div>
                        <p className="text-sm text-muted-foreground">Current Financing Partner</p>
                        <p className="font-medium">{enhancedCustomerData.financingHistory.currentFinancing}</p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5 text-brand-orange" />
                    <span>Financial Summary</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">Lifetime Value</span>
                      <span className="text-xl font-bold text-green-600 dark:text-green-400">
                        {customer.lifetimeValue}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">Average Purchase</span>
                      <span className="text-lg font-medium text-blue-600 dark:text-blue-400">
                        ${Math.round(parseFloat(customer.lifetimeValue.replace(/[$,]/g, '')) / Math.max(customer.totalPurchases, 1)).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">Total Purchases</span>
                      <span className="text-lg font-medium text-purple-600 dark:text-purple-400">
                        {customer.totalPurchases}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Purchase History Tab */}
          <TabsContent value="history" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Car className="h-5 w-5 text-brand-orange" />
                    <span>Previous Vehicle Purchases</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {enhancedCustomerData.previousVehicles.length > 0 ? (
                    <div className="space-y-4">
                      {enhancedCustomerData.previousVehicles.map((vehicle, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <Car className="h-8 w-8 text-brand-orange" />
                            <div>
                              <p className="font-medium">{vehicle.year} {vehicle.make} {vehicle.model}</p>
                              <p className="text-sm text-muted-foreground">
                                Sold on {new Date(vehicle.soldDate).toLocaleDateString('en-CA')}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-brand-orange">{vehicle.salePrice}</p>
                            <Badge variant="outline" className="text-xs">Completed</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Car className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No previous purchases on record</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-brand-orange" />
                    <span>Service History</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {enhancedCustomerData.serviceHistory.length > 0 ? (
                    <div className="space-y-3">
                      {enhancedCustomerData.serviceHistory.map((service, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                          <div>
                            <p className="font-medium">{service.type}</p>
                            <p className="text-sm text-muted-foreground">
                              {new Date(service.date).toLocaleDateString('en-CA')} • {service.location}
                            </p>
                          </div>
                          <p className="font-medium text-brand-orange">{service.cost}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No service history available</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-brand-orange" />
                  <span>Recent Activity</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-3 border-l-4 border-brand-orange bg-orange-50 dark:bg-orange-900/20 rounded-r-lg">
                    <Calendar className="h-5 w-5 text-brand-orange mt-0.5" />
                    <div>
                      <p className="font-medium">Last Contact</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(customer.lastContact).toLocaleDateString('en-CA')} - Sales follow-up call
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 p-3 border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-r-lg">
                    <User className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Customer Joined</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(customer.joinDate).toLocaleDateString('en-CA')} - via {customer.leadSource}
                      </p>
                    </div>
                  </div>

                  {enhancedCustomerData.previousVehicles.map((vehicle, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 border-l-4 border-green-500 bg-green-50 dark:bg-green-900/20 rounded-r-lg">
                      <Car className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <p className="font-medium">Vehicle Purchase</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(vehicle.soldDate).toLocaleDateString('en-CA')} - {vehicle.year} {vehicle.make} {vehicle.model} for {vehicle.salePrice}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-brand-orange" />
                  <span>Marketing Preferences</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(enhancedCustomerData.marketingConsent).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                      <div className={`h-3 w-3 rounded-full ${value ? 'bg-green-500' : 'bg-gray-300'}`} />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}