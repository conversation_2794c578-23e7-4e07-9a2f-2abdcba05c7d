/**
 * StepTracker Component - SubPrime Pro (Enhanced with Dashed Progress Line)
 * 
 * A sophisticated step tracker component with outlined icons, pale orange blur hover effects,
 * darker hover text contrast, and a center-aligned dashed progress line system.
 * 
 * ENHANCED PROGRESS LINE FEATURES:
 * - Center-aligned dashed segments between each pair of step icons
 * - 2 long dashes per segment with consistent spacing
 * - Equal spacing between all elements through to the 5th step
 * - State-based coloring (grey for pending, green for completed segments)
 * 
 * ENHANCED HOVER FEATURES:
 * - Very pale orange blur background on lowermost layer using CSS custom effects
 * - Darker gray label text on hover for enhanced contrast
 * - Smooth transitions with professional animation curves
 * - Layered visual hierarchy with proper z-indexing
 * 
 * OUTLINED ICON FEATURES:
 * - Enhanced stroke width (2px base, 2.5px for active/complete states)
 * - Consistent sizing across all step states (pending, active, complete)
 * - Proper Material Design 3 color integration with custom CSS classes
 * - Smooth transitions between states
 * - Accessibility considerations with proper contrast
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import React from 'react';
import { LucideIcon, Check } from 'lucide-react';
import { cn } from '../utils/cn';

export type StepState = 'pending' | 'active' | 'complete';

export interface Step {
  id: string;
  title: string;
  subtitle: string;
  icon: LucideIcon;
  state: StepState;
  stepNumber: number;
}

interface StepTrackerProps {
  steps: Step[];
  onStepClick?: (stepNumber: number) => void;
  className?: string;
  disabled?: boolean;
}

export const StepTracker: React.FC<StepTrackerProps> = ({ 
  steps, 
  onStepClick, 
  className,
  disabled = false 
}) => {
  const handleStepClick = (step: Step) => {
    if (onStepClick && !disabled) {
      onStepClick(step.stepNumber);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent, step: Step) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
      event.preventDefault();
      handleStepClick(step);
    }
  };

  // Calculate which progress segments should be completed (green)
  const getSegmentState = (segmentIndex: number): 'pending' | 'complete' => {
    // Segment index 0 is between step 1 and 2, etc.
    const nextStepNumber = segmentIndex + 2;
    const nextStep = steps.find(s => s.stepNumber === nextStepNumber);
    
    // If the next step is complete, this segment should be complete
    return nextStep && nextStep.state === 'complete' ? 'complete' : 'pending';
  };

  return (
    <div className={cn("step-tracker-container w-full", className)}>
      <div className="flex items-center justify-between relative max-w-4xl mx-auto">
        {steps.map((step, index) => {
          const isLastStep = index === steps.length - 1;
          
          return (
            <React.Fragment key={step.id}>
              {/* Step Item */}
              <div 
                className={cn(
                  "step-tracker-item relative flex flex-col items-center transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-50 focus-visible:ring-offset-2 rounded-lg",
                  !disabled && "cursor-pointer",
                  disabled && "cursor-not-allowed opacity-75"
                )}
                onClick={() => handleStepClick(step)}
                onKeyDown={(e) => handleKeyDown(e, step)}
                tabIndex={disabled ? -1 : 0}
                role="button"
                aria-label={`${step.title} - ${step.subtitle}`}
                aria-pressed={step.state === 'active'}
                aria-disabled={disabled}
                style={{
                  flex: '0 0 auto',
                  minWidth: '80px'
                }}
              >
                {/* Step circle with outlined icon styling using custom CSS classes */}
                <div
                  className={cn(
                    "step-tracker-icon relative z-20 flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300",
                    {
                      // Pending state - Outlined grey
                      'pending bg-neutral-95 border-neutral-variant-80 text-neutral-60': 
                        step.state === 'pending',
                      
                      // Active state - Outlined orange with enhanced styling
                      'active bg-primary-95 border-primary-50 text-primary-50': 
                        step.state === 'active',
                      
                      // Complete state - Outlined green
                      'complete bg-green-50 border-green-500 text-green-500': 
                        step.state === 'complete'
                    }
                  )}
                >
                  {step.state === 'complete' ? (
                    <Check className="h-6 w-6 stroke-[2.5]" />
                  ) : (
                    <step.icon className="h-6 w-6 stroke-[2]" />
                  )}
                </div>

                {/* Step labels using custom CSS classes for hover effects */}
                <div className="step-tracker-text mt-3 text-center min-w-0 max-w-32">
                  <div
                    className={cn(
                      "step-tracker-title font-medium text-sm leading-tight transition-all duration-300",
                      {
                        'text-neutral-30': step.state === 'pending',
                        'text-primary-50': step.state === 'active',
                        'text-green-600': step.state === 'complete'
                      }
                    )}
                  >
                    {step.title}
                  </div>
                  <div 
                    className={cn(
                      "step-tracker-subtitle text-xs mt-1 transition-all duration-300",
                      {
                        'text-neutral-60': step.state === 'pending',
                        'text-primary-60': step.state === 'active',
                        'text-green-500': step.state === 'complete'
                      }
                    )}
                  >
                    {step.subtitle}
                  </div>
                </div>

                {/* Focus indicator for keyboard navigation */}
                <div 
                  className="absolute inset-0 rounded-xl opacity-0 focus-visible:opacity-100 transition-all duration-200 pointer-events-none -m-2 ring-2 ring-primary-50 ring-offset-2"
                />
              </div>

              {/* Dashed Progress Segment between steps */}
              {!isLastStep && (
                <div 
                  className="flex-1 flex items-center justify-center px-4"
                  style={{
                    minWidth: '60px',
                    maxWidth: '120px'
                  }}
                >
                  <div className="flex items-center space-x-2">
                    {/* First Dash */}
                    <div
                      className={cn(
                        "h-0.5 rounded-full transition-all duration-500 ease-out",
                        {
                          'bg-neutral-variant-80': getSegmentState(index) === 'pending',
                          'bg-green-500': getSegmentState(index) === 'complete'
                        }
                      )}
                      style={{
                        width: '24px'
                      }}
                    />
                    
                    {/* Second Dash */}
                    <div
                      className={cn(
                        "h-0.5 rounded-full transition-all duration-500 ease-out",
                        {
                          'bg-neutral-variant-80': getSegmentState(index) === 'pending',
                          'bg-green-500': getSegmentState(index) === 'complete'
                        }
                      )}
                      style={{
                        width: '24px'
                      }}
                    />
                  </div>
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepTracker;