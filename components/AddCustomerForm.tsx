import { useState } from 'react';
import { TextField } from './ui/material/text-field';
import { SelectField, SelectFieldContent, SelectFieldItem, SelectFieldTrigger, SelectFieldValue } from './ui/material/select-field';
import { Textarea } from './ui/material/textarea';
import { Checkbox } from './ui/material/checkbox';
import { RadioGroup, RadioGroupItem } from './ui/material/radio-group';
import { Button } from './ui/button';
import { Label } from './ui/material/label';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { PhotoUpload } from './PhotoUpload';
import { toast } from 'sonner@2.0.3';
import exampleImage from 'figma:asset/2dfcbb2acb3128300204790ccaa26a59d40fe36c.png';
import { 
  ArrowLeft, 
  ArrowRight, 
  Users, 
  User, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Car, 
  CreditCard, 
  MessageSquare,
  Target,
  CheckCircle,
  Plus,
  X,
  Check,
  Calendar,
  FileText,
  Upload,
  Shield
} from 'lucide-react';

interface Location {
  id: string;
  name: string;
  count: number;
}

interface AddCustomerFormProps {
  onBack: () => void;
  onSubmit: (customerData: any) => void;
  locations: Location[];
}

interface FormData {
  // Personal Information
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  phone: string;
  email: string;
  
  // Photo & Documents
  photo: File | null;
  idType: string;
  idDocument: File | null;
  
  // Address Information
  suite: string;
  streetAddress: string;
  city: string;
  province: string;
  postalCode: string;
  
  // Customer Status
  isActiveCustomer: 'Yes' | 'No';
  
  // Tax Exemption
  statusCard: File | null;
  
  // System fields
  assignedDealer: string;
}

const CANADIAN_PROVINCES = [
  { code: 'AB', name: 'Alberta' },
  { code: 'BC', name: 'British Columbia' },
  { code: 'MB', name: 'Manitoba' },
  { code: 'NB', name: 'New Brunswick' },
  { code: 'NL', name: 'Newfoundland and Labrador' },
  { code: 'NT', name: 'Northwest Territories' },
  { code: 'NS', name: 'Nova Scotia' },
  { code: 'NU', name: 'Nunavut' },
  { code: 'ON', name: 'Ontario' },
  { code: 'PE', name: 'Prince Edward Island' },
  { code: 'QC', name: 'Quebec' },
  { code: 'SK', name: 'Saskatchewan' },
  { code: 'YT', name: 'Yukon' }
];

const CANADIAN_ID_TYPES = [
  { value: 'drivers_license', label: 'Driver\'s License' },
  { value: 'passport', label: 'Canadian Passport' },
  { value: 'health_card', label: 'Provincial Health Card' },
  { value: 'sin_card', label: 'Social Insurance Number Card' },
  { value: 'birth_certificate', label: 'Birth Certificate' },
  { value: 'citizenship_certificate', label: 'Citizenship Certificate' },
  { value: 'permanent_resident_card', label: 'Permanent Resident Card' },
  { value: 'indian_status_card', label: 'Indian Status Card' },
  { value: 'nexus_card', label: 'NEXUS Card' },
  { value: 'enhanced_drivers_license', label: 'Enhanced Driver\'s License' }
];

export function AddCustomerForm({ onBack, onSubmit, locations }: AddCustomerFormProps) {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    phone: '',
    email: '',
    photo: null,
    idType: '',
    idDocument: null,
    suite: '',
    streetAddress: '',
    city: '',
    province: '',
    postalCode: '',
    isActiveCustomer: 'Yes',
    statusCard: null,
    assignedDealer: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // File upload handlers
  const handlePhotoUpload = (file: File | null) => {
    updateFormData({ photo: file });
  };

  const handleIdDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    updateFormData({ idDocument: file });
  };

  const handleStatusCardUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    updateFormData({ statusCard: file });
  };

  // Date validation
  const validateDateOfBirth = (date: string): boolean => {
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1 >= 18;
    }
    return age >= 18;
  };

  const updateFormData = (updates: Partial<FormData>) => {
    try {
      setFormData(prev => ({ ...prev, ...updates }));
      // Clear related errors when user starts typing
      const newErrors = { ...errors };
      Object.keys(updates).forEach(key => {
        if (newErrors[key]) {
          delete newErrors[key];
        }
      });
      setErrors(newErrors);
    } catch (error) {
      console.error('Error updating form data:', error);
      toast.error(
        'Form update error',
        {
          description: 'Unable to update form data. Please refresh the page and try again.',
          duration: 4000,
        }
      );
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Personal Information validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters long';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters long';
    }

    if (!formData.dateOfBirth.trim()) {
      newErrors.dateOfBirth = 'Date of birth is required';
    } else if (!validateDateOfBirth(formData.dateOfBirth)) {
      newErrors.dateOfBirth = 'Customer must be at least 18 years old';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Invalid Canadian phone number format';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    // ID Type validation
    if (!formData.idType) {
      newErrors.idType = 'ID type is required';
    }

    // Address validation
    if (!formData.streetAddress.trim()) {
      newErrors.streetAddress = 'Street address is required';
    } else if (formData.streetAddress.trim().length < 5) {
      newErrors.streetAddress = 'Street address must be at least 5 characters long';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    } else if (formData.city.trim().length < 2) {
      newErrors.city = 'City name must be at least 2 characters long';
    }

    if (!formData.province) {
      newErrors.province = 'Province is required';
    }

    if (!formData.postalCode.trim()) {
      newErrors.postalCode = 'Postal code is required';
    } else if (!/^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/.test(formData.postalCode)) {
      newErrors.postalCode = 'Invalid Canadian postal code format (e.g., M5H 2M9)';
    }

    if (!formData.assignedDealer) {
      newErrors.assignedDealer = 'Assigned dealer is required';
    }

    setErrors(newErrors);
    
    // Show error toasts for validation failures
    if (Object.keys(newErrors).length > 0) {
      toast.error(
        `Please fix the following errors:`,
        {
          description: Object.values(newErrors).slice(0, 3).join(', ') + (Object.values(newErrors).length > 3 ? '...' : ''),
          duration: 5000,
        }
      );
    }
    
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Additional business logic validation
      const duplicateEmailCheck = formData.email.toLowerCase();
      
      // Mock check for duplicate email (in real app, this would be an API call)
      if (duplicateEmailCheck === '<EMAIL>') {
        toast.error(
          'Customer already exists',
          {
            description: `A customer with email ${formData.email} already exists in the system.`,
            duration: 5000,
          }
        );
        return;
      }

      // Check for duplicate phone number
      if (formData.phone === '(*************') {
        toast.error(
          'Duplicate phone number detected',
          {
            description: 'This phone number is already associated with another customer.',
            duration: 5000,
          }
        );
        return;
      }

      // Create photo URL if not uploaded
      const photoUrl = formData.photo 
        ? URL.createObjectURL(formData.photo)
        : `https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face&${Date.now()}`;

      const customerData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        name: `${formData.firstName} ${formData.lastName}`, // Legacy compatibility
        dateOfBirth: formData.dateOfBirth,
        phone: formData.phone.replace(/\D/g, '').replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3'), // Format phone
        email: formData.email.toLowerCase(), // Normalize email
        photo: photoUrl,
        idType: formData.idType,
        idDocument: formData.idDocument,
        suite: formData.suite,
        streetAddress: formData.streetAddress,
        address: formData.streetAddress, // Legacy compatibility
        city: formData.city,
        province: formData.province,
        postalCode: formData.postalCode.toUpperCase(), // Normalize postal code
        isActiveCustomer: formData.isActiveCustomer,
        statusCard: formData.statusCard,
        assignedDealer: formData.assignedDealer,
        status: formData.isActiveCustomer === 'Yes' ? 'Active' : 'Inactive',
      };

      onSubmit(customerData);
      
    } catch (error) {
      console.error('Error creating customer:', error);
      toast.error(
        'Failed to create customer',
        {
          description: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
          duration: 6000,
        }
      );
    }
  };

  const isFormComplete = () => {
    return formData.firstName && 
           formData.lastName && 
           formData.dateOfBirth && 
           formData.phone && 
           formData.email && 
           formData.idType && 
           formData.streetAddress && 
           formData.city && 
           formData.province && 
           formData.postalCode && 
           formData.assignedDealer;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-on-surface">Add New Customer</h1>
          <p className="text-sm text-muted-foreground">
            Enter customer information based on user testing feedback
          </p>
        </div>
        <Badge 
          variant="outline" 
          className="badge-orange px-3 py-1.5 font-medium hover-scale-sm transition-transform"
        >
          Enhanced Form Fields
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Form - 2 columns */}
        <div className="lg:col-span-2 space-y-8">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5 text-brand-orange" />
                <span>Personal Information</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Enter the customer's basic personal information
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <TextField
                  label="First Name *"
                  value={formData.firstName}
                  onChange={(e) => updateFormData({ firstName: e.target.value })}
                  error={!!errors.firstName}
                  helperText={errors.firstName}
                  placeholder="John"
                  required
                />

                <TextField
                  label="Last Name *"
                  value={formData.lastName}
                  onChange={(e) => updateFormData({ lastName: e.target.value })}
                  error={!!errors.lastName}
                  helperText={errors.lastName}
                  placeholder="Smith"
                  required
                />

                <TextField
                  label="Date of Birth *"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => updateFormData({ dateOfBirth: e.target.value })}
                  error={!!errors.dateOfBirth}
                  helperText={errors.dateOfBirth || "Must be 18 years or older"}
                  leadingIcon={<Calendar className="h-4 w-4" />}
                  required
                />

                <TextField
                  label="Phone Number *"
                  value={formData.phone}
                  onChange={(e) => updateFormData({ phone: e.target.value })}
                  placeholder="(*************"
                  error={!!errors.phone}
                  helperText={errors.phone}
                  leadingIcon={<Phone className="h-4 w-4" />}
                  required
                />

                <div className="md:col-span-2">
                  <TextField
                    label="Email Address *"
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData({ email: e.target.value })}
                    error={!!errors.email}
                    helperText={errors.email}
                    placeholder="<EMAIL>"
                    leadingIcon={<Mail className="h-4 w-4" />}
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Photo & Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-brand-orange" />
                <span>Photo & Documents</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Upload customer photo and identification documents
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-6">
                {/* Photo Upload */}
                <div className="space-y-3">
                  <Label>Customer Photo</Label>
                  <div className="bg-muted/50 rounded-lg p-6 border-2 border-dashed border-border hover:border-brand-orange transition-colors">
                    <PhotoUpload
                      onFileSelect={handlePhotoUpload}
                      currentFile={formData.photo}
                      accept="image/*"
                      maxSize={5}
                    />
                  </div>
                </div>

                <Separator />

                {/* ID Type Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <SelectField value={formData.idType} onValueChange={(value) => updateFormData({ idType: value })}>
                    <SelectFieldTrigger label="ID Type *" error={!!errors.idType} helperText={errors.idType}>
                      <SelectFieldValue placeholder="Select Canadian ID type" />
                    </SelectFieldTrigger>
                    <SelectFieldContent>
                      {CANADIAN_ID_TYPES.map((idType) => (
                        <SelectFieldItem key={idType.value} value={idType.value}>
                          {idType.label}
                        </SelectFieldItem>
                      ))}
                    </SelectFieldContent>
                  </SelectField>

                  {/* ID Document Upload */}
                  <div className="space-y-3">
                    <Label>ID Document Upload</Label>
                    <div className="relative">
                      <input
                        type="file"
                        id="id-document"
                        onChange={handleIdDocumentUpload}
                        accept=".pdf,.jpg,.jpeg,.png"
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      />
                      <div className="bg-input-background border border-input-idle-border rounded-lg px-4 py-3 flex items-center justify-between hover:border-input-hover-border transition-colors">
                        <span className="text-sm text-muted-foreground">
                          {formData.idDocument ? formData.idDocument.name : 'Choose file...'}
                        </span>
                        <Upload className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      PDF, JPG, PNG up to 10MB
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5 text-brand-orange" />
                <span>Address Information</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Enter the customer's Canadian address details
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <TextField
                  label="Suite/Unit"
                  value={formData.suite}
                  onChange={(e) => updateFormData({ suite: e.target.value })}
                  placeholder="Unit 123"
                />

                <div className="md:col-span-2">
                  <TextField
                    label="Street Address *"
                    value={formData.streetAddress}
                    onChange={(e) => updateFormData({ streetAddress: e.target.value })}
                    error={!!errors.streetAddress}
                    helperText={errors.streetAddress}
                    placeholder="123 Main Street"
                    required
                  />
                </div>

                <TextField
                  label="City *"
                  value={formData.city}
                  onChange={(e) => updateFormData({ city: e.target.value })}
                  placeholder="Toronto"
                  error={!!errors.city}
                  helperText={errors.city}
                  required
                />

                <SelectField value={formData.province} onValueChange={(value) => updateFormData({ province: value })}>
                  <SelectFieldTrigger label="Province *" error={!!errors.province} helperText={errors.province}>
                    <SelectFieldValue placeholder="Select province" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    {CANADIAN_PROVINCES.map((province) => (
                      <SelectFieldItem key={province.code} value={province.code}>
                        {province.name}
                      </SelectFieldItem>
                    ))}
                  </SelectFieldContent>
                </SelectField>

                <TextField
                  label="Postal Code *"
                  value={formData.postalCode}
                  onChange={(e) => updateFormData({ postalCode: e.target.value.toUpperCase() })}
                  placeholder="M5H 2M9"
                  error={!!errors.postalCode}
                  helperText={errors.postalCode}
                  required
                />
              </div>

              <SelectField value={formData.assignedDealer} onValueChange={(value) => updateFormData({ assignedDealer: value })}>
                <SelectFieldTrigger label="Assigned Dealer *" error={!!errors.assignedDealer} helperText={errors.assignedDealer}>
                  <SelectFieldValue placeholder="Select dealer location" />
                </SelectFieldTrigger>
                <SelectFieldContent>
                  {locations.filter(loc => loc.id !== 'all').map((location) => (
                    <SelectFieldItem key={location.id} value={location.name}>
                      {location.name}
                    </SelectFieldItem>
                  ))}
                </SelectFieldContent>
              </SelectField>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - 1 column */}
        <div className="space-y-6">
          {/* Enhanced Customer Status */}
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-2">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-brand-orange/10">
                  <CheckCircle className="h-4 w-4 text-brand-orange" />
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Customer Status</h3>
                  <p className="text-xs text-muted-foreground">Current customer status verification</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <Label className="text-sm font-medium">Is this customer active? *</Label>
                <div className="bg-neutral-50 border border-neutral-200 rounded-lg p-4">
                  <RadioGroup
                    value={formData.isActiveCustomer}
                    onValueChange={(value: 'Yes' | 'No') => updateFormData({ isActiveCustomer: value })}
                    className="space-y-4"
                  >
                    <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-primary-95 transition-colors">
                      <RadioGroupItem value="Yes" id="active-yes" />
                      <Label htmlFor="active-yes" className="cursor-pointer text-sm font-medium flex-1">
                        Yes - Active Customer
                      </Label>
                      {formData.isActiveCustomer === 'Yes' && (
                        <div className="flex items-center justify-center w-5 h-5 rounded-full bg-green-100">
                          <Check className="h-3 w-3 text-green-600" />
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-100 transition-colors">
                      <RadioGroupItem value="No" id="active-no" />
                      <Label htmlFor="active-no" className="cursor-pointer text-sm font-medium flex-1">
                        No - Inactive
                      </Label>
                      {formData.isActiveCustomer === 'No' && (
                        <div className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-100">
                          <X className="h-3 w-3 text-gray-600" />
                        </div>
                      )}
                    </div>
                  </RadioGroup>
                </div>
                
                {/* Status Information */}
                <div className={`rounded-lg px-3 py-2 border ${
                  formData.isActiveCustomer === 'Yes' 
                    ? 'bg-green-50 border-green-200' 
                    : formData.isActiveCustomer === 'No' 
                    ? 'bg-yellow-50 border-yellow-200' 
                    : 'bg-neutral-50 border-neutral-200'
                }`}>
                  <p className={`text-xs ${
                    formData.isActiveCustomer === 'Yes' 
                      ? 'text-green-700' 
                      : formData.isActiveCustomer === 'No' 
                      ? 'text-yellow-700' 
                      : 'text-neutral-600'
                  }`}>
                    {formData.isActiveCustomer === 'Yes' 
                      ? '✓ Active customers receive priority service and full access to deals'
                      : formData.isActiveCustomer === 'No' 
                      ? '⚠ Inactive customers may have limited access to certain services'
                      : 'Please select customer status to continue'
                    }
                  </p>
                </div>
                
                {/* Integrated Status Card Upload */}
                <Separator className="my-4" />
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <Label className="text-sm font-medium">Tax Exemption Status</Label>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="relative">
                      <input
                        type="file"
                        id="status-card"
                        onChange={handleStatusCardUpload}
                        accept=".pdf,.jpg,.jpeg,.png"
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      />
                      <div className="bg-input-background border border-input-idle-border rounded-lg px-4 py-3 flex items-center justify-between hover:border-input-hover-border transition-colors cursor-pointer">
                        <span className="text-sm text-muted-foreground">
                          {formData.statusCard ? formData.statusCard.name : 'Upload status card (optional)'}
                        </span>
                        <Upload className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                    
                    {/* Status Card Feedback */}
                    {formData.statusCard ? (
                      <div className="bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                        <div className="flex items-start space-x-2">
                          <div className="flex items-center justify-center w-4 h-4 rounded-full bg-green-100 mt-0.5">
                            <Check className="h-3 w-3 text-green-600" />
                          </div>
                          <div className="flex-1">
                            <p className="text-xs font-medium text-green-800">
                              Status Card Uploaded
                            </p>
                            <p className="text-xs text-green-600 mt-0.5">
                              Customer eligible for tax exemption verification
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-neutral-50 border border-neutral-200 rounded-lg px-3 py-2">
                        <p className="text-xs text-neutral-600">
                          <strong>Optional:</strong> Upload First Nations Status Card, Métis Nation Card, 
                          or other tax exemption documents if applicable
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Customer Status Preview */}
          <Card>
            <CardHeader className="pb-4">
              <h3 className="font-semibold text-sm">Status Verification</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <img 
                  src={exampleImage} 
                  alt="Customer Status Verification Interface" 
                  className="w-full rounded-lg border shadow-sm"
                />
                <p className="text-xs text-muted-foreground">
                  Enhanced customer status verification with radio button interface
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Customers</span>
        </Button>

        <div className="flex space-x-3">
          <Button variant="outline" onClick={onBack}>
            Cancel
          </Button>

          <Button 
            onClick={handleSubmit}
            disabled={!isFormComplete()}
            className="bg-brand-orange text-white hover:bg-brand-orange-dark flex items-center space-x-2"
          >
            <Check className="h-4 w-4" />
            <span>Add Customer</span>
          </Button>
        </div>
      </div>
    </div>
  );
}