/**
 * DealDetailsView Component - Read-Only Deal Information Display
 * 
 * Comprehensive read-only view displaying complete deal information using
 * the review step layout pattern. Features professional aesthetic with
 * Material Design 3 integration and enhanced visual hierarchy.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { Deal } from '../types';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Separator } from './ui/separator';
import {
  UserCheck,
  Users,
  Car,
  FileText,
  MapPin,
  Phone,
  Mail,
  Calendar,
  DollarSign,
  CheckCircle,
  AlertCircle,
  XCircle,
  Edit,
  Clock,
  ArrowLeftRight,
  CreditCard,
  Building,
  User,
  IdCard,
  Home
} from 'lucide-react';
import { 
  formatCurrency, 
  formatName, 
  getInitials, 
  formatPhone, 
  formatEmail 
} from '../utils/stringUtils';

interface DealDetailsViewProps {
  deal: Deal;
}

export function DealDetailsView({ deal }: DealDetailsViewProps) {
  // Get status styling with outline variants
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-800 border-green-500 text-base px-4 py-2">
            <CheckCircle className="h-4 w-4 mr-2" />
            Approved
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-500 text-base px-4 py-2">
            <AlertCircle className="h-4 w-4 mr-2" />
            Pending
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-800 border-red-500 text-base px-4 py-2">
            <XCircle className="h-4 w-4 mr-2" />
            Rejected
          </Badge>
        );
      case 'draft':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-800 border-gray-500 text-base px-4 py-2">
            <Edit className="h-4 w-4 mr-2" />
            Draft
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-neutral-50 text-neutral-800 border-neutral-500 text-base px-4 py-2">
            <Clock className="h-4 w-4 mr-2" />
            {status}
          </Badge>
        );
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Deal Header */}
      <div className="bg-gradient-to-br from-primary-99 to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-lg bg-primary-90">
              <FileText className="h-6 w-6 text-primary-50" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-on-surface">Deal Summary</h1>
              <p className="text-base text-muted-foreground mt-1">
                Complete deal information and details
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {getStatusBadge(deal.status || 'pending')}
            <Badge variant="outline" className="px-3 py-2 text-sm">
              ID: {deal.id.split('-')[1]?.slice(-6) || deal.id.slice(-6)}
            </Badge>
          </div>
        </div>

        {/* Enhanced Deal Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
          <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-primary-50" />
              <span className="text-sm font-medium text-muted-foreground">Deal Value</span>
            </div>
            <p className="text-2xl font-bold text-primary-50 mt-1">
              {formatCurrency(deal.dealValue || 0)}
            </p>
            {deal.transactionSummary?.cashPrice && (
              <p className="text-xs text-muted-foreground mt-1">
                Vehicle: {formatCurrency(deal.transactionSummary.cashPrice)}
              </p>
            )}
          </div>

          <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-secondary-50" />
              <span className="text-sm font-medium text-muted-foreground">Approvals</span>
            </div>
            <p className="text-2xl font-bold text-secondary-50 mt-1">
              {deal.approvalEntities?.length || 0}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {deal.priority || 'Normal'} Priority
            </p>
          </div>

          <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
            <div className="flex items-center space-x-2">
              <ArrowLeftRight className="h-5 w-5 text-tertiary-50" />
              <span className="text-sm font-medium text-muted-foreground">Trade-in</span>
            </div>
            <p className="text-2xl font-bold text-tertiary-50 mt-1">
              {deal.transactionSummary?.hasTradeIn ? 'Yes' : 'No'}
            </p>
            {deal.transactionSummary?.tradeInAllowance && deal.transactionSummary.tradeInAllowance > 0 && (
              <p className="text-xs text-muted-foreground mt-1">
                Value: {formatCurrency(deal.transactionSummary.tradeInAllowance)}
              </p>
            )}
          </div>

          <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-neutral-60" />
              <span className="text-sm font-medium text-muted-foreground">Location</span>
            </div>
            <p className="text-lg font-semibold text-neutral-60 mt-1">
              {deal.location || 'Unknown'}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {deal.transactionSummary?.province || deal.customerDetails?.province || 'N/A'}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Customer Information Card */}
        <Card className="border-2 border-outline-variant shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2.5 rounded-lg bg-primary-90">
                <UserCheck className="h-6 w-6 text-primary-50" />
              </div>
              <h2 className="text-xl font-bold text-on-surface">Customer Information</h2>
            </div>

            <div className="space-y-6">
              {/* Customer Overview */}
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16 border-2 border-outline-variant">
                  <AvatarFallback className="text-lg font-semibold">
                    {getInitials(deal.customerName || 'Unknown')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-on-surface">
                    {formatName(deal.customerName || 'Unknown Customer')}
                  </h3>
                  <p className="text-sm text-muted-foreground">Primary Customer</p>
                </div>
              </div>

              {/* Enhanced Customer Details */}
              <div className="space-y-4">
                <Separator />
                
                {/* Personal Details from customerDetails or approvalEntities */}
                <div className="bg-surface-container-lowest rounded-lg p-4 space-y-3">
                  <h4 className="font-semibold text-base text-on-surface flex items-center">
                    <User className="h-4 w-4 mr-2 text-primary-50" />
                    Personal Details
                  </h4>
                  
                  <div className="grid grid-cols-1 gap-3">
                    {/* Use enhanced customerDetails first, then fallback to approvalEntities */}
                    {(deal.customerDetails?.emailAddress || deal.approvalEntities?.[0]?.email) && (
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {formatEmail(deal.customerDetails?.emailAddress || deal.approvalEntities?.[0]?.email || '')}
                        </span>
                      </div>
                    )}
                    
                    {(deal.customerDetails?.phoneNumber || deal.approvalEntities?.[0]?.phone) && (
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {formatPhone(deal.customerDetails?.phoneNumber || deal.approvalEntities?.[0]?.phone || '')}
                        </span>
                      </div>
                    )}
                    
                    {(deal.customerDetails?.dateOfBirth || deal.approvalEntities?.[0]?.dateOfBirth) && (
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          Born: {new Date(deal.customerDetails?.dateOfBirth || deal.approvalEntities?.[0]?.dateOfBirth || '').toLocaleDateString()}
                        </span>
                      </div>
                    )}
                    
                    {deal.customerDetails?.status && (
                      <div className="flex items-center space-x-2">
                        <UserCheck className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          Status: <span className="font-medium">{deal.customerDetails.status === 'Yes' ? 'Active Customer' : 'Inactive'}</span>
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Enhanced ID Information */}
                <div className="bg-surface-container-lowest rounded-lg p-4 space-y-3">
                  <h4 className="font-semibold text-base text-on-surface flex items-center">
                    <IdCard className="h-4 w-4 mr-2 text-secondary-40" />
                    Identification
                  </h4>
                  
                  <div className="grid grid-cols-1 gap-3">
                    {deal.customerDetails?.idType && (
                      <div className="flex items-center space-x-2">
                        <IdCard className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          ID Type: <span className="font-medium">{deal.customerDetails.idType}</span>
                        </span>
                      </div>
                    )}
                    
                    {deal.approvalEntities?.[0]?.drivingLicenseNumber && (
                      <div className="flex items-center space-x-2">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          DL: {deal.approvalEntities[0].drivingLicenseNumber}
                        </span>
                      </div>
                    )}
                    
                    {deal.approvalEntities?.[0]?.sin && (
                      <div className="flex items-center space-x-2">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          SIN: {deal.approvalEntities[0].sin}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Enhanced Address Information */}
                {(deal.customerDetails?.fullAddress || deal.approvalEntities?.[0]?.address) && (
                  <div className="bg-surface-container-lowest rounded-lg p-4 space-y-3">
                    <h4 className="font-semibold text-base text-on-surface flex items-center">
                      <Home className="h-4 w-4 mr-2 text-tertiary-40" />
                      Address
                    </h4>
                    
                    <div className="text-sm text-muted-foreground">
                      {deal.customerDetails?.fullAddress ? (
                        <p>{deal.customerDetails.fullAddress}</p>
                      ) : (
                        <>
                          <p>{deal.approvalEntities?.[0]?.address}</p>
                          <p>
                            {deal.approvalEntities?.[0]?.city}, {deal.approvalEntities?.[0]?.province} {deal.approvalEntities?.[0]?.postalCode}
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                )}

                {/* Enhanced Employment Information */}
                {deal.approvalEntities?.[0]?.employer && (
                  <div className="bg-surface-container-lowest rounded-lg p-4 space-y-3">
                    <h4 className="font-semibold text-base text-on-surface flex items-center">
                      <Building className="h-4 w-4 mr-2 text-primary-50" />
                      Employment
                    </h4>
                    
                    <div className="grid grid-cols-1 gap-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Employer:</span>
                        <span className="text-sm font-medium">{deal.approvalEntities[0].employer}</span>
                      </div>
                      {deal.approvalEntities[0].jobTitle && (
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Position:</span>
                          <span className="text-sm font-medium">{deal.approvalEntities[0].jobTitle}</span>
                        </div>
                      )}
                      {deal.approvalEntities[0].employmentStatus && (
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Status:</span>
                          <span className="text-sm font-medium">{deal.approvalEntities[0].employmentStatus}</span>
                        </div>
                      )}
                      {deal.approvalEntities[0].annualIncome && (
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Annual Income:</span>
                          <span className="text-sm font-semibold text-primary-50">
                            {formatCurrency(deal.approvalEntities[0].annualIncome)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Approval Entities Card */}
        <Card className="border-2 border-outline-variant shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2.5 rounded-lg bg-secondary-90">
                <Users className="h-6 w-6 text-secondary-40" />
              </div>
              <h2 className="text-xl font-bold text-on-surface">
                Approval Entities ({deal.approvalEntities?.length || 0})
              </h2>
            </div>

            <div className="space-y-4">
              {deal.approvalEntities && deal.approvalEntities.length > 0 ? (
                deal.approvalEntities.map((entity, index) => (
                  <div key={entity.id || index} className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10 border border-outline-variant">
                          <AvatarFallback className="text-sm font-semibold">
                            {getInitials(`${entity.firstName} ${entity.lastName}`)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h4 className="font-semibold text-base text-on-surface">
                            {entity.firstName} {entity.lastName}
                          </h4>
                          <p className="text-sm text-muted-foreground">{entity.relationship}</p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        Entity {index + 1}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 gap-2">
                      {entity.email && (
                        <div className="flex items-center space-x-2">
                          <Mail className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs">{entity.email}</span>
                        </div>
                      )}
                      {entity.phone && (
                        <div className="flex items-center space-x-2">
                          <Phone className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs">{entity.phone}</span>
                        </div>
                      )}
                      {entity.annualIncome && (
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs font-medium">
                            {formatCurrency(entity.annualIncome)} annually
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground">No approval entities found</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Vehicle and Financial Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Vehicle Information Card */}
        <Card className="border-2 border-outline-variant shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2.5 rounded-lg bg-primary-90">
                <Car className="h-6 w-6 text-primary-50" />
              </div>
              <h2 className="text-xl font-bold text-on-surface">Vehicle Information</h2>
            </div>

            <div className="space-y-4">
              {/* Enhanced Selected Vehicle Information */}
              <div className="bg-gradient-to-br from-primary-95 to-surface-bright rounded-lg p-4 border border-outline-variant">
                <div className="flex items-center space-x-3 mb-3">
                  <Car className="h-5 w-5 text-primary-50" />
                  <h3 className="font-semibold text-lg text-on-surface">Selected Vehicle</h3>
                </div>
                
                {deal.selectedVehicleDetails && deal.selectedVehicleDetails.length > 0 ? (
                  <div className="space-y-3">
                    {deal.selectedVehicleDetails.map((vehicle, index) => (
                      <div key={vehicle.id || index} className="bg-surface-container-lowest rounded-lg p-3 border border-outline-variant">
                        <div className="grid grid-cols-1 gap-2">
                          <p className="text-base font-semibold text-on-surface">
                            {vehicle.year} {vehicle.make} {vehicle.model}
                          </p>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">VIN:</span>
                              <span className="ml-2 font-mono">{vehicle.vin}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Mileage:</span>
                              <span className="ml-2">{vehicle.mileage}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Exterior:</span>
                              <span className="ml-2">{vehicle.exterior}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Interior:</span>
                              <span className="ml-2">{vehicle.interior}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Transmission:</span>
                              <span className="ml-2">{vehicle.transmission}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Fuel Type:</span>
                              <span className="ml-2">{vehicle.fuelType}</span>
                            </div>
                          </div>
                          <div className="mt-2 pt-2 border-t border-outline-variant">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-muted-foreground">Vehicle Price:</span>
                              <span className="text-lg font-bold text-primary-50">{vehicle.price}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-base font-medium text-on-surface">
                    {deal.vehicleInfo || 'Vehicle information not available'}
                  </p>
                )}
                
                <div className="mt-4 pt-3 border-t border-outline-variant">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Total Deal Value:</span>
                    <span className="text-xl font-bold text-primary-50">
                      {formatCurrency(deal.dealValue || 0)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Enhanced Trade-in Information */}
              {deal.tradeInVehicle ? (
                <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                  <div className="flex items-center space-x-3 mb-3">
                    <ArrowLeftRight className="h-5 w-5 text-secondary-50" />
                    <h3 className="font-semibold text-base text-on-surface">Trade-in Vehicle</h3>
                  </div>
                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Vehicle:</span>
                      <span className="font-medium">
                        {deal.tradeInVehicle.year} {deal.tradeInVehicle.make} {deal.tradeInVehicle.model}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Mileage:</span>
                      <span>{deal.tradeInVehicle.mileage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Color:</span>
                      <span>{deal.tradeInVehicle.color}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Trade Allowance:</span>
                      <span className="font-semibold text-secondary-50">
                        {formatCurrency(deal.tradeInVehicle.tradeAllowance || 0)}
                      </span>
                    </div>
                    {deal.tradeInVehicle.outstandingLoan && deal.tradeInVehicle.outstandingLoan > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Outstanding Loan:</span>
                        <span className="text-red-600">
                          {formatCurrency(deal.tradeInVehicle.outstandingLoan)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                  <div className="flex items-center space-x-3 mb-3">
                    <ArrowLeftRight className="h-5 w-5 text-muted-foreground" />
                    <h3 className="font-semibold text-base text-muted-foreground">No Trade-in</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    No trade-in vehicle for this deal
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Deal Notes Card */}
        <Card className="border-2 border-outline-variant shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2.5 rounded-lg bg-tertiary-90">
                <FileText className="h-6 w-6 text-tertiary-40" />
              </div>
              <h2 className="text-xl font-bold text-on-surface">
                Deal Notes ({deal.notes?.length || 0})
              </h2>
            </div>

            <div className="space-y-4">
              {/* Deal Notes from Build Deal step */}
              {deal.dealNotes && (
                <div className="bg-gradient-to-br from-primary-95 to-surface-bright rounded-lg p-4 border border-outline-variant">
                  <div className="flex items-center space-x-3 mb-3">
                    <FileText className="h-5 w-5 text-primary-50" />
                    <h4 className="font-semibold text-base text-on-surface">Deal Summary Notes</h4>
                  </div>
                  <p className="text-sm text-on-surface leading-relaxed whitespace-pre-wrap">
                    {deal.dealNotes}
                  </p>
                </div>
              )}
              
              {/* Step-by-step Notes */}
              {deal.notes && deal.notes.length > 0 ? (
                deal.notes.map((note, index) => (
                  <div key={note.id || index} className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline" className="text-xs">
                        Step {note.step}: {note.stepName}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {note.createdAt ? new Date(note.createdAt).toLocaleDateString() : 'No date'}
                      </span>
                    </div>
                    <p className="text-sm text-on-surface leading-relaxed">
                      {note.content}
                    </p>
                  </div>
                ))
              ) : !deal.dealNotes && (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground">No notes available for this deal</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Deal Timeline (if needed) */}
      <Card className="border-2 border-outline-variant shadow-sm">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2.5 rounded-lg bg-neutral-90">
              <Clock className="h-6 w-6 text-neutral-60" />
            </div>
            <h2 className="text-xl font-bold text-on-surface">Deal Timeline</h2>
          </div>

          <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-primary-50" />
                <div>
                  <p className="font-semibold text-base text-on-surface">Deal Created</p>
                  <p className="text-sm text-muted-foreground">
                    {deal.createdDate ? new Date(deal.createdDate).toLocaleDateString('en-CA', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : 'Creation date not available'}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Current Status</p>
                {getStatusBadge(deal.status || 'pending')}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}