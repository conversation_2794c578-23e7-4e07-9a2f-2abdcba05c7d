import React, { useState, useMemo } from 'react';
import { X, Users, Search, Filter, Check, User, Building, LucideIcon } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { SliderHeader } from './SliderHeader';
import { cn } from '../utils/cn';
import { Customer, Vehicle } from '../types';
import { getStatusColor, getCreditColor } from '../utils/helpers';

interface CustomerMatchingPanelProps {
  isOpen: boolean;
  onClose: () => void;
  vehicle: Vehicle | null;
  customers: Customer[];
  onSaveMatches: (vehicleId: number, selectedCustomerIds: string[]) => void;
  triggerIcon?: LucideIcon | null;
  triggerAction?: string;
}

/**
 * CustomerMatchingPanel Component - Dark Themed
 * 
 * A sliding panel that allows users to match customers to a specific vehicle.
 * Features filtering, search, and multi-selection capabilities.
 * 
 * Now styled with dark theme to match the navigation pane.
 */
export const CustomerMatchingPanel: React.FC<CustomerMatchingPanelProps> = ({
  isOpen,
  onClose,
  vehicle,
  customers,
  onSaveMatches,
  triggerIcon,
  triggerAction
}) => {
  const [selectedCustomerIds, setSelectedCustomerIds] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // Filter and search customers
  const filteredCustomers = useMemo(() => {
    return customers.filter(customer => {
      // Search filter
      const matchesSearch = searchQuery === '' || 
        customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.assignedDealer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.salesRep.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === 'all' || customer.status.toLowerCase() === statusFilter;

      // Type filter
      const matchesType = typeFilter === 'all' || customer.customerType.toLowerCase() === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    });
  }, [customers, searchQuery, statusFilter, typeFilter]);

  const handleCustomerToggle = (customerId: string) => {
    setSelectedCustomerIds(prev => 
      prev.includes(customerId)
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    );
  };

  const handleSelectAll = () => {
    if (selectedCustomerIds.length === filteredCustomers.length) {
      setSelectedCustomerIds([]);
    } else {
      setSelectedCustomerIds(filteredCustomers.map(c => c.id));
    }
  };

  const handleSave = () => {
    if (vehicle && selectedCustomerIds.length > 0) {
      onSaveMatches(vehicle.id, selectedCustomerIds);
      setSelectedCustomerIds([]);
      onClose();
    }
  };

  const handleCancel = () => {
    setSelectedCustomerIds([]);
    setSearchQuery('');
    setStatusFilter('all');
    setTypeFilter('all');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/40 backdrop-blur-sm"
        onClick={handleCancel}
      />
      
      {/* Panel - Dark Themed */}
      <div className="absolute right-0 top-0 h-full w-full max-w-2xl bg-neutral-10 shadow-2xl border-l border-neutral-variant-30">
        <div className="flex h-full flex-col">
          {/* Header with trigger icon */}
          <SliderHeader
            title="Match Customers"
            subtitle={vehicle ? `${vehicle.year} ${vehicle.make} ${vehicle.model} • ${vehicle.location}` : undefined}
            triggerIcon={triggerIcon}
            triggerAction={triggerAction}
            onClose={handleCancel}
          />

          {/* Vehicle Summary */}
          {vehicle && (
            <div className="p-6 bg-neutral-20 border-b border-neutral-variant-30">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-xl bg-brand-orange/20 border border-brand-orange/30">
                  <Users className="h-6 w-6 text-brand-orange" />
                </div>
                <div>
                  <h3 className="font-semibold text-neutral-95">
                    {vehicle.year} {vehicle.make} {vehicle.model}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-neutral-80">
                    <span>{vehicle.price}</span>
                    <span>{vehicle.location}</span>
                    <span>{vehicle.mileage}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Filters and Search */}
          <div className="p-6 space-y-4 border-b border-neutral-variant-30 bg-neutral-10">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-neutral-60" />
              <Input
                placeholder="Search customers by name, email, or dealer..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-neutral-20 border-neutral-variant-30 text-neutral-95 placeholder:text-neutral-60 focus:border-brand-orange"
              />
            </div>

            <div className="flex space-x-4">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40 bg-neutral-20 border-neutral-variant-30 text-neutral-95">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent className="bg-neutral-20 border-neutral-variant-30">
                  <SelectItem value="all" className="text-neutral-95 focus:bg-neutral-30">All Status</SelectItem>
                  <SelectItem value="active" className="text-neutral-95 focus:bg-neutral-30">Active</SelectItem>
                  <SelectItem value="prospect" className="text-neutral-95 focus:bg-neutral-30">Prospect</SelectItem>
                  <SelectItem value="vip" className="text-neutral-95 focus:bg-neutral-30">VIP</SelectItem>
                  <SelectItem value="corporate" className="text-neutral-95 focus:bg-neutral-30">Corporate</SelectItem>
                  <SelectItem value="inactive" className="text-neutral-95 focus:bg-neutral-30">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-40 bg-neutral-20 border-neutral-variant-30 text-neutral-95">
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent className="bg-neutral-20 border-neutral-variant-30">
                  <SelectItem value="all" className="text-neutral-95 focus:bg-neutral-30">All Types</SelectItem>
                  <SelectItem value="individual" className="text-neutral-95 focus:bg-neutral-30">Individual</SelectItem>
                  <SelectItem value="business" className="text-neutral-95 focus:bg-neutral-30">Business</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleSelectAll}
                className="ml-auto bg-neutral-20 border-neutral-variant-30 text-neutral-95 hover:bg-neutral-30"
              >
                {selectedCustomerIds.length === filteredCustomers.length ? 'Deselect All' : 'Select All'}
              </Button>
            </div>

            {selectedCustomerIds.length > 0 && (
              <div className="p-3 bg-brand-orange/20 border border-brand-orange/30 rounded-lg">
                <p className="text-sm font-medium text-brand-orange">
                  {selectedCustomerIds.length} customer{selectedCustomerIds.length === 1 ? '' : 's'} selected
                </p>
              </div>
            )}
          </div>

          {/* Customer List */}
          <div className="flex-1 overflow-y-auto bg-neutral-10">
            <div className="p-6 space-y-3">
              {filteredCustomers.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-neutral-60 mx-auto mb-4" />
                  <h3 className="font-medium mb-2 text-neutral-95">No customers found</h3>
                  <p className="text-sm text-neutral-80">
                    Try adjusting your search or filter criteria.
                  </p>
                </div>
              ) : (
                filteredCustomers.map((customer) => (
                  <div
                    key={customer.id}
                    className={cn(
                      "flex items-center space-x-4 p-4 rounded-xl border-2 transition-all cursor-pointer hover:shadow-sm",
                      selectedCustomerIds.includes(customer.id)
                        ? "border-brand-orange bg-brand-orange/10"
                        : "border-neutral-variant-30 bg-neutral-20 hover:border-neutral-variant-20 hover:bg-neutral-30"
                    )}
                    onClick={() => handleCustomerToggle(customer.id)}
                  >
                    <div className="flex-shrink-0">
                      <div className={cn(
                        "w-5 h-5 rounded border-2 flex items-center justify-center transition-all",
                        selectedCustomerIds.includes(customer.id)
                          ? "border-brand-orange bg-brand-orange"
                          : "border-neutral-variant-50"
                      )}>
                        {selectedCustomerIds.includes(customer.id) && (
                          <Check className="h-3 w-3 text-white" />
                        )}
                      </div>
                    </div>

                    <Avatar className="h-12 w-12 border border-neutral-variant-30">
                      <AvatarImage src={customer.photo} alt={customer.name} />
                      <AvatarFallback className="bg-neutral-30 text-neutral-95">
                        {customer.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-neutral-95 truncate">
                          {customer.name}
                        </h4>
                        <div className="flex items-center space-x-1">
                          {customer.customerType === 'Individual' ? (
                            <User className="h-3 w-3 text-blue-400" />
                          ) : (
                            <Building className="h-3 w-3 text-purple-400" />
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3 mt-1">
                        <Badge variant="outline" className={cn(
                          "text-xs border-current",
                          selectedCustomerIds.includes(customer.id) ? "text-brand-orange border-brand-orange" : ""
                        )}>
                          {customer.status}
                        </Badge>
                        <span className={`text-xs font-medium ${getCreditColor(customer.creditRating)}`}>
                          {customer.creditRating}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 mt-2 text-sm text-neutral-80">
                        <span>{customer.assignedDealer}</span>
                        <span>{customer.lifetimeValue}</span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Footer Actions */}
          <div className="p-6 border-t border-neutral-variant-30 bg-neutral-20">
            <div className="flex items-center justify-between">
              <p className="text-sm text-neutral-80">
                {selectedCustomerIds.length} of {filteredCustomers.length} customers selected
              </p>
              <div className="flex space-x-3">
                <Button 
                  variant="outline" 
                  onClick={handleCancel}
                  className="bg-neutral-30 border-neutral-variant-30 text-neutral-95 hover:bg-neutral-40"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSave}
                  disabled={selectedCustomerIds.length === 0}
                  className="bg-brand-orange text-white hover:bg-brand-orange-dark disabled:opacity-50"
                >
                  Create {selectedCustomerIds.length} Deal{selectedCustomerIds.length === 1 ? '' : 's'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerMatchingPanel;