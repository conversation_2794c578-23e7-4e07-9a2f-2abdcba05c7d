/**
 * StepNotesButton Component - Reusable Notes Button for Deal Steps
 * 
 * Consistent Notes button component used across both horizontal and accordion layouts.
 * Positioned to the left of Next buttons for easy access at each step level.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { StickyNote } from 'lucide-react';
import { DealNote } from '../types';

interface StepNotesButtonProps {
  notes: DealNote[];
  onNotesToggle: () => void;
  disabled?: boolean;
  currentStep: number;
}

export function StepNotesButton({ 
  notes, 
  onNotesToggle, 
  disabled = false,
  currentStep 
}: StepNotesButtonProps) {
  // Count notes for current step
  const currentStepNotes = notes.filter(note => note.step === currentStep);
  const totalNotes = notes.length;

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onNotesToggle}
      disabled={disabled}
      className="flex items-center space-x-2 relative"
    >
      <StickyNote className="h-4 w-4" />
      <span>Notes</span>
      {totalNotes > 0 && (
        <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 bg-primary-50 text-white text-xs flex items-center justify-center">
          {totalNotes}
        </Badge>
      )}
      {currentStepNotes.length > 0 && (
        <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-brand-orange rounded-full border border-white"></div>
      )}
    </Button>
  );
}