import React, { useState, useMemo } from 'react';
import { X, Car, Search, Filter, Check, LucideIcon } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { SliderHeader } from './SliderHeader';
import { cn } from '../utils/cn';
import { Customer, Vehicle } from '../types';
import { getStatusColor } from '../utils/helpers';

interface VehicleMatchingPanelProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
  vehicles: Vehicle[];
  onSaveMatches: (customerId: string, selectedVehicleIds: number[]) => void;
  triggerIcon?: LucideIcon | null;
  triggerAction?: string;
}

/**
 * VehicleMatchingPanel Component - Dark Themed
 * 
 * A sliding panel that allows users to match vehicles to a specific customer.
 * Features filtering, search, and multi-selection capabilities.
 * 
 * Now styled with dark theme to match the navigation pane.
 */
export const VehicleMatchingPanel: React.FC<VehicleMatchingPanelProps> = ({
  isOpen,
  onClose,
  customer,
  vehicles,
  onSaveMatches,
  triggerIcon,
  triggerAction
}) => {
  const [selectedVehicleIds, setSelectedVehicleIds] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [makeFilter, setMakeFilter] = useState<string>('all');

  // Filter and search vehicles (status filtering removed - not fetched by system)
  const filteredVehicles = useMemo(() => {
    return vehicles.filter(vehicle => {
      // Search filter
      const matchesSearch = searchQuery === '' ||
        `${vehicle.year} ${vehicle.make} ${vehicle.model}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vehicle.vin.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vehicle.location.toLowerCase().includes(searchQuery.toLowerCase());

      // Make filter
      const matchesMake = makeFilter === 'all' || vehicle.make.toLowerCase() === makeFilter;

      return matchesSearch && matchesMake;
    });
  }, [vehicles, searchQuery, makeFilter]);

  // Get unique makes for filter
  const uniqueMakes = useMemo(() => {
    const makes = [...new Set(vehicles.map(v => v.make))].sort();
    return makes;
  }, [vehicles]);

  const handleVehicleToggle = (vehicleId: number) => {
    setSelectedVehicleIds(prev => 
      prev.includes(vehicleId)
        ? prev.filter(id => id !== vehicleId)
        : [...prev, vehicleId]
    );
  };

  const handleSelectAll = () => {
    if (selectedVehicleIds.length === filteredVehicles.length) {
      setSelectedVehicleIds([]);
    } else {
      setSelectedVehicleIds(filteredVehicles.map(v => v.id));
    }
  };

  const handleSave = () => {
    if (customer && selectedVehicleIds.length > 0) {
      onSaveMatches(customer.id, selectedVehicleIds);
      setSelectedVehicleIds([]);
      onClose();
    }
  };

  const handleCancel = () => {
    setSelectedVehicleIds([]);
    setSearchQuery('');
    setMakeFilter('all');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/40 backdrop-blur-sm"
        onClick={handleCancel}
      />
      
      {/* Panel - Dark Themed */}
      <div className="absolute right-0 top-0 h-full w-full max-w-2xl bg-neutral-10 shadow-2xl border-l border-neutral-variant-30">
        <div className="flex h-full flex-col">
          {/* Header with trigger icon */}
          <SliderHeader
            title="Match Vehicles"
            subtitle={customer ? `${customer.name} • ${customer.assignedDealer}` : undefined}
            triggerIcon={triggerIcon}
            triggerAction={triggerAction}
            onClose={handleCancel}
          />

          {/* Customer Summary */}
          {customer && (
            <div className="p-6 bg-neutral-20 border-b border-neutral-variant-30">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-xl bg-brand-orange/20 border border-brand-orange/30">
                  <Car className="h-6 w-6 text-brand-orange" />
                </div>
                <div>
                  <h3 className="font-semibold text-neutral-95">
                    {customer.name}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-neutral-80">
                    <span>{customer.customerType}</span>
                    <span>{customer.assignedDealer}</span>
                    <span>{customer.lifetimeValue}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Filters and Search */}
          <div className="p-6 space-y-4 border-b border-neutral-variant-30 bg-neutral-10">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-neutral-60" />
              <Input
                placeholder="Search vehicles by make, model, VIN, or location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-neutral-20 border-neutral-variant-30 text-neutral-95 placeholder:text-neutral-60 focus:border-brand-orange"
              />
            </div>

            <div className="flex space-x-4">


              <Select value={makeFilter} onValueChange={setMakeFilter}>
                <SelectTrigger className="w-40 bg-neutral-20 border-neutral-variant-30 text-neutral-95">
                  <SelectValue placeholder="All Makes" />
                </SelectTrigger>
                <SelectContent className="bg-neutral-20 border-neutral-variant-30">
                  <SelectItem value="all" className="text-neutral-95 focus:bg-neutral-30">All Makes</SelectItem>
                  {uniqueMakes.map(make => (
                    <SelectItem key={make} value={make.toLowerCase()} className="text-neutral-95 focus:bg-neutral-30">{make}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleSelectAll}
                className="ml-auto bg-neutral-20 border-neutral-variant-30 text-neutral-95 hover:bg-neutral-30"
              >
                {selectedVehicleIds.length === filteredVehicles.length ? 'Deselect All' : 'Select All'}
              </Button>
            </div>

            {selectedVehicleIds.length > 0 && (
              <div className="p-3 bg-brand-orange/20 border border-brand-orange/30 rounded-lg">
                <p className="text-sm font-medium text-brand-orange">
                  {selectedVehicleIds.length} vehicle{selectedVehicleIds.length === 1 ? '' : 's'} selected
                </p>
              </div>
            )}
          </div>

          {/* Vehicle List */}
          <div className="flex-1 overflow-y-auto bg-neutral-10">
            <div className="p-6 space-y-3">
              {filteredVehicles.length === 0 ? (
                <div className="text-center py-12">
                  <Car className="h-12 w-12 text-neutral-60 mx-auto mb-4" />
                  <h3 className="font-medium mb-2 text-neutral-95">No vehicles found</h3>
                  <p className="text-sm text-neutral-80">
                    Try adjusting your search or filter criteria.
                  </p>
                </div>
              ) : (
                filteredVehicles.map((vehicle) => (
                  <div
                    key={vehicle.id}
                    className={cn(
                      "flex items-center space-x-4 p-4 rounded-xl border-2 transition-all cursor-pointer hover:shadow-sm",
                      selectedVehicleIds.includes(vehicle.id)
                        ? "border-brand-orange bg-brand-orange/10"
                        : "border-neutral-variant-30 bg-neutral-20 hover:border-neutral-variant-20 hover:bg-neutral-30"
                    )}
                    onClick={() => handleVehicleToggle(vehicle.id)}
                  >
                    <div className="flex-shrink-0">
                      <div className={cn(
                        "w-5 h-5 rounded border-2 flex items-center justify-center transition-all",
                        selectedVehicleIds.includes(vehicle.id)
                          ? "border-brand-orange bg-brand-orange"
                          : "border-neutral-variant-50"
                      )}>
                        {selectedVehicleIds.includes(vehicle.id) && (
                          <Check className="h-3 w-3 text-white" />
                        )}
                      </div>
                    </div>

                    <div className="relative">
                      <ImageWithFallback
                        src={vehicle.photos[0]}
                        alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                        className="w-16 h-12 object-cover rounded-lg shadow-sm"
                      />
                      {vehicle.photos.length > 1 && (
                        <div className="absolute bottom-1 right-1 bg-black/60 text-white text-xs px-1 rounded">
                          +{vehicle.photos.length - 1}
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-neutral-95 truncate">
                          {vehicle.year} {vehicle.make} {vehicle.model}
                        </h4>
                      </div>
                      
                      <div className="flex items-center space-x-3 mt-1">
                        <span className="text-sm font-bold text-brand-orange">
                          {vehicle.price}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 mt-2 text-sm text-neutral-80">
                        <span>{vehicle.location}</span>
                        <span>{vehicle.mileage}</span>
                        <span>{vehicle.exterior}</span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Footer Actions */}
          <div className="p-6 border-t border-neutral-variant-30 bg-neutral-20">
            <div className="flex items-center justify-between">
              <p className="text-sm text-neutral-80">
                {selectedVehicleIds.length} of {filteredVehicles.length} vehicles selected
              </p>
              <div className="flex space-x-3">
                <Button 
                  variant="outline" 
                  onClick={handleCancel}
                  className="bg-neutral-30 border-neutral-variant-30 text-neutral-95 hover:bg-neutral-40"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSave}
                  disabled={selectedVehicleIds.length === 0}
                  className="bg-brand-orange text-white hover:bg-brand-orange-dark disabled:opacity-50"
                >
                  Create Deal with {selectedVehicleIds.length} Vehicle{selectedVehicleIds.length === 1 ? '' : 's'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VehicleMatchingPanel;