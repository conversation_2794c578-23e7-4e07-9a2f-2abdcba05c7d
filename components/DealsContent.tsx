/**
 * Deals Content Component - Enhanced with Consistent StatCard System
 * 
 * Comprehensive deal management interface with statistics cards, status indicators,
 * and enhanced table with compact rows and visible pagination.
 * Now includes standardized StatCard components for visual consistency.
 * 
 * STAT CARD ENHANCEMENTS:
 * - Consistent StatCard components for deal statistics
 * - Progressive variant styling with contextual colors
 * - Professional icon integration and hover effects
 * - Enhanced visual hierarchy matching dashboard design
 * - Improved typography and spacing consistency
 * 
 * Vision and Design by Ritwi<PERSON> | Built by Figma Make - Claude Sonnet
 */

import { Card, CardContent } from './ui/card';
import { Button } from './ui/button';
import { EnhancedMaterialTable, TableColumn } from './ui/material/table';
import { Plus, Handshake, DollarSign, TrendingUp, Users, Edit, Phone } from 'lucide-react';
import { InventoryStatCard } from './StatCard';
import { Deal } from '../types';
import { toast } from 'sonner@2.0.3';

interface DealsContentProps {
  dealStats: {
    totalDeals: number;
    totalValue: number;
    averageDealValue: number;
    averageApprovalEntities: number;
    completedDeals: number;
    pendingDeals: number;
    cancelledDeals: number;
  };
  deals: Deal[];
  dealColumns: TableColumn<Deal>[];
  onAddDeal: () => void;
  onTabChange: (tab: string) => void;
}

export function DealsContent({
  dealStats,
  deals,
  dealColumns,
  onAddDeal,
  onTabChange
}: DealsContentProps) {
  // Transform deal stats into StatCard format
  const dealStatCards = [
    {
      title: 'Total Deals',
      value: dealStats.totalDeals.toString(),
      trend: '+12%',
      trendLabel: 'vs last month',
      icon: Handshake,
      variant: 'primary' as const,
      onClick: () => toast.info('View all deals breakdown', { 
        description: `Total deals: ${dealStats.totalDeals}` 
      })
    },
    {
      title: 'Total Value',
      value: `$${Math.round(dealStats.totalValue / 1000)}K`,
      trend: '+18%',
      trendLabel: 'vs last month',
      icon: DollarSign,
      variant: 'success' as const,
      onClick: () => toast.info('View total deal value breakdown', { 
        description: `Total value: $${dealStats.totalValue.toLocaleString()}` 
      })
    },
    {
      title: 'Avg Deal Value',
      value: `$${Math.round(dealStats.averageDealValue / 1000)}K`,
      trend: '+7%',
      trendLabel: 'vs last month',
      icon: TrendingUp,
      variant: 'accent' as const,
      onClick: () => toast.info('View average deal value breakdown', { 
        description: `Average: $${dealStats.averageDealValue.toLocaleString()}` 
      })
    },
    {
      title: 'Avg Entities/Deal',
      value: dealStats.averageApprovalEntities.toFixed(1),
      trend: '+3%',
      trendLabel: 'vs last month',
      icon: Users,
      variant: 'secondary' as const,
      onClick: () => toast.info('View approval entities breakdown', { 
        description: `Average entities per deal: ${dealStats.averageApprovalEntities.toFixed(1)}` 
      })
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2>Deal Management</h2>
          <p className="text-sm text-muted-foreground">Comprehensive deal tracking with multi-approval entity support</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span className="text-muted-foreground">Finalized: {dealStats.completedDeals}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span className="text-muted-foreground">Pending: {dealStats.pendingDeals}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span className="text-muted-foreground">Cancelled: {dealStats.cancelledDeals}</span>
            </div>
          </div>
          <Button 
            className="bg-brand-orange text-white hover:bg-brand-orange-dark"
            onClick={onAddDeal}
          >
            <Plus className="h-4 w-4 mr-2" />
            Build New Deal
          </Button>
        </div>
      </div>

      {/* Deal Statistics Cards - Enhanced with Consistent StatCard Components */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        {dealStatCards.map((stat, index) => (
          <InventoryStatCard
            key={index}
            title={stat.title}
            value={stat.value}
            trend={stat.trend}
            trendLabel={stat.trendLabel}
            icon={stat.icon}
            variant={stat.variant}
            onClick={stat.onClick}
          />
        ))}
      </div>

      {/* Enhanced Deals Table with Compact Rows and Visible Pagination */}
      <EnhancedMaterialTable
        data={deals}
        columns={dealColumns}
        rowKey="id"
        selectable={true}
        sortable={true}
        filterable={true}
        searchable={true}
        paginated={true}
        pageSize={6}
        pageSizeOptions={[5, 6, 10, 15, 20]}
        exportable={true}
        bulkActions={(selectedDeals) => (
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => toast.info(`Bulk contact ${selectedDeals.length} customers`)}
            >
              <Phone className="h-4 w-4 mr-2" />
              Contact ({selectedDeals.length})
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => toast.info(`Export ${selectedDeals.length} deals`)}
            >
              Export ({selectedDeals.length})
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => toast.info(`Bulk update ${selectedDeals.length} deals`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Update ({selectedDeals.length})
            </Button>
          </div>
        )}
        containerClassName="h-fit"
        className="compact-deal-rows"
        emptyState={
          <div className="text-center py-12">
            <Handshake className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium mb-2">No deals found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Start creating deals by matching customers to vehicles, or create a new deal from scratch.
            </p>
            <div className="flex justify-center space-x-4">
              <Button 
                onClick={onAddDeal}
                className="bg-brand-orange text-white hover:bg-brand-orange-dark"
              >
                <Plus className="h-4 w-4 mr-2" />
                Build New Deal
              </Button>
              <Button 
                variant="outline"
                onClick={() => onTabChange('customers')}
              >
                Go to Customers
              </Button>
            </div>
          </div>
        }
      />
    </div>
  );
}