/**
 * Application Sidebar Component
 * 
 * Enhanced sidebar navigation with floating deal builder, collapsible design,
 * and comprehensive tooltip support for collapsed states.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> | Built by Figma Make - Claude Sonnet
 */

import { Badge } from './ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { cn } from '../utils/cn';
import { 
  LayoutDashboard, Car, Users, Handshake, BarChart3, Zap 
} from 'lucide-react';

interface AppSidebarProps {
  sidebarCollapsed: boolean;
  activeTab: string;
  dealsCount: number;
  onTabChange: (tab: string) => void;
  onFloatingDealBuilder: () => void;
}

export function AppSidebar({ 
  sidebarCollapsed, 
  activeTab, 
  dealsCount, 
  onTabChange, 
  onFloatingDealBuilder 
}: AppSidebarProps) {
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'inventory', label: 'Inventory', icon: Car },
    { id: 'customers', label: 'Customers', icon: Users },
    { id: 'deals', label: 'Deals', icon: Handshake, badge: dealsCount },
    { id: 'reports', label: 'Reports', icon: BarChart3 }
  ];

  return (
    <aside className={`${sidebarCollapsed ? 'w-16' : 'w-64'} border-r bg-neutral-10 transition-all duration-300 flex-shrink-0`}>
      <nav className="p-4 h-full">
        <div className="space-y-4">
          {/* Floating Deal Builder Button - Primary CTA Exception */}
          {sidebarCollapsed ? (
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="flex h-14 w-14 items-center justify-center rounded-xl font-semibold transition-all duration-200 cursor-pointer mx-auto group bg-primary-50 hover:bg-primary-60 text-white shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-0.5 relative overflow-hidden"
                  onClick={onFloatingDealBuilder}
                  role="button"
                  aria-label="Build new deal"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      onFloatingDealBuilder();
                    }
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-40 to-primary-60 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute inset-0 rounded-xl bg-primary-50 animate-ping opacity-20" />
                  <div className="relative flex items-center justify-center">
                    <Zap className="h-6 w-6 transition-transform group-hover:scale-110 drop-shadow-sm" />
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p className="font-semibold">Build new deal</p>
              </TooltipContent>
            </Tooltip>
          ) : (
            <div
              className="flex h-14 items-center justify-center px-4 rounded-xl font-semibold transition-all duration-200 cursor-pointer group bg-primary-50 hover:bg-primary-60 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] hover:-translate-y-0.5 relative overflow-hidden"
              onClick={onFloatingDealBuilder}
              role="button"
              aria-label="Build new deal"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onFloatingDealBuilder();
                }
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary-40 to-primary-60 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute inset-0 rounded-xl bg-primary-40 animate-pulse opacity-10" />
              <div className="relative flex items-center space-x-3">
                <Zap className="h-5 w-5 transition-transform group-hover:scale-110 drop-shadow-sm" />
                <span className="text-sm font-semibold tracking-wide">Build new deal</span>
              </div>
            </div>
          )}

          {/* Navigation Separator */}
          <div className={`${sidebarCollapsed ? 'w-8 mx-auto' : 'w-full'} h-px bg-neutral-variant-30 opacity-50`} />

          {/* Main Navigation Items */}
          <div className="space-y-2">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              const isActive = activeTab === item.id;
              
              if (sidebarCollapsed) {
                return (
                  <Tooltip key={item.id}>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          "flex h-12 w-12 items-center justify-center whitespace-nowrap rounded-xl font-medium transition-all duration-200 cursor-pointer mx-auto group",
                          isActive 
                            ? "bg-primary-20 text-white shadow-lg" 
                            : "text-neutral-95 hover:bg-brand-orange/20 hover:text-brand-orange-light",
                          item.id === 'deals' && "relative"
                        )}
                        onClick={() => onTabChange(item.id)}
                      >
                        <IconComponent className="h-5 w-5 transition-transform group-hover:scale-110" />
                        {item.id === 'deals' && item.badge && item.badge > 0 && (
                          <div className="absolute -top-1 -right-1">
                            <div className="h-5 w-5 bg-primary-60 rounded-full border-2 border-neutral-10 flex items-center justify-center">
                              <span className="text-xs font-bold text-white">{item.badge}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <div className="flex items-center space-x-2">
                        <p>{item.label}</p>
                        {item.id === 'deals' && item.badge && item.badge > 0 && (
                          <Badge variant="outline" className="border-primary-60 text-primary-60">
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                );
              }

              return (
                <div
                  key={item.id}
                  className={cn(
                    "flex h-12 items-center justify-start px-4 whitespace-nowrap rounded-xl font-medium transition-all duration-200 cursor-pointer group",
                    isActive 
                      ? "bg-primary-20 text-white shadow-lg" 
                      : "text-neutral-95 hover:bg-brand-orange/20 hover:text-brand-orange-light"
                  )}
                  onClick={() => onTabChange(item.id)}
                >
                  <div className="flex items-center space-x-3 w-full">
                    <IconComponent className="h-5 w-5 flex-shrink-0 transition-transform group-hover:scale-110" />
                    <span className="transition-colors">{item.label}</span>
                    {item.id === 'deals' && item.badge && item.badge > 0 && (
                      <Badge variant="outline" className={cn(
                        "ml-auto transition-colors",
                        isActive
                          ? "border-white/30 text-white/90"
                          : "border-brand-orange text-brand-orange group-hover:border-brand-orange-light group-hover:text-brand-orange-light"
                      )}>
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </nav>
    </aside>
  );
}