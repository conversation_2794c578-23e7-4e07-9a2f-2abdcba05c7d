/**
 * Statistics Icon Component - Consistent Icon Styling System
 * 
 * Provides standardized icon styling for dashboard stats cards and throughout the application.
 * Ensures consistent sizing, coloring, and visual hierarchy across all icon implementations.
 * 
 * DESIGN PRINCIPLES:
 * - Consistent sizing for different contexts (small, medium, large)
 * - Material Design 3 color palette integration
 * - Orange brand accent variations for visual hierarchy
 * - Professional appearance with subtle enhancements
 * - Accessibility considerations with proper contrast
 * 
 * Vision and Design by Ritwik <PERSON>upte | Built by Figma Make - Claude Sonnet
 */

import { LucideIcon } from 'lucide-react';
import { cn } from '../utils/cn';

export type IconSize = 'sm' | 'md' | 'lg' | 'xl';
export type IconVariant = 'primary' | 'secondary' | 'accent' | 'neutral' | 'success' | 'warning' | 'error';
export type IconStyle = 'solid' | 'outline' | 'ghost' | 'minimal';

interface StatsIconProps {
  icon: LucideIcon;
  size?: IconSize;
  variant?: IconVariant;
  style?: IconStyle;
  className?: string;
  containerClassName?: string;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6', 
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
};

const containerSizeClasses = {
  sm: 'p-2',
  md: 'p-3',
  lg: 'p-4',
  xl: 'p-6'
};

const variantClasses = {
  primary: {
    solid: 'text-primary-100 bg-primary-50',
    outline: 'text-primary-50 bg-primary-95 border border-primary-50',
    ghost: 'text-primary-50 bg-primary-95',
    minimal: 'text-primary-50'
  },
  secondary: {
    solid: 'text-primary-100 bg-primary-40',
    outline: 'text-primary-40 bg-primary-95 border border-primary-40',
    ghost: 'text-primary-40 bg-primary-95',
    minimal: 'text-primary-40'
  },
  accent: {
    solid: 'text-primary-100 bg-primary-60',
    outline: 'text-primary-60 bg-primary-95 border border-primary-60',
    ghost: 'text-primary-60 bg-primary-95',
    minimal: 'text-primary-60'
  },
  neutral: {
    solid: 'text-neutral-100 bg-neutral-50',
    outline: 'text-neutral-50 bg-neutral-95 border border-neutral-50',
    ghost: 'text-neutral-50 bg-neutral-95',
    minimal: 'text-neutral-50'
  },
  success: {
    solid: 'text-white bg-green-500',
    outline: 'text-green-600 bg-green-50 border border-green-500',
    ghost: 'text-green-600 bg-green-50',
    minimal: 'text-green-600'
  },
  warning: {
    solid: 'text-white bg-yellow-500',
    outline: 'text-yellow-600 bg-yellow-50 border border-yellow-500',
    ghost: 'text-yellow-600 bg-yellow-50',
    minimal: 'text-yellow-600'
  },
  error: {
    solid: 'text-white bg-red-500',
    outline: 'text-red-600 bg-red-50 border border-red-500',
    ghost: 'text-red-600 bg-red-50',
    minimal: 'text-red-600'
  }
};

/**
 * StatsIcon Component
 * 
 * Renders a consistently styled icon with optional container styling.
 * Supports multiple sizes, variants, and styles for different use cases.
 */
export function StatsIcon({ 
  icon: Icon, 
  size = 'lg', 
  variant = 'primary', 
  style = 'ghost',
  className,
  containerClassName 
}: StatsIconProps) {
  const iconSizeClass = sizeClasses[size];
  const containerSizeClass = containerSizeClasses[size];
  const variantClass = variantClasses[variant][style];

  // For minimal style, don't render container
  if (style === 'minimal') {
    return (
      <Icon 
        className={cn(
          iconSizeClass,
          variantClass,
          'flex-shrink-0',
          className
        )} 
      />
    );
  }

  return (
    <div 
      className={cn(
        containerSizeClass,
        'rounded-lg flex items-center justify-center flex-shrink-0 transition-all duration-200',
        variantClass,
        containerClassName
      )}
    >
      <Icon className={cn(iconSizeClass, 'flex-shrink-0', className)} />
    </div>
  );
}

/**
 * Pre-configured Icon Variants for Common Use Cases
 */

// Dashboard Stats Icons - Large with subtle backgrounds
export function DashboardStatsIcon({ icon, className }: { icon: LucideIcon; className?: string }) {
  return (
    <StatsIcon 
      icon={icon} 
      size="xl" 
      variant="primary" 
      style="ghost"
      className={className}
    />
  );
}

// Action Icons - Medium size for buttons and interactions
export function ActionIcon({ icon, variant = 'neutral', className }: { 
  icon: LucideIcon; 
  variant?: IconVariant;
  className?: string;
}) {
  return (
    <StatsIcon 
      icon={icon} 
      size="md" 
      variant={variant} 
      style="minimal"
      className={className}
    />
  );
}

// Feature Icons - Large with outline style for feature highlights
export function FeatureIcon({ icon, variant = 'primary', className }: { 
  icon: LucideIcon; 
  variant?: IconVariant;
  className?: string;
}) {
  return (
    <StatsIcon 
      icon={icon} 
      size="lg" 
      variant={variant} 
      style="outline"
      className={className}
    />
  );
}

// Status Icons - Small minimal icons for status indicators
export function StatusIcon({ icon, variant = 'neutral', className }: { 
  icon: LucideIcon; 
  variant?: IconVariant;
  className?: string;
}) {
  return (
    <StatsIcon 
      icon={icon} 
      size="sm" 
      variant={variant} 
      style="minimal"
      className={className}
    />
  );
}

// Navigation Icons - Medium minimal icons for navigation elements
export function NavigationIcon({ icon, variant = 'neutral', className }: { 
  icon: LucideIcon; 
  variant?: IconVariant;
  className?: string;
}) {
  return (
    <StatsIcon 
      icon={icon} 
      size="md" 
      variant={variant} 
      style="minimal"
      className={className}
    />
  );
}