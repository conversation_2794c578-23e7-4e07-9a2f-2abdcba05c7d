/**
 * AddDealForm Component - Streamlined Wrapper
 * 
 * Simple wrapper component that directly renders the AccordionDealForm.
 * Maintains interface compatibility while removing redundancy.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { AccordionDealForm } from './AccordionDealForm';
import { DealNote } from '../types';

interface AddDealFormProps {
  onBack: () => void;
  onSubmit: (data: any) => void;
  locations: Array<{ id: string; name: string; count: number }>;
  isSubmitting?: boolean;
  notes: DealNote[];
  onNotesChange: (notes: DealNote[]) => void;
  currentStep: number;
  onCurrentStepChange: (step: number) => void;
  onNotesToggle: () => void;
}

export function AddDealForm(props: AddDealFormProps) {
  return <AccordionDealForm {...props} />;
}