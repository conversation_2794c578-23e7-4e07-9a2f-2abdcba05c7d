/**
 * HorizontalDealForm Component - Enhanced UI Layout & Visual Aesthetics
 * 
 * Classic horizontal step tracker with enhanced visual hierarchy and proper alignments.
 * Features sophisticated step navigation system with professional aesthetic improvements
 * and comprehensive form layouts optimized for 1366x768 resolution.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Separator } from './ui/separator';

// Material Design 3 Components
import { TextField } from './ui/material/text-field';
import { 
  SelectField, 
  SelectFieldTrigger, 
  SelectFieldContent, 
  SelectFieldItem, 
  SelectFieldValue 
} from './ui/material/select-field';
import { Checkbox } from './ui/material/checkbox';
import { Label } from './ui/material/label';
import { Textarea } from './ui/material/textarea';

// Table components for vehicle selection
import { MaterialTable, MaterialTableBody, MaterialTableCell, MaterialTableHead, MaterialTableHeader, MaterialTableRow, MaterialTableContainer } from './ui/material/table';

// Vehicle Filters Component
import { VehicleFilters, VehicleFilterState, initialFilters } from './VehicleFilters';

// Enhanced components
import { StepTracker, Step } from './StepTracker';
import { ApprovalEntityManager } from './ApprovalEntityManager';
import { StepNotesButton } from './StepNotesButton';
import { PhotoUpload } from './PhotoUpload';

import { ImageWithFallback } from './figma/ImageWithFallback';
import { ApprovalEntity, DealNote, DEAL_STEP_NAMES, ID_TYPE_OPTIONS } from '../types';
import { 
  capitalize, 
  capitalizeWords, 
  safeString, 
  formatName, 
  getInitials,
  formatPhone,
  formatEmail,
  formatCurrency
} from '../utils/stringUtils';

// Fallback ID options in case import fails
const FALLBACK_ID_OPTIONS = [
  'Driver\'s License',
  'Passport',
  'Health Card',
  'Social Insurance Number Card',
  'Birth Certificate',
  'Permanent Resident Card',
  'Citizenship Certificate',
  'Other Government ID'
];

import { 
  ArrowLeft, ArrowRight, Check, UserCheck, Building, CreditCard, ArrowLeftRight, 
  FileText, Edit, Phone, Mail, MapPin, Calendar, DollarSign,
  CheckCircle, Clock, Image as ImageIcon, Users, Search, 
  AlertTriangle, Briefcase, Home, IdCard, Loader2, User, Car, Shield
} from 'lucide-react';

interface DealFormData {
  // Step 1: Customer Information - Personal Details
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  phoneNumber: string;
  emailAddress: string;
  customerPhoto?: string;
  
  // Step 1: ID Documentation
  idType: string;
  idImageUpload?: string;
  
  // Step 1: Residential Information
  suiteNumber?: string;
  address: string;
  streetName: string;
  city: string;
  province: string;
  
  // Step 1: Status
  status: 'Yes' | 'No';
  
  // Step 2: Approval Entities
  approvalEntities: ApprovalEntity[];
  
  // Step 3: Trade-in Details
  hasTradeIn: boolean;
  tradeInMake?: string;
  tradeInModel?: string;
  tradeInYear?: string;
  tradeInTrim?: string;
  tradeInVin?: string;
  tradeInMileage?: string;
  tradeInColor?: string;
  tradeInAllowance?: string;
  tradeInFcv?: string;
  tradeInLoan?: string;
  
  // Step 4: Vehicle Selection
  selectedVehicles: number[];
  
  // Step 5: Deal Summary
  dealNotes: string;
}

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  price: string;
  status: 'Available' | 'Reserved' | 'Sold';
  location: string;
  vin: string;
  mileage: string;
  exterior: string;
  interior: string;
  transmission: string;
  fuelType: string;
  daysOnLot: number;
  cost: string;
  photos: string[];
  features: string[];
}

interface HorizontalDealFormProps {
  onBack: () => void;
  onSubmit: (data: DealFormData) => void;
  locations: Array<{ id: string; name: string; count: number }>;
  isSubmitting?: boolean;
  // External notes system props
  notes: DealNote[];
  onNotesChange: (notes: DealNote[]) => void;
  currentStep: number;
  onCurrentStepChange: (step: number) => void;
  // Step-level notes toggle
  onNotesToggle: () => void;
}

export function HorizontalDealForm({ 
  onBack, 
  onSubmit, 
  locations = [], 
  isSubmitting = false,
  // External notes system props
  notes,
  onNotesChange,
  currentStep,
  onCurrentStepChange,
  // Step-level notes toggle
  onNotesToggle
}: HorizontalDealFormProps) {
  const [internalCurrentStep, setInternalCurrentStep] = useState(1);
  const [vehicleFilters, setVehicleFilters] = useState<VehicleFilterState>(initialFilters);
  const [formData, setFormData] = useState<DealFormData>({
    // Personal Details
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    phoneNumber: '',
    emailAddress: '',
    customerPhoto: undefined,
    
    // ID Documentation
    idType: '',
    idImageUpload: undefined,
    
    // Residential Information
    suiteNumber: '',
    address: '',
    streetName: '',
    city: '',
    province: '',
    
    // Status
    status: 'Yes',
    
    // Other steps
    approvalEntities: [],
    hasTradeIn: false,
    selectedVehicles: [],
    dealNotes: ''
  });

  // Sync internal step with external step
  useEffect(() => {
    setInternalCurrentStep(currentStep);
  }, [currentStep]);

  // Mock vehicle data based on your existing inventory structure
  const availableVehicles: Vehicle[] = [
    { 
      id: 1, make: 'Toyota', model: 'Camry', year: 2024, price: '$32,999', status: 'Available', 
      location: 'Toronto Downtown', vin: '1HGCM82633A123456', mileage: '12,450 km',
      exterior: 'Midnight Black', interior: 'Black Leather', transmission: 'CVT', fuelType: 'Hybrid',
      daysOnLot: 23, cost: '$28,500', 
      photos: ['https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=400&h=300&fit=crop'],
      features: ['Navigation', 'Backup Camera', 'Heated Seats']
    },
    { 
      id: 2, make: 'Honda', model: 'Civic', year: 2024, price: '$28,499', status: 'Available', 
      location: 'Mississauga', vin: '2HGCM82633A123457', mileage: '8,750 km',
      exterior: 'Pearl White', interior: 'Black Cloth', transmission: 'CVT', fuelType: 'Gasoline',
      daysOnLot: 15, cost: '$24,800', 
      photos: ['https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop'],
      features: ['Honda Sensing', 'Sunroof', 'Wireless Charging']
    },
    { 
      id: 3, make: 'Ford', model: 'F-150', year: 2023, price: '$45,999', status: 'Available', 
      location: 'Vancouver', vin: '3HGCM82633A123458', mileage: '25,100 km',
      exterior: 'Agate Black', interior: 'Medium Earth Gray', transmission: '10-Speed Automatic', fuelType: 'Gasoline',
      daysOnLot: 41, cost: '$41,200', 
      photos: ['https://images.unsplash.com/photo-1563720223185-11003d516935?w=400&h=300&fit=crop'],
      features: ['4WD', 'Tow Package', 'B&O Sound']
    },
    { 
      id: 4, make: 'BMW', model: 'X3', year: 2024, price: '$52,999', status: 'Available', 
      location: 'Toronto Downtown', vin: '6HGCM82633A123461', mileage: '15,200 km',
      exterior: 'Alpine White', interior: 'Black SensaTec', transmission: '8-Speed Automatic', fuelType: 'Gasoline',
      daysOnLot: 28, cost: '$47,500', 
      photos: ['https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=300&fit=crop'],
      features: ['xDrive AWD', 'Panoramic Roof', 'Harman Kardon Audio']
    },
    { 
      id: 5, make: 'Audi', model: 'Q5', year: 2024, price: '$48,999', status: 'Available', 
      location: 'Vancouver', vin: '7HGCM82633A123462', mileage: '9,800 km',
      exterior: 'Quantum Gray', interior: 'Black Leather', transmission: '7-Speed S tronic', fuelType: 'Gasoline',
      daysOnLot: 19, cost: '$43,800', 
      photos: ['https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop'],
      features: ['quattro AWD', 'Virtual Cockpit', 'Bang & Olufsen Audio']
    }
  ];

  const updateFormData = (updates: Partial<DealFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const isStepComplete = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        return !!(formData.firstName && formData.lastName && formData.phoneNumber && formData.emailAddress);
      case 2:
        return formData.approvalEntities.length > 0;
      case 3:
        return !formData.hasTradeIn || !!(formData.tradeInMake && formData.tradeInModel && formData.tradeInYear);
      case 4:
        return formData.selectedVehicles.length > 0;
      case 5:
        return true;
      default:
        return false;
    }
  };

  // Enhanced step state logic with proper completion tracking
  const getStepState = (stepNumber: number): 'pending' | 'active' | 'complete' => {
    if (stepNumber === internalCurrentStep) {
      return 'active';
    } else if (stepNumber < internalCurrentStep && isStepComplete(stepNumber)) {
      return 'complete';
    } else {
      return 'pending';
    }
  };

  // Updated steps with outlined style icons for consistent visual hierarchy
  const steps: Step[] = [
    {
      id: 'customer-info',
      title: 'Customer Info',
      subtitle: 'Step 1',
      icon: UserCheck,
      state: getStepState(1),
      stepNumber: 1
    },
    {
      id: 'pre-approval-details',
      title: 'Pre Approval Details',
      subtitle: 'Step 2',
      icon: Users,
      state: getStepState(2),
      stepNumber: 2
    },
    {
      id: 'trade-in',
      title: 'Trade-in',
      subtitle: 'Step 3',
      icon: ArrowLeftRight,
      state: getStepState(3),
      stepNumber: 3
    },
    {
      id: 'vehicle',
      title: 'Vehicle',
      subtitle: 'Step 4',
      icon: Search,
      state: getStepState(4),
      stepNumber: 4
    },
    {
      id: 'finish-deal',
      title: 'Finish Deal',
      subtitle: 'Step 5',
      icon: CheckCircle,
      state: getStepState(5),
      stepNumber: 5
    }
  ];

  const getSelectedVehicleDetails = () => {
    return availableVehicles.filter(v => formData.selectedVehicles.includes(v.id));
  };

  const getStatusColor = (status: string): string => {
    const statusMap: Record<string, string> = {
      'available': 'border-green-500 text-green-500 font-medium px-3 py-1.5',
      'reserved': 'border-yellow-500 text-yellow-500 font-medium px-3 py-1.5',
      'sold': 'border-gray-500 text-gray-500 font-medium px-3 py-1.5'
    };
    return statusMap[status.toLowerCase()] || 'border-gray-500 text-gray-500 font-medium px-3 py-1.5';
  };

  // Enhanced step navigation handler
  const handleStepClick = (stepNumber: number) => {
    if (!isSubmitting) {
      setInternalCurrentStep(stepNumber);
      onCurrentStepChange(stepNumber);
    }
  };

  const handleNext = () => {
    if (internalCurrentStep < 5 && !isSubmitting) {
      const nextStep = internalCurrentStep + 1;
      setInternalCurrentStep(nextStep);
      onCurrentStepChange(nextStep);
    }
  };

  const handlePrevious = () => {
    if (internalCurrentStep > 1 && !isSubmitting) {
      const prevStep = internalCurrentStep - 1;
      setInternalCurrentStep(prevStep);
      onCurrentStepChange(prevStep);
    }
  };

  const handleSubmit = () => {
    if (!isSubmitting) {
      // Include notes in the submitted data
      const dataWithNotes = {
        ...formData,
        notes: notes
      };
      onSubmit(dataWithNotes);
    }
  };

  const handleVehicleSelection = (vehicleId: number) => {
    if (!isSubmitting) {
      setFormData(prev => ({
        ...prev,
        selectedVehicles: prev.selectedVehicles.includes(vehicleId)
          ? prev.selectedVehicles.filter(id => id !== vehicleId)
          : [...prev.selectedVehicles, vehicleId]
      }));
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6 relative">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            onClick={onBack} 
            className="p-2"
            disabled={isSubmitting}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-on-surface">Deal Builder - Enhanced Flow</h1>
            <p className="text-base text-muted-foreground">Navigate through the 5-step process with improved visual aesthetics</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {isSubmitting && (
            <div className="flex items-center space-x-2 text-primary-50">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Creating deal...</span>
            </div>
          )}
          <Badge variant="outline" className="px-4 py-2 text-sm font-medium">
            Step {internalCurrentStep} of 5
          </Badge>
        </div>
      </div>

      {/* Enhanced Step Tracker */}
      <StepTracker 
        steps={steps} 
        onStepClick={handleStepClick}
        disabled={isSubmitting}
      />

      {/* Enhanced Step Content */}
      <Card className={`bg-surface border-outline-variant shadow-sm ${isSubmitting ? 'opacity-75' : ''}`}>
        <CardContent className="p-8">
          {/* Step 1: Customer Information - Enhanced Layout */}
          {internalCurrentStep === 1 && (
            <div className="space-y-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="p-3 rounded-full bg-primary-50 text-white">
                  <UserCheck className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-on-surface">Customer Information</h3>
                  <p className="text-base text-muted-foreground">Enter the customer's personal details, ID documentation, and address</p>
                </div>
              </div>

              {/* Personal Information Section - Enhanced */}
              <div className="bg-gradient-to-br from-primary-99 to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-primary-90 mr-3">
                    <User className="h-5 w-5 text-primary-50" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">Personal Information</h4>
                    <p className="text-sm text-muted-foreground">Enter customer's basic details</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                  <TextField
                    label="First name *"
                    value={formData.firstName}
                    onChange={(e) => updateFormData({ firstName: e.target.value })}
                    placeholder="Enter first name"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />

                  <TextField
                    label="Last name *"
                    value={formData.lastName}
                    onChange={(e) => updateFormData({ lastName: e.target.value })}
                    placeholder="Enter last name"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />

                  <TextField
                    label="Date of birth *"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => updateFormData({ dateOfBirth: e.target.value })}
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <TextField
                    label="Phone number *"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => updateFormData({ phoneNumber: e.target.value })}
                    placeholder="(*************"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />

                  <TextField
                    label="Email address *"
                    type="email"
                    value={formData.emailAddress}
                    onChange={(e) => updateFormData({ emailAddress: e.target.value })}
                    placeholder="<EMAIL>"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />
                </div>

                <div className="mt-6 pt-6 border-t border-outline-variant">
                  <div className="flex items-start space-x-6">
                    <div className="flex-shrink-0">
                      <div className="w-20 h-20 rounded-lg bg-surface-container border-2 border-dashed border-outline-variant flex items-center justify-center">
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <PhotoUpload
                        label="Customer photo"
                        value={formData.customerPhoto}
                        onChange={(file) => updateFormData({ customerPhoto: file })}
                        disabled={isSubmitting}
                        type="photo"
                        placeholder="Upload customer photo (optional)"
                        className="w-full"
                      />
                      <p className="text-xs text-muted-foreground mt-2">
                        Upload a clear photo of the customer for identification purposes
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* ID Documentation Section - Enhanced */}
              <div className="bg-gradient-to-br from-surface-bright to-surface-container-low rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-secondary-90 mr-3">
                    <IdCard className="h-5 w-5 text-secondary-40" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">ID Documentation</h4>
                    <p className="text-sm text-muted-foreground">Required identification documents</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <SelectField 
                      value={formData.idType} 
                      onValueChange={(value) => updateFormData({ idType: value })}
                      disabled={isSubmitting}
                    >
                      <SelectFieldTrigger label="ID type *">
                        <SelectFieldValue placeholder="Select ID type" />
                      </SelectFieldTrigger>
                      <SelectFieldContent>
                        {(ID_TYPE_OPTIONS || FALLBACK_ID_OPTIONS).map(idType => (
                          <SelectFieldItem key={idType} value={idType}>
                            {idType}
                          </SelectFieldItem>
                        ))}
                      </SelectFieldContent>
                    </SelectField>
                    <p className="text-xs text-muted-foreground">
                      Select the type of government-issued ID
                    </p>
                  </div>

                  <div className="space-y-2">
                    <PhotoUpload
                      label="Upload ID document *"
                      value={formData.idImageUpload}
                      onChange={(file) => updateFormData({ idImageUpload: file })}
                      disabled={isSubmitting}
                      type="document"
                      placeholder="Upload clear photo of ID"
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Upload both sides of the ID document
                    </p>
                  </div>
                </div>
              </div>

              {/* Residential Information Section - Enhanced */}
              <div className="bg-gradient-to-br from-surface-container-lowest to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-tertiary-90 mr-3">
                    <Home className="h-5 w-5 text-tertiary-40" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">Residential Information</h4>
                    <p className="text-sm text-muted-foreground">Customer's current address details</p>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <div className="lg:col-span-1">
                      <TextField
                        label="Suite/Unit #"
                        value={formData.suiteNumber || ''}
                        onChange={(e) => updateFormData({ suiteNumber: e.target.value })}
                        placeholder="e.g. 205"
                        disabled={isSubmitting}
                        className="form-input"
                      />
                    </div>

                    <div className="lg:col-span-1">
                      <TextField
                        label="Address #"
                        value={formData.address}
                        onChange={(e) => updateFormData({ address: e.target.value })}
                        placeholder="e.g. 123"
                        disabled={isSubmitting}
                        className="form-input"
                      />
                    </div>

                    <div className="lg:col-span-2">
                      <TextField
                        label="Street name *"
                        value={formData.streetName}
                        onChange={(e) => updateFormData({ streetName: e.target.value })}
                        placeholder="e.g. Main Street"
                        disabled={isSubmitting}
                        className="form-input"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <TextField
                      label="City *"
                      value={formData.city}
                      onChange={(e) => updateFormData({ city: e.target.value })}
                      placeholder="e.g. Toronto"
                      disabled={isSubmitting}
                      className="form-input"
                    />

                    <SelectField 
                      value={formData.province} 
                      onValueChange={(value) => updateFormData({ province: value })}
                      disabled={isSubmitting}
                    >
                      <SelectFieldTrigger label="Province *">
                        <SelectFieldValue placeholder="Select province" />
                      </SelectFieldTrigger>
                      <SelectFieldContent>
                        <SelectFieldItem value="ON">Ontario</SelectFieldItem>
                        <SelectFieldItem value="BC">British Columbia</SelectFieldItem>
                        <SelectFieldItem value="AB">Alberta</SelectFieldItem>
                        <SelectFieldItem value="QC">Quebec</SelectFieldItem>
                        <SelectFieldItem value="MB">Manitoba</SelectFieldItem>
                        <SelectFieldItem value="SK">Saskatchewan</SelectFieldItem>
                        <SelectFieldItem value="NS">Nova Scotia</SelectFieldItem>
                        <SelectFieldItem value="NB">New Brunswick</SelectFieldItem>
                        <SelectFieldItem value="PE">Prince Edward Island</SelectFieldItem>
                        <SelectFieldItem value="NL">Newfoundland and Labrador</SelectFieldItem>
                        <SelectFieldItem value="YT">Yukon</SelectFieldItem>
                        <SelectFieldItem value="NT">Northwest Territories</SelectFieldItem>
                        <SelectFieldItem value="NU">Nunavut</SelectFieldItem>
                      </SelectFieldContent>
                    </SelectField>
                  </div>
                </div>
              </div>

              {/* Status Section - Enhanced */}
              <div className="bg-gradient-to-br from-primary-95 to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-primary-90 mr-3">
                    <CheckCircle className="h-5 w-5 text-primary-50" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">Customer Status</h4>
                    <p className="text-sm text-muted-foreground">Current customer status verification</p>
                  </div>
                </div>
                
                <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                  <Label className="text-sm font-semibold text-on-surface mb-3 block">
                    Is this customer active? *
                  </Label>
                  
                  <div className="flex items-center space-x-8">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        id="status-yes-horizontal"
                        name="status-horizontal"
                        value="Yes"
                        checked={formData.status === 'Yes'}
                        onChange={(e) => updateFormData({ status: e.target.value as 'Yes' | 'No' })}
                        disabled={isSubmitting}
                        className="w-4 h-4 text-primary-50 bg-white border-2 border-outline focus:ring-2 focus:ring-primary-50 focus:ring-offset-2"
                      />
                      <Label htmlFor="status-yes-horizontal" className="text-sm font-medium text-on-surface cursor-pointer">
                        Yes - Active Customer
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        id="status-no-horizontal"
                        name="status-horizontal"
                        value="No"
                        checked={formData.status === 'No'}
                        onChange={(e) => updateFormData({ status: e.target.value as 'Yes' | 'No' })}
                        disabled={isSubmitting}
                        className="w-4 h-4 text-primary-50 bg-white border-2 border-outline focus:ring-2 focus:ring-primary-50 focus:ring-offset-2"
                      />
                      <Label htmlFor="status-no-horizontal" className="text-sm font-medium text-on-surface cursor-pointer">
                        No - Inactive
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Approval Entities - Enhanced */}
          {internalCurrentStep === 2 && (
            <div className="space-y-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="p-3 rounded-full bg-secondary-50 text-white">
                  <Users className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-on-surface">Pre Approval Details</h3>
                  <p className="text-base text-muted-foreground">Add multiple lender approvals</p>
                </div>
              </div>

              <div className="bg-gradient-to-br from-secondary-99 to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="p-2.5 rounded-lg bg-secondary-90 mr-4">
                      <Users className="h-6 w-6 text-secondary-40" />
                    </div>
                    <div>
                      <h4 className="font-bold text-xl text-on-surface">Pre Approval Management</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Add multiple lender approvals with comprehensive Canadian credit analysis
                      </p>
                    </div>
                  </div>
                  <Badge variant="outline" className="px-3 py-1.5 font-medium">
                    {formData.approvalEntities.length} {formData.approvalEntities.length === 1 ? 'Entity' : 'Entities'}
                  </Badge>
                </div>

                <ApprovalEntityManager
                  entities={formData.approvalEntities}
                  onChange={(entities) => updateFormData({ approvalEntities: entities })}
                  disabled={isSubmitting}
                />
              </div>

              <div className="bg-gradient-to-br from-primary-95 to-primary-99 rounded-xl p-6 border-l-4 border-primary-50 shadow-sm">
                <div className="flex items-start space-x-4">
                  <div className="p-2 rounded-lg bg-primary-90 flex-shrink-0">
                    <Shield className="h-5 w-5 text-primary-50" />
                  </div>
                  <div className="flex-1">
                    <h5 className="font-bold text-lg text-primary-20 mb-3">Canadian Credit Requirements</h5>
                    <p className="text-sm text-primary-40 leading-relaxed">
                      Each approval entity requires comprehensive credit analysis including income verification, 
                      employment history, and credit bureau reports from Equifax and TransUnion Canada.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Trade-in Details - Enhanced */}
          {internalCurrentStep === 3 && (
            <div className="space-y-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="p-3 rounded-full bg-primary-50 text-white">
                  <ArrowLeftRight className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-on-surface">Trade-in Details</h3>
                  <p className="text-base text-muted-foreground">Optional trade-in vehicle information and valuation</p>
                </div>
              </div>

              <div className="bg-gradient-to-br from-surface-bright to-surface-container-low rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center space-x-4 mb-6">
                  <Checkbox
                    id="hasTradeIn"
                    checked={formData.hasTradeIn}
                    onCheckedChange={(checked) => updateFormData({ hasTradeIn: checked as boolean })}
                    disabled={isSubmitting}
                    className="w-6 h-6"
                  />
                  <Label htmlFor="hasTradeIn" className="text-lg font-semibold text-on-surface cursor-pointer">
                    Customer has a vehicle to trade in
                  </Label>
                </div>

                {formData.hasTradeIn && (
                  <div className="bg-surface-container-lowest rounded-xl p-6 border border-outline-variant">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      <div className="space-y-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 rounded-lg bg-primary-90 mr-3">
                            <Car className="h-5 w-5 text-primary-50" />
                          </div>
                          <h4 className="font-bold text-lg text-on-surface">Vehicle Details</h4>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <TextField
                            label="Make *"
                            value={formData.tradeInMake || ''}
                            onChange={(e) => updateFormData({ tradeInMake: e.target.value })}
                            placeholder="Toyota"
                            disabled={isSubmitting}
                          />
                          <TextField
                            label="Model *"
                            value={formData.tradeInModel || ''}
                            onChange={(e) => updateFormData({ tradeInModel: e.target.value })}
                            placeholder="Camry"
                            disabled={isSubmitting}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <TextField
                            label="Year *"
                            value={formData.tradeInYear || ''}
                            onChange={(e) => updateFormData({ tradeInYear: e.target.value })}
                            placeholder="2020"
                            disabled={isSubmitting}
                          />
                          <TextField
                            label="Trim"
                            value={formData.tradeInTrim || ''}
                            onChange={(e) => updateFormData({ tradeInTrim: e.target.value })}
                            placeholder="LE"
                            disabled={isSubmitting}
                          />
                        </div>
                      </div>

                      <div className="space-y-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 rounded-lg bg-secondary-90 mr-3">
                            <DollarSign className="h-5 w-5 text-secondary-40" />
                          </div>
                          <h4 className="font-bold text-lg text-on-surface">Financial Valuation</h4>
                        </div>
                        
                        <TextField
                          label="Trade Allowance"
                          value={formData.tradeInAllowance || ''}
                          onChange={(e) => updateFormData({ tradeInAllowance: e.target.value })}
                          placeholder="$15,000"
                          disabled={isSubmitting}
                        />

                        <TextField
                          label="Fair Cash Value (FCV)"
                          value={formData.tradeInFcv || ''}
                          onChange={(e) => updateFormData({ tradeInFcv: e.target.value })}
                          placeholder="$13,500"
                          disabled={isSubmitting}
                        />

                        <TextField
                          label="Outstanding Loan"
                          value={formData.tradeInLoan || ''}
                          onChange={(e) => updateFormData({ tradeInLoan: e.target.value })}
                          placeholder="$8,000"
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 4: Vehicle Selection - Enhanced */}
          {internalCurrentStep === 4 && (
            <div className="space-y-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="p-3 rounded-full bg-primary-50 text-white">
                  <Search className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-on-surface">Vehicle Selection</h3>
                  <p className="text-base text-muted-foreground">Choose vehicles from available inventory</p>
                </div>
              </div>

              {/* Vehicle Filters */}
              <VehicleFilters
                filters={vehicleFilters}
                onFiltersChange={setVehicleFilters}
                disabled={isSubmitting}
              />

              <div className="flex items-center justify-between bg-gradient-to-r from-primary-99 to-surface-bright rounded-lg p-4 border border-outline-variant">
                <div className="flex items-center space-x-3">
                  <Car className="h-5 w-5 text-primary-50" />
                  <span className="text-base font-semibold text-on-surface">
                    Selected vehicles: {formData.selectedVehicles.length}
                  </span>
                </div>
                <Badge variant="outline" className="px-3 py-1">
                  {availableVehicles.filter(v => v.status === 'Available').length} Available
                </Badge>
              </div>
              
              <div className="border-2 border-outline-variant rounded-xl overflow-hidden bg-surface-bright">
                <MaterialTableContainer>
                  <MaterialTable>
                    <MaterialTableHeader>
                      <MaterialTableRow>
                        <MaterialTableHead className="w-12"></MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Vehicle</MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Price</MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Mileage</MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Location</MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Status</MaterialTableHead>
                      </MaterialTableRow>
                    </MaterialTableHeader>
                    <MaterialTableBody>
                      {availableVehicles.map((vehicle) => (
                        <MaterialTableRow 
                          key={vehicle.id}
                          className={`cursor-pointer hover:bg-surface-container-low transition-all duration-200 ${
                            formData.selectedVehicles.includes(vehicle.id) ? 'bg-primary-95 border-l-4 border-primary-50' : ''
                          } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={() => !isSubmitting && handleVehicleSelection(vehicle.id)}
                        >
                          <MaterialTableCell>
                            <Checkbox
                              checked={formData.selectedVehicles.includes(vehicle.id)}
                              onChange={() => handleVehicleSelection(vehicle.id)}
                              disabled={isSubmitting}
                              className="w-5 h-5"
                            />
                          </MaterialTableCell>
                          <MaterialTableCell>
                            <div className="flex items-center space-x-4">
                              <ImageWithFallback
                                src={vehicle.photos[0]}
                                alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                                className="w-16 h-16 rounded-lg object-cover border border-outline-variant"
                              />
                              <div>
                                <div className="font-semibold text-base text-on-surface">
                                  {vehicle.year} {vehicle.make} {vehicle.model}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  VIN: {vehicle.vin}
                                </div>
                              </div>
                            </div>
                          </MaterialTableCell>
                          <MaterialTableCell className="font-bold text-lg text-primary-50">
                            {vehicle.price}
                          </MaterialTableCell>
                          <MaterialTableCell className="text-base">{vehicle.mileage}</MaterialTableCell>
                          <MaterialTableCell className="text-base">{vehicle.location}</MaterialTableCell>
                          <MaterialTableCell>
                            <Badge variant="outline" className={getStatusColor(vehicle.status)}>
                              {vehicle.status}
                            </Badge>
                          </MaterialTableCell>
                        </MaterialTableRow>
                      ))}
                    </MaterialTableBody>
                  </MaterialTable>
                </MaterialTableContainer>
              </div>
            </div>
          )}

          {/* Step 5: Deal Summary - Enhanced */}
          {internalCurrentStep === 5 && (
            <div className="space-y-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="p-3 rounded-full bg-primary-50 text-white">
                  <CheckCircle className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-on-surface">Deal Summary</h3>
                  <p className="text-base text-muted-foreground">Review and finalize the deal details</p>
                </div>
              </div>

              {/* Summary Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="border-2 border-outline-variant shadow-sm hover-lift">
                  <CardContent className="p-6">
                    <h4 className="font-bold text-lg mb-4 flex items-center space-x-2">
                      <UserCheck className="h-6 w-6 text-primary-50" />
                      <span>Customer Information</span>
                    </h4>
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16 border-2 border-outline-variant">
                        <AvatarImage src={formData.customerPhoto} alt={formatName(formData.firstName, formData.lastName)} />
                        <AvatarFallback className="text-lg font-semibold">{getInitials(formData.firstName, formData.lastName)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <h5 className="font-bold text-lg text-on-surface">{formatName(formData.firstName, formData.lastName)}</h5>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div>{formatEmail(formData.emailAddress)}</div>
                          <div>{formatPhone(formData.phoneNumber)}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-2 border-outline-variant shadow-sm hover-lift">
                  <CardContent className="p-6">
                    <h4 className="font-bold text-lg mb-4 flex items-center space-x-2">
                      <Users className="h-6 w-6 text-secondary-50" />
                      <span>Approvals ({formData.approvalEntities.length})</span>
                    </h4>
                    <div className="space-y-3">
                      {formData.approvalEntities.slice(0, 2).map((entity, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border border-outline-variant rounded-lg bg-surface-container-low">
                          <div>
                            <div className="font-semibold text-base text-on-surface">{entity.firstName} {entity.lastName}</div>
                            <div className="text-sm text-muted-foreground">{entity.relationship}</div>
                          </div>
                        </div>
                      ))}
                      {formData.approvalEntities.length > 2 && (
                        <div className="text-sm text-muted-foreground text-center py-2 border border-dashed border-outline-variant rounded-lg">
                          +{formData.approvalEntities.length - 2} more entities
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="border-2 border-outline-variant shadow-sm">
                <CardContent className="p-6">
                  <h4 className="font-bold text-lg mb-4 flex items-center space-x-2">
                    <FileText className="h-6 w-6 text-tertiary-50" />
                    <span>Deal Notes</span>
                  </h4>
                  <Textarea
                    placeholder="Add any additional notes or special conditions for this deal..."
                    value={formData.dealNotes}
                    onChange={(e) => updateFormData({ dealNotes: e.target.value })}
                    className="min-h-[120px] text-base"
                    disabled={isSubmitting}
                  />
                </CardContent>
              </Card>
            </div>
          )}

          {/* Enhanced Navigation */}
          <div className="flex justify-between items-center pt-8 border-t border-outline-variant">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={internalCurrentStep === 1 || isSubmitting}
                className="flex items-center space-x-2 px-4 py-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Previous</span>
              </Button>
              
              <StepNotesButton
                notes={notes}
                onNotesToggle={onNotesToggle}
                disabled={isSubmitting}
                currentStep={internalCurrentStep}
              />
            </div>

            <div className="flex items-center space-x-4">
              {internalCurrentStep < 5 ? (
                <Button
                  onClick={handleNext}
                  disabled={!isStepComplete(internalCurrentStep) || isSubmitting}
                  className="bg-brand-orange text-white hover:bg-brand-orange-dark flex items-center space-x-2 px-6 py-3 text-base font-semibold shadow-orange-md hover:shadow-orange-lg"
                >
                  <span>Next</span>
                  <ArrowRight className="h-5 w-5" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="bg-brand-orange text-white hover:bg-brand-orange-dark flex items-center space-x-2 px-6 py-3 text-base font-semibold shadow-orange-md hover:shadow-orange-lg"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span>Creating Deal...</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-5 w-5" />
                      <span>Create Deal</span>
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}