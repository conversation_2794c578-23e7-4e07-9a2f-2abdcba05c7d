import React from 'react';

interface BrandIconProps {
  className?: string;
  size?: number;
}

export const BrandIcon: React.FC<BrandIconProps> = ({ className = "", size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Supporting Hand/Palm */}
      <path
        d="M4 16C4 16 4.5 15.5 5.5 15.5C6.5 15.5 7 16 8 16C9 16 9.5 15.5 10.5 15.5C11.5 15.5 12 16 13 16C14 16 14.5 15.5 15.5 15.5C16.5 15.5 17 16 18 16C19 16 20 16.5 20 17.5V19C20 19.5 19.5 20 19 20H5C4.5 20 4 19.5 4 19V16Z"
        fill="currentColor"
        fillOpacity="0.4"
        stroke="currentColor"
        strokeWidth="1"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      
      {/* Palm Details - Fingers */}
      <path
        d="M6 15.5V13.5C6 13 6.4 12.5 7 12.5C7.6 12.5 8 13 8 13.5V15.5"
        stroke="currentColor"
        strokeWidth="0.8"
        strokeLinecap="round"
      />
      <path
        d="M9 15.5V12.5C9 12 9.4 11.5 10 11.5C10.6 11.5 11 12 11 12.5V15.5"
        stroke="currentColor"
        strokeWidth="0.8"
        strokeLinecap="round"
      />
      <path
        d="M12.5 15.5V13C12.5 12.5 12.9 12 13.5 12C14.1 12 14.5 12.5 14.5 13V15.5"
        stroke="currentColor"
        strokeWidth="0.8"
        strokeLinecap="round"
      />
      <path
        d="M16 15.5V14C16 13.5 16.4 13 17 13C17.6 13 18 13.5 18 14V15.5"
        stroke="currentColor"
        strokeWidth="0.8"
        strokeLinecap="round"
      />

      {/* Car Body - Modern SUV/Sedan Silhouette */}
      <path
        d="M5.5 8L6.8 5.2C7 4.8 7.4 4.5 7.8 4.5H16.2C16.6 4.5 17 4.8 17.2 5.2L18.5 8"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      
      {/* Car Main Body */}
      <path
        d="M3.5 8V12.5C3.5 13 4 13.5 4.5 13.5H5.5C6 13.5 6.5 13 6.5 12.5V12H17.5V12.5C17.5 13 18 13.5 18.5 13.5H19.5C20 13.5 20.5 13 20.5 12.5V8C20.5 7.5 20 7 19.5 7H4.5C4 7 3.5 7.5 3.5 8Z"
        fill="currentColor"
        fillOpacity="0.6"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />

      {/* Car Windows */}
      <path
        d="M7 6.5H17C17 6.5 16.5 8 16 8H8C7.5 8 7 6.5 7 6.5Z"
        fill="none"
        stroke="currentColor"
        strokeWidth="1"
        strokeLinecap="round"
        strokeLinejoin="round"
      />

      {/* Car Wheels */}
      <circle 
        cx="7.5" 
        cy="12" 
        r="1.5" 
        fill="currentColor" 
        stroke="currentColor" 
        strokeWidth="0.5"
      />
      <circle 
        cx="16.5" 
        cy="12" 
        r="1.5" 
        fill="currentColor" 
        stroke="currentColor" 
        strokeWidth="0.5"
      />

      {/* Care/Trust Symbol - Small heart or dot between car and hand */}
      <circle 
        cx="12" 
        cy="14.5" 
        r="0.8" 
        fill="currentColor" 
        fillOpacity="0.8"
      />
    </svg>
  );
};