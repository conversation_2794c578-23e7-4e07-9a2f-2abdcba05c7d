/**
 * Dashboard Content Component - Enhanced with Consistent Stat Card System
 * 
 * Comprehensive dashboard with analytics, charts, and business intelligence.
 * Features 5-card metrics, inventory snapshots, earnings visualization, and operational data.
 * Now includes standardized StatCard components for consistent visual hierarchy.
 * 
 * STAT CARD ENHANCEMENTS:
 * - Consistent StatCard components for inventory snapshots
 * - Progressive variant styling (primary, secondary, accent, highlight)
 * - Standardized icon integration with Material Design 3 colors
 * - Professional hover effects and transitions
 * - Enhanced typography and spacing consistency
 * - Unified visual hierarchy across all dashboard elements
 * 
 * Vision and Design by Ritwi<PERSON>upte | Built by Figma Make - Claude Sonnet
 */

import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { EnhancedMaterialTable, TableColumn } from './ui/material/table';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';
import { Calendar, Award, DollarSign, Handshake, MapPin } from 'lucide-react';
import { DashboardStatsIcon } from './StatsIcon';
import { InventoryStatCard } from './StatCard';
import { toast } from 'sonner@2.0.3';

interface DashboardContentProps {
  dashboardStats: any[];
  inventoryStats: any[];
  popularModels: any[];
  grossEarningsData: any[];
  dealsClosedData: any[];
  totalDealsThisMonth: number;
  avgDealsPerDay: number;
  recentSales: any[];
  recentSalesColumns: TableColumn<any>[];
  locations: any[];
}

export function DashboardContent({
  dashboardStats,
  inventoryStats,
  popularModels,
  grossEarningsData,
  dealsClosedData,
  totalDealsThisMonth,
  avgDealsPerDay,
  recentSales,
  recentSalesColumns,
  locations
}: DashboardContentProps) {
  // Define progressive variants for inventory snapshot cards
  const inventoryVariants = ['primary', 'secondary', 'accent', 'highlight'] as const;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2>Dashboard Overview</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Last 30 Days
          </Button>
        </div>
      </div>

      {/* Primary Dashboard Statistics - 5-Card Layout with Consistent Icons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {dashboardStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <p className="text-sm text-primary-50">{stat.trend} from last month</p>
                </div>
                <DashboardStatsIcon icon={stat.icon} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Inventory Snapshot Section - Enhanced with Consistent StatCard Components */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Inventory Snapshot</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {inventoryStats.map((stat, index) => (
            <InventoryStatCard
              key={index}
              title={stat.title}
              value={stat.value}
              trend={stat.trend}
              trendLabel={stat.trendLabel}
              icon={stat.icon}
              variant={inventoryVariants[index] || 'primary'}
              onClick={() => {
                toast.info(`View ${stat.title.toLowerCase()} details`, {
                  description: `Current ${stat.title.toLowerCase()}: ${stat.value}`,
                  duration: 3000,
                });
              }}
            />
          ))}
        </div>
      </div>

      {/* Business Intelligence Visualization Section - Enhanced with Chart Icons */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top 5 Models Performance */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="chart-icon" />
              <span>Top 5 Models - Last 30 Days</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {popularModels.map((model, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                      index === 0 ? 'bg-primary-50 text-white' :
                      index === 1 ? 'bg-primary-60 text-white' :
                      index === 2 ? 'bg-primary-70 text-primary-20' :
                      index === 3 ? 'bg-primary-80 text-primary-30' :
                      'bg-primary-90 text-primary-40'
                    } font-bold text-sm`}>
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-semibold text-sm">{model.model}</p>
                      <p className="text-xs text-muted-foreground">{model.sales} units sold</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-brand-orange">{model.percentage}%</p>
                    <p className="text-xs text-primary-50">{model.trend}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Gross Earnings Trend Analysis */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="chart-icon" />
              <span>Gross Earnings - Last 6 Months</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={grossEarningsData}>
                  <defs>
                    <linearGradient id="earningsGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="var(--primary-50)" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="var(--primary-50)" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="month" 
                    tick={{ fontSize: 12 }}
                    axisLine={false}
                    tickLine={false}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    axisLine={false}
                    tickLine={false}
                    tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
                  />
                  <RechartsTooltip 
                    formatter={(value) => [`$${value.toLocaleString()}`, 'Earnings']}
                    labelStyle={{ color: '#374151' }}
                    contentStyle={{ 
                      backgroundColor: 'white', 
                      border: '1px solid #e5e7eb', 
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="earnings" 
                    stroke="var(--primary-50)" 
                    strokeWidth={2}
                    fill="url(#earningsGradient)" 
                  />
                  <Line 
                    type="monotone" 
                    dataKey="target" 
                    stroke="var(--primary-30)" 
                    strokeDasharray="5 5"
                    strokeWidth={1}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">Average: $171K/month</p>
              <p className="text-xs text-primary-50">↗ 14% vs last period</p>
            </div>
          </CardContent>
        </Card>

        {/* Deal Closure Performance */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Handshake className="chart-icon" />
              <span>Deals Closed - Last 30 Days</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={dealsClosedData.slice(-15)} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 10 }}
                    axisLine={false}
                    tickLine={false}
                    interval={1}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    axisLine={false}
                    tickLine={false}
                  />
                  <RechartsTooltip 
                    formatter={(value) => [`${value}`, 'Deals']}
                    labelStyle={{ color: '#374151' }}
                    contentStyle={{ 
                      backgroundColor: 'white', 
                      border: '1px solid #e5e7eb', 
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Bar 
                    dataKey="deals" 
                    fill="var(--primary-50)" 
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 flex justify-between text-center">
              <div>
                <p className="text-2xl font-bold text-primary-50">{totalDealsThisMonth}</p>
                <p className="text-xs text-muted-foreground">Total Deals</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-primary-60">{avgDealsPerDay}</p>
                <p className="text-xs text-muted-foreground">Avg/Day</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Operational Intelligence Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Sales Activity Table */}
        <EnhancedMaterialTable
          data={recentSales}
          columns={recentSalesColumns}
          rowKey="id"
          selectable={false}
          sortable={true}
          filterable={true}
          searchable={true}
          paginated={false}
          exportable={true}
          containerClassName="h-fit"
          className="text-sm"
          onRowClick={(sale) => {
            toast.info(`View details for sale #${sale.id}`);
          }}
        />

        {/* Top Performing Locations */}
        <Card>
          <CardHeader>
            <CardTitle>Top Locations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {locations.slice(1, 5).map((location) => (
                <div key={location.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MapPin className="status-icon available" />
                    <span className="font-semibold">{location.name}</span>
                  </div>
                  <Badge variant="outline" className="border-neutral-variant-50 text-neutral-80">
                    {location.count} vehicles
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}