import { useState } from 'react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Separator } from './ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from './ui/breadcrumb';
import { TextField } from './ui/material/text-field';
import { SelectField, SelectFieldContent, SelectFieldItem, SelectFieldTrigger, SelectFieldValue } from './ui/material/select-field';
import { Textarea } from './ui/material/textarea';
import { Checkbox } from './ui/material/checkbox';
import { toast } from 'sonner@2.0.3';
import { 
  Car, 
  ArrowLeft, 
  ArrowRight, 
  Check, 
  Upload, 
  X,
  Plus,
  Fuel,
  Settings,
  DollarSign,
  Camera,
  FileText,
  MapPin,
  Calendar,
  Gauge,
  Zap,
  Shield,
  Info
} from 'lucide-react';

interface AddVehicleFormProps {
  onBack: () => void;
  onSubmit: (vehicleData: any) => void;
  locations: Array<{ id: string; name: string; count: number }>;
}

export function AddVehicleForm({ onBack, onSubmit, locations }: AddVehicleFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 7;

  // Form state
  const [vehicleData, setVehicleData] = useState({
    // Basic Information (Step 1)
    year: '',
    make: '',
    model: '',
    trim: '',
    bodyStyle: '',
    vin: '',
    
    // Specifications (Step 2)
    engine: '',
    transmission: '',
    drivetrain: '',
    fuelType: '',
    fuelEconomy: '',
    mileage: '',
    exteriorColor: '',
    interiorColor: '',
    interiorMaterial: '',
    
    // Pricing & Status (Step 3)
    cost: '',
    price: '',
    status: 'Available',
    condition: '',
    
    // Features & Options (Step 4)
    features: [],
    safetyFeatures: [],
    techFeatures: [],
    comfortFeatures: [],
    
    // Location & Details (Step 5)
    location: '',
    keyCount: '2',
    serviceRecords: true,
    accidentHistory: 'None',
    previousOwners: '1',
    
    // Photos & Documentation (Step 6)
    photos: [],
    documents: [],
    
    // Canadian Compliance (Step 7)
    transportCanadaApproved: true,
    emissionsCompliant: true,
    safetyStandard: 'CMVSS',
    drlCompliant: true,
    metricSpeedometer: true,
    blockHeater: false,
    winterPackage: false,
    notes: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const canadianMakes = [
    'Acura', 'Audi', 'BMW', 'Buick', 'Cadillac', 'Chevrolet', 'Chrysler', 
    'Dodge', 'Ford', 'Genesis', 'GMC', 'Honda', 'Hyundai', 'Infiniti', 
    'Jaguar', 'Jeep', 'Kia', 'Land Rover', 'Lexus', 'Lincoln', 'Mazda', 
    'Mercedes-Benz', 'MINI', 'Mitsubishi', 'Nissan', 'Porsche', 'Ram', 
    'Subaru', 'Tesla', 'Toyota', 'Volkswagen', 'Volvo'
  ];

  const bodyStyles = [
    'Sedan', 'Hatchback', 'SUV', 'Crossover', 'Pickup Truck', 'Coupe', 
    'Convertible', 'Wagon', 'Van', 'Minivan'
  ];

  const drivetrainOptions = [
    'FWD', 'RWD', 'AWD', 'Intelligent AWD', '4WD', 'xDrive AWD', 'quattro AWD', '4MATIC AWD'
  ];

  const fuelTypes = [
    'Gasoline', 'Diesel', 'Hybrid', 'Plug-in Hybrid', 'Electric', 'Flex Fuel'
  ];

  const standardFeatures = [
    'Navigation System', 'Backup Camera', 'Heated Seats', 'Remote Start', 'Sunroof', 
    'Leather Seats', 'Apple CarPlay', 'Android Auto', 'Bluetooth', 'USB Ports',
    'Wireless Charging', 'Power Seats', 'Memory Seats', 'Heated Steering Wheel',
    'Dual Zone Climate', 'Rear Entertainment', 'Premium Audio', 'Power Liftgate'
  ];

  const safetyFeatures = [
    'Adaptive Cruise Control', 'Lane Keep Assist', 'Blind Spot Monitoring', 
    'Forward Collision Warning', 'Automatic Emergency Braking', 'Rear Cross Traffic Alert',
    'Lane Departure Warning', 'Driver Attention Monitoring', 'Night Vision',
    'Surround View Camera', 'Parking Sensors', 'Traffic Sign Recognition'
  ];

  const techFeatures = [
    'Head-Up Display', 'Digital Instrument Cluster', 'Gesture Control', 
    'Voice Control', 'Wi-Fi Hotspot', 'OTA Updates', 'Mobile App Integration',
    'Remote Diagnostics', 'Digital Key', 'Augmented Reality Navigation'
  ];

  const comfortFeatures = [
    'Ventilated Seats', 'Massage Seats', 'Ambient Lighting', 'Air Suspension',
    'Adaptive Suspension', 'Noise Cancellation', 'Heated/Cooled Cup Holders',
    'Power Running Boards', 'Hands-Free Liftgate', 'Easy Entry/Exit'
  ];

  const updateVehicleData = (field: string, value: any) => {
    try {
      setVehicleData(prev => ({ ...prev, [field]: value }));
      
      // Clear related errors when user starts updating
      if (errors[field]) {
        const newErrors = { ...errors };
        delete newErrors[field];
        setErrors(newErrors);
      }
    } catch (error) {
      console.error('Error updating vehicle data:', error);
      toast.error(
        'Form update error',
        {
          description: 'Unable to update vehicle information. Please refresh the page and try again.',
          duration: 4000,
        }
      );
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!vehicleData.year) {
          newErrors.year = 'Year is required';
        } else if (parseInt(vehicleData.year) < 1980 || parseInt(vehicleData.year) > new Date().getFullYear() + 1) {
          newErrors.year = 'Invalid year range';
        }
        
        if (!vehicleData.make) {
          newErrors.make = 'Make is required';
        }
        
        if (!vehicleData.model.trim()) {
          newErrors.model = 'Model is required';
        } else if (vehicleData.model.trim().length < 2) {
          newErrors.model = 'Model must be at least 2 characters long';
        }
        
        if (!vehicleData.bodyStyle) {
          newErrors.bodyStyle = 'Body style is required';
        }
        
        if (!vehicleData.vin.trim()) {
          newErrors.vin = 'VIN is required';
        } else if (!/^[A-HJ-NPR-Z0-9]{17}$/.test(vehicleData.vin)) {
          newErrors.vin = 'VIN must be exactly 17 characters (letters and numbers, no I, O, or Q)';
        }
        break;
        
      case 2:
        if (!vehicleData.fuelType) {
          newErrors.fuelType = 'Fuel type is required';
        }
        
        if (!vehicleData.mileage.trim()) {
          newErrors.mileage = 'Mileage is required';
        } else {
          const mileageNum = parseInt(vehicleData.mileage.replace(/\D/g, ''));
          if (isNaN(mileageNum) || mileageNum < 0) {
            newErrors.mileage = 'Invalid mileage value';
          } else if (mileageNum > 500000) {
            newErrors.mileage = 'Mileage seems unusually high. Please verify.';
          }
        }
        
        if (!vehicleData.exteriorColor.trim()) {
          newErrors.exteriorColor = 'Exterior color is required';
        }
        break;
        
      case 3:
        if (!vehicleData.cost.trim()) {
          newErrors.cost = 'Dealer cost is required';
        } else {
          const costNum = parseFloat(vehicleData.cost.replace(/[$,]/g, ''));
          if (isNaN(costNum) || costNum <= 0) {
            newErrors.cost = 'Invalid cost amount';
          } else if (costNum > 500000) {
            newErrors.cost = 'Cost seems unusually high. Please verify.';
          }
        }
        
        if (!vehicleData.price.trim()) {
          newErrors.price = 'Asking price is required';
        } else {
          const priceNum = parseFloat(vehicleData.price.replace(/[$,]/g, ''));
          const costNum = parseFloat(vehicleData.cost.replace(/[$,]/g, ''));
          if (isNaN(priceNum) || priceNum <= 0) {
            newErrors.price = 'Invalid price amount';
          } else if (!isNaN(costNum) && priceNum < costNum) {
            newErrors.price = 'Asking price should not be less than dealer cost';
          } else if (priceNum > 1000000) {
            newErrors.price = 'Price seems unusually high. Please verify.';
          }
        }
        break;
        
      case 5:
        if (!vehicleData.location) {
          newErrors.location = 'Dealership location is required';
        }
        break;
    }

    setErrors(newErrors);
    
    // Show error toasts for validation failures
    if (Object.keys(newErrors).length > 0) {
      const errorMessages = Object.values(newErrors);
      toast.error(
        `Please fix the following errors to continue:`,
        {
          description: errorMessages.join(', '),
          duration: 5000,
        }
      );
    }
    
    return Object.keys(newErrors).length === 0;
  };

  const toggleFeature = (category: string, feature: string) => {
    try {
      setVehicleData(prev => {
        const currentFeatures = prev[category] || [];
        const isSelected = currentFeatures.includes(feature);
        const allFeatures = [
          ...prev.features,
          ...prev.safetyFeatures,
          ...prev.techFeatures,
          ...prev.comfortFeatures
        ];
        
        // Limit total features to prevent overwhelming the display
        if (!isSelected && allFeatures.length >= 15) {
          toast.error(
            'Too many features selected',
            {
              description: 'Please select a maximum of 15 total features across all categories.',
              duration: 4000,
            }
          );
          return prev;
        }
        
        return {
          ...prev,
          [category]: isSelected 
            ? currentFeatures.filter(f => f !== feature)
            : [...currentFeatures, feature]
        };
      });
    } catch (error) {
      console.error('Error updating features:', error);
      toast.error(
        'Feature update error',
        {
          description: 'Unable to update vehicle features. Please try again.',
          duration: 4000,
        }
      );
    }
  };

  const nextStep = () => {
    if (!validateStep(currentStep)) {
      return;
    }

    try {
      if (currentStep < totalSteps) {
        setCurrentStep(currentStep + 1);
      }
    } catch (error) {
      console.error('Error proceeding to next step:', error);
      toast.error(
        'Navigation error',
        {
          description: 'Unable to proceed to the next step. Please try again.',
          duration: 4000,
        }
      );
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    if (!validateStep(currentStep)) {
      return;
    }

    try {
      // Additional business logic validation
      const vinCheck = vehicleData.vin.toUpperCase();
      
      // Mock check for duplicate VIN (in real app, this would be an API call)
      if (vinCheck === '1HGCM82633A000000') {
        toast.error(
          'Duplicate VIN detected',
          {
            description: `A vehicle with VIN ${vehicleData.vin} already exists in the inventory.`,
            duration: 5000,
          }
        );
        return;
      }

      // Validate Canadian compliance for used vehicles
      if (vehicleData.condition !== 'New' && !vehicleData.transportCanadaApproved) {
        toast.error(
          'Compliance requirement missing',
          {
            description: 'Used vehicles must be Transport Canada approved for sale in Canada.',
            duration: 5000,
          }
        );
        return;
      }

      // Check pricing logic
      const costNum = parseFloat(vehicleData.cost.replace(/[$,]/g, ''));
      const priceNum = parseFloat(vehicleData.price.replace(/[$,]/g, ''));
      const markup = ((priceNum - costNum) / costNum) * 100;
      
      if (markup > 50) {
        toast.error(
          'Unusual pricing detected',
          {
            description: `The markup of ${markup.toFixed(1)}% seems high. Please verify pricing is correct.`,
            duration: 6000,
          }
        );
        return;
      }

      // Process the form data
      const processedData = {
        ...vehicleData,
        id: Date.now(), // Generate unique ID
        daysOnLot: 0,
        vin: vehicleData.vin.toUpperCase(), // Normalize VIN
        photos: vehicleData.photos.length > 0 ? vehicleData.photos : [
          'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=400&h=300&fit=crop'
        ],
        features: [
          ...vehicleData.features,
          ...vehicleData.safetyFeatures,
          ...vehicleData.techFeatures,
          ...vehicleData.comfortFeatures
        ].slice(0, 8) // Limit to 8 features for display
      };
      
      onSubmit(processedData);
      
    } catch (error) {
      console.error('Error creating vehicle:', error);
      toast.error(
        'Failed to create vehicle',
        {
          description: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
          duration: 6000,
        }
      );
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Car className="h-5 w-5 text-brand-orange" />
                <span>Basic Vehicle Information</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Enter the basic information about the vehicle including year, make, model, and VIN number.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <SelectField 
                  value={vehicleData.year} 
                  onValueChange={(value) => updateVehicleData('year', value)}
                >
                  <SelectFieldTrigger label="Year *">
                    <SelectFieldValue placeholder="Select year" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    {Array.from({ length: 25 }, (_, i) => 2025 - i).map(year => (
                      <SelectFieldItem key={year} value={year.toString()}>{year}</SelectFieldItem>
                    ))}
                  </SelectFieldContent>
                </SelectField>
                
                <SelectField 
                  value={vehicleData.make} 
                  onValueChange={(value) => updateVehicleData('make', value)}
                >
                  <SelectFieldTrigger label="Make *">
                    <SelectFieldValue placeholder="Select make" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    {canadianMakes.map(make => (
                      <SelectFieldItem key={make} value={make}>{make}</SelectFieldItem>
                    ))}
                  </SelectFieldContent>
                </SelectField>
                
                <TextField
                  label="Model *"
                  value={vehicleData.model}
                  onChange={(e) => updateVehicleData('model', e.target.value)}
                  placeholder="Enter model name"
                  error={!!errors.model}
                  helperText={errors.model}
                  required
                />
                
                <TextField
                  label="Trim Level"
                  value={vehicleData.trim}
                  onChange={(e) => updateVehicleData('trim', e.target.value)}
                  placeholder="e.g., LX, EX, Touring"
                />
                
                <SelectField 
                  value={vehicleData.bodyStyle} 
                  onValueChange={(value) => updateVehicleData('bodyStyle', value)}
                >
                  <SelectFieldTrigger label="Body Style *">
                    <SelectFieldValue placeholder="Select body style" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    {bodyStyles.map(style => (
                      <SelectFieldItem key={style} value={style}>{style}</SelectFieldItem>
                    ))}
                  </SelectFieldContent>
                </SelectField>
                
                <TextField
                  label="VIN Number *"
                  value={vehicleData.vin}
                  onChange={(e) => updateVehicleData('vin', e.target.value.toUpperCase())}
                  placeholder="17-character VIN"
                  maxLength={17}
                  error={!!errors.vin}
                  helperText={errors.vin}
                  required
                />
              </div>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-brand-orange" />
                <span>Vehicle Specifications</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Add detailed specifications including engine, transmission, fuel type, and appearance details.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <TextField
                  label="Engine"
                  value={vehicleData.engine}
                  onChange={(e) => updateVehicleData('engine', e.target.value)}
                  placeholder="e.g., 2.4L 4-Cylinder"
                />
                
                <TextField
                  label="Transmission"
                  value={vehicleData.transmission}
                  onChange={(e) => updateVehicleData('transmission', e.target.value)}
                  placeholder="e.g., CVT, 8-Speed Automatic"
                />
                
                <SelectField 
                  value={vehicleData.drivetrain} 
                  onValueChange={(value) => updateVehicleData('drivetrain', value)}
                >
                  <SelectFieldTrigger label="Drivetrain">
                    <SelectFieldValue placeholder="Select drivetrain" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    {drivetrainOptions.map(option => (
                      <SelectFieldItem key={option} value={option}>{option}</SelectFieldItem>
                    ))}
                  </SelectFieldContent>
                </SelectField>
                
                <SelectField 
                  value={vehicleData.fuelType} 
                  onValueChange={(value) => updateVehicleData('fuelType', value)}
                >
                  <SelectFieldTrigger label="Fuel Type *">
                    <SelectFieldValue placeholder="Select fuel type" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    {fuelTypes.map(type => (
                      <SelectFieldItem key={type} value={type}>{type}</SelectFieldItem>
                    ))}
                  </SelectFieldContent>
                </SelectField>
                
                <TextField
                  label="Fuel Economy (L/100km)"
                  value={vehicleData.fuelEconomy}
                  onChange={(e) => updateVehicleData('fuelEconomy', e.target.value)}
                  placeholder="e.g., 8.5L/100km city, 6.2L/100km highway"
                />
                
                <TextField
                  label="Mileage (km) *"
                  type="number"
                  value={vehicleData.mileage.replace(' km', '')}
                  onChange={(e) => updateVehicleData('mileage', e.target.value + ' km')}
                  placeholder="Enter kilometers"
                />
                
                <TextField
                  label="Exterior Color *"
                  value={vehicleData.exteriorColor}
                  onChange={(e) => updateVehicleData('exteriorColor', e.target.value)}
                  placeholder="e.g., Midnight Black, Pearl White"
                />
                
                <TextField
                  label="Interior Color"
                  value={vehicleData.interiorColor}
                  onChange={(e) => updateVehicleData('interiorColor', e.target.value)}
                  placeholder="e.g., Black Leather, Beige Cloth"
                />
              </div>
            </CardContent>
          </Card>
        );

      case 3:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5 text-brand-orange" />
                <span>Pricing & Status</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Set pricing information, vehicle status, condition, and ownership history details.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <TextField
                  label="Dealer Cost (CAD) *"
                  value={vehicleData.cost}
                  onChange={(e) => updateVehicleData('cost', '$' + e.target.value.replace(/[^0-9]/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ','))}
                  placeholder="$25,000"
                />
                
                <TextField
                  label="Asking Price (CAD) *"
                  value={vehicleData.price}
                  onChange={(e) => updateVehicleData('price', '$' + e.target.value.replace(/[^0-9]/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ','))}
                  placeholder="$28,999"
                />
                
                <SelectField 
                  value={vehicleData.status} 
                  onValueChange={(value) => updateVehicleData('status', value)}
                >
                  <SelectFieldTrigger label="Vehicle Status">
                    <SelectFieldValue />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    <SelectFieldItem value="Available">Available</SelectFieldItem>
                    <SelectFieldItem value="Reserved">Reserved</SelectFieldItem>
                    <SelectFieldItem value="Sold">Sold</SelectFieldItem>
                    <SelectFieldItem value="In Transit">In Transit</SelectFieldItem>
                  </SelectFieldContent>
                </SelectField>
                
                <SelectField 
                  value={vehicleData.condition} 
                  onValueChange={(value) => updateVehicleData('condition', value)}
                >
                  <SelectFieldTrigger label="Condition">
                    <SelectFieldValue placeholder="Select condition" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    <SelectFieldItem value="New">New</SelectFieldItem>
                    <SelectFieldItem value="Certified Pre-Owned">Certified Pre-Owned</SelectFieldItem>
                    <SelectFieldItem value="Used - Excellent">Used - Excellent</SelectFieldItem>
                    <SelectFieldItem value="Used - Good">Used - Good</SelectFieldItem>
                    <SelectFieldItem value="Used - Fair">Used - Fair</SelectFieldItem>
                  </SelectFieldContent>
                </SelectField>
                
                <SelectField 
                  value={vehicleData.previousOwners} 
                  onValueChange={(value) => updateVehicleData('previousOwners', value)}
                >
                  <SelectFieldTrigger label="Previous Owners">
                    <SelectFieldValue />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    <SelectFieldItem value="0">0 (New Vehicle)</SelectFieldItem>
                    <SelectFieldItem value="1">1 Previous Owner</SelectFieldItem>
                    <SelectFieldItem value="2">2 Previous Owners</SelectFieldItem>
                    <SelectFieldItem value="3">3 Previous Owners</SelectFieldItem>
                    <SelectFieldItem value="4+">4+ Previous Owners</SelectFieldItem>
                  </SelectFieldContent>
                </SelectField>
                
                <SelectField 
                  value={vehicleData.accidentHistory} 
                  onValueChange={(value) => updateVehicleData('accidentHistory', value)}
                >
                  <SelectFieldTrigger label="Accident History">
                    <SelectFieldValue />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    <SelectFieldItem value="None">No Accidents Reported</SelectFieldItem>
                    <SelectFieldItem value="Minor">Minor Accident History</SelectFieldItem>
                    <SelectFieldItem value="Moderate">Moderate Accident History</SelectFieldItem>
                    <SelectFieldItem value="Major">Major Accident History</SelectFieldItem>
                  </SelectFieldContent>
                </SelectField>
              </div>
            </CardContent>
          </Card>
        );

      case 4:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-brand-orange" />
                <span>Features & Options</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Select all applicable features and options including standard, safety, technology, and comfort features.
              </p>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-8">
                <div>
                  <h4 className="font-medium mb-4">Standard Features</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {standardFeatures.map((feature) => (
                      <div key={feature} className="flex items-center space-x-3">
                        <Checkbox
                          id={feature}
                          checked={vehicleData.features.includes(feature)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              toggleFeature('features', feature);
                            } else {
                              toggleFeature('features', feature);
                            }
                          }}
                        />
                        <label htmlFor={feature} className="text-sm cursor-pointer">{feature}</label>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium mb-4">Safety Features</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {safetyFeatures.map((feature) => (
                      <div key={feature} className="flex items-center space-x-3">
                        <Checkbox
                          id={`safety-${feature}`}
                          checked={vehicleData.safetyFeatures.includes(feature)}
                          onCheckedChange={() => toggleFeature('safetyFeatures', feature)}
                        />
                        <label htmlFor={`safety-${feature}`} className="text-sm cursor-pointer">{feature}</label>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium mb-4">Technology Features</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {techFeatures.map((feature) => (
                      <div key={feature} className="flex items-center space-x-3">
                        <Checkbox
                          id={`tech-${feature}`}
                          checked={vehicleData.techFeatures.includes(feature)}
                          onCheckedChange={() => toggleFeature('techFeatures', feature)}
                        />
                        <label htmlFor={`tech-${feature}`} className="text-sm cursor-pointer">{feature}</label>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium mb-4">Comfort Features</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {comfortFeatures.map((feature) => (
                      <div key={feature} className="flex items-center space-x-3">
                        <Checkbox
                          id={`comfort-${feature}`}
                          checked={vehicleData.comfortFeatures.includes(feature)}
                          onCheckedChange={() => toggleFeature('comfortFeatures', feature)}
                        />
                        <label htmlFor={`comfort-${feature}`} className="text-sm cursor-pointer">{feature}</label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 5:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5 text-brand-orange" />
                <span>Location & Vehicle Details</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Choose the dealership location and specify additional vehicle details like keys and service records.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <SelectField 
                  value={vehicleData.location} 
                  onValueChange={(value) => updateVehicleData('location', value)}
                >
                  <SelectFieldTrigger label="Dealership Location *">
                    <SelectFieldValue placeholder="Select location" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    {locations.filter(loc => loc.id !== 'all').map((location) => (
                      <SelectFieldItem key={location.id} value={location.name}>
                        {location.name}
                      </SelectFieldItem>
                    ))}
                  </SelectFieldContent>
                </SelectField>
                
                <SelectField 
                  value={vehicleData.keyCount} 
                  onValueChange={(value) => updateVehicleData('keyCount', value)}
                >
                  <SelectFieldTrigger label="Number of Keys">
                    <SelectFieldValue />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    <SelectFieldItem value="1">1 Key</SelectFieldItem>
                    <SelectFieldItem value="2">2 Keys</SelectFieldItem>
                    <SelectFieldItem value="3">3 Keys</SelectFieldItem>
                    <SelectFieldItem value="4+">4+ Keys</SelectFieldItem>
                  </SelectFieldContent>
                </SelectField>
                
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="serviceRecords"
                    checked={vehicleData.serviceRecords}
                    onCheckedChange={(checked) => updateVehicleData('serviceRecords', checked)}
                  />
                  <label htmlFor="serviceRecords" className="text-sm cursor-pointer">Service Records Available</label>
                </div>
                
                <SelectField 
                  value={vehicleData.interiorMaterial} 
                  onValueChange={(value) => updateVehicleData('interiorMaterial', value)}
                >
                  <SelectFieldTrigger label="Interior Material">
                    <SelectFieldValue placeholder="Select material" />
                  </SelectFieldTrigger>
                  <SelectFieldContent>
                    <SelectFieldItem value="Cloth">Cloth</SelectFieldItem>
                    <SelectFieldItem value="Leatherette">Leatherette</SelectFieldItem>
                    <SelectFieldItem value="Leather">Leather</SelectFieldItem>
                    <SelectFieldItem value="Premium Leather">Premium Leather</SelectFieldItem>
                    <SelectFieldItem value="Alcantara">Alcantara</SelectFieldItem>
                  </SelectFieldContent>
                </SelectField>
              </div>
            </CardContent>
          </Card>
        );

      case 6:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5 text-brand-orange" />
                <span>Photos & Documentation</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Upload vehicle photos and provide required documentation for the listing.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Vehicle Photos</label>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Drag and drop photos here, or click to browse
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Recommended: Exterior (4 angles), Interior (3-4 photos), Engine bay
                  </p>
                  <Button variant="outline" className="mt-3">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Photos
                  </Button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Required Documents</label>
                <div className="space-y-3 mt-2">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4" />
                      <span className="text-sm">Vehicle Registration</span>
                    </div>
                    <Badge variant="outline">Required</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4" />
                      <span className="text-sm">Safety Certificate (if applicable)</span>
                    </div>
                    <Badge variant="outline">Recommended</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4" />
                      <span className="text-sm">CARFAX Report</span>
                    </div>
                    <Badge variant="outline">Recommended</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4" />
                      <span className="text-sm">Service History</span>
                    </div>
                    <Badge variant="outline">Optional</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 7:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-brand-orange" />
                <span>Canadian Compliance & Final Details</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Verify Canadian compliance requirements and add any additional notes for this vehicle.
              </p>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
                <h4 className="font-medium mb-4">Canadian Vehicle Standards</h4>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="transportCanada"
                      checked={vehicleData.transportCanadaApproved}
                      onCheckedChange={(checked) => updateVehicleData('transportCanadaApproved', checked)}
                    />
                    <label htmlFor="transportCanada" className="text-sm cursor-pointer">Transport Canada Approved</label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="emissions"
                      checked={vehicleData.emissionsCompliant}
                      onCheckedChange={(checked) => updateVehicleData('emissionsCompliant', checked)}
                    />
                    <label htmlFor="emissions" className="text-sm cursor-pointer">Emissions Compliant</label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="drl"
                      checked={vehicleData.drlCompliant}
                      onCheckedChange={(checked) => updateVehicleData('drlCompliant', checked)}
                    />
                    <label htmlFor="drl" className="text-sm cursor-pointer">DRL (Daytime Running Lights) Compliant</label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="metric"
                      checked={vehicleData.metricSpeedometer}
                      onCheckedChange={(checked) => updateVehicleData('metricSpeedometer', checked)}
                    />
                    <label htmlFor="metric" className="text-sm cursor-pointer">Metric Speedometer</label>
                  </div>
                </div>
              </div>
              
              <div className="bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg">
                <h4 className="font-medium mb-4">Canadian Winter Features</h4>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="blockHeater"
                      checked={vehicleData.blockHeater}
                      onCheckedChange={(checked) => updateVehicleData('blockHeater', checked)}
                    />
                    <label htmlFor="blockHeater" className="text-sm cursor-pointer">Block Heater Installed</label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="winterPackage"
                      checked={vehicleData.winterPackage}
                      onCheckedChange={(checked) => updateVehicleData('winterPackage', checked)}
                    />
                    <label htmlFor="winterPackage" className="text-sm cursor-pointer">Winter Package</label>
                  </div>
                </div>
              </div>
              
              <SelectField 
                value={vehicleData.safetyStandard} 
                onValueChange={(value) => updateVehicleData('safetyStandard', value)}
              >
                <SelectFieldTrigger label="Safety Standard">
                  <SelectFieldValue />
                </SelectFieldTrigger>
                <SelectFieldContent>
                  <SelectFieldItem value="CMVSS">CMVSS (Canadian Motor Vehicle Safety Standards)</SelectFieldItem>
                  <SelectFieldItem value="FMVSS">FMVSS (US Standard - Requires Conversion)</SelectFieldItem>
                </SelectFieldContent>
              </SelectField>
              
              <Textarea
                label="Additional Notes"
                value={vehicleData.notes}
                onChange={(e) => updateVehicleData('notes', e.target.value)}
                placeholder="Any additional information about this vehicle..."
                rows={4}
              />
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  const isStepComplete = () => {
    switch (currentStep) {
      case 1:
        return vehicleData.year && vehicleData.make && vehicleData.model && vehicleData.bodyStyle && vehicleData.vin;
      case 2:
        return vehicleData.fuelType && vehicleData.mileage && vehicleData.exteriorColor;
      case 3:
        return vehicleData.cost && vehicleData.price;
      case 4:
        return true; // Features are optional
      case 5:
        return vehicleData.location;
      case 6:
        return true; // Photos are recommended but not required
      case 7:
        return true; // Final step
      default:
        return false;
    }
  };

  const getStepIcon = (stepNumber: number) => {
    if (stepNumber < currentStep) {
      return <Check className="h-3 w-3" />;
    } else {
      return stepNumber;
    }
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink onClick={onBack} className="flex items-center space-x-1 cursor-pointer hover:text-brand-orange transition-colors">
                <Car className="h-4 w-4" />
                <span>Inventory</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="font-medium">Add New Vehicle</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Step Progress Tracker - Direct on Page */}
      <div className="flex items-center space-x-1">
        {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step) => (
          <div key={step} className="flex items-center">
            <div 
              className={`
                flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium transition-all hover:scale-105 border-2 cursor-pointer
                ${step < currentStep ? 'bg-white text-green-500 border-green-500' : 
                  step === currentStep ? 'bg-white text-brand-orange border-brand-orange' : 
                  'bg-white text-gray-600 border-gray-300 hover:border-brand-orange hover:text-brand-orange'}
              `}
              onClick={() => setCurrentStep(step)}
            >
              {getStepIcon(step)}
            </div>
            {step < totalSteps && (
              <div className={`w-6 h-0.5 mx-0.5 ${
                step < currentStep ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      {renderStepContent()}

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between">
        <Button 
          variant="outline" 
          onClick={prevStep}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="flex space-x-3">
          <Button variant="outline" onClick={onBack}>
            Cancel
          </Button>
          
          {currentStep < totalSteps ? (
            <Button 
              onClick={nextStep}
              disabled={!isStepComplete()}
              className="bg-brand-orange text-white hover:bg-brand-orange-dark"
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button 
              onClick={handleSubmit}
              disabled={!isStepComplete()}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              <Check className="h-4 w-4 mr-2" />
              Add Vehicle
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}