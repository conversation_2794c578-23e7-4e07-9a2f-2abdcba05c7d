/**
 * ApprovalEntityManager Component - Streamlined Approval Configuration
 * 
 * Streamlined approval configuration management focusing on financial terms
 * and lender details for Canadian auto credit processing.
 * 
 * Features:
 * - Add/Edit/Remove multiple approval configurations per deal
 * - Essential financial fields: Lender, Tier, Rate, Payment, Max Finance, Reserve, Holdback, GPS Fee
 * - Material Design 3 form components
 * - Real-time validation and error handling
 * 
 * Vision and Design by <PERSON>it<PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from './ui/alert-dialog';

// Material Design 3 Components
import { TextField } from './ui/material/text-field';
import { 
  SelectField, 
  SelectFieldTrigger, 
  SelectFieldContent, 
  SelectFieldItem, 
  SelectFieldValue 
} from './ui/material/select-field';
import { Checkbox } from './ui/material/checkbox';


import { ApprovalEntity } from '../types';
import { 
  Users, Plus, Edit, Trash2, CreditCard, Building, 
  CheckCircle2, DollarSign, AlertTriangle
} from 'lucide-react';

interface ApprovalEntityManagerProps {
  entities: ApprovalEntity[];
  onChange: (entities: ApprovalEntity[]) => void;
  readonly?: boolean;
}



const initialEntityData: Omit<ApprovalEntity, 'id' | 'createdDate' | 'updatedDate'> = {
  type: 'Primary Applicant',
  name: 'Approval Configuration',
  email: '<EMAIL>',
  phone: '************',
  drivingLicenseNumber: 'N/A',
  address: 'N/A',
  city: 'N/A',
  province: 'ON',
  postalCode: 'N/A',
  dateOfBirth: '1990-01-01',
  sin: 'N/A',
  employmentStatus: 'Employed',
  employer: 'N/A',
  jobTitle: 'N/A',
  annualIncome: '0',
  monthsAtJob: '0',
  creditScore: '650',
  approvalAmount: '0',
  interestRate: '',
  monthlyPayment: '',
  downPayment: '0',
  lenderName: '',
  approvalDate: '',
  termLength: '60',
  creditHistory: 'Good',
  bankruptcyHistory: false,
  bankruptcyDate: '',
  notes: '',
  // Streamlined approval fields - these are the focus
  tier: '',
  maxFinance: '',
  reserve: '',
  holdback: '',
  gpsEnabled: false,
  gpsFee: ''
};

export function ApprovalEntityManager({ entities, onChange, readonly = false }: ApprovalEntityManagerProps) {
  const [selectedEntity, setSelectedEntity] = useState<ApprovalEntity | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [entityToDelete, setEntityToDelete] = useState<ApprovalEntity | null>(null);

  const generateEntityId = (): string => {
    return `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const handleAddEntity = () => {
    const newEntity: ApprovalEntity = {
      ...initialEntityData,
      id: generateEntityId(),
      createdDate: new Date().toISOString(),
      updatedDate: new Date().toISOString()
    };
    setSelectedEntity(newEntity);
    setIsEditing(true);
  };

  const handleEditEntity = (entity: ApprovalEntity) => {
    setSelectedEntity({ ...entity });
    setIsEditing(true);
  };

  const handleSaveEntity = () => {
    if (!selectedEntity) return;

    const updatedEntity = {
      ...selectedEntity,
      updatedDate: new Date().toISOString()
    };

    const existingIndex = entities.findIndex(e => e.id === selectedEntity.id);
    let updatedEntities: ApprovalEntity[];

    if (existingIndex >= 0) {
      // Update existing entity
      updatedEntities = [...entities];
      updatedEntities[existingIndex] = updatedEntity;
    } else {
      // Add new entity
      updatedEntities = [...entities, updatedEntity];
    }

    onChange(updatedEntities);
    setSelectedEntity(null);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setSelectedEntity(null);
    setIsEditing(false);
  };

  const handleDeleteEntity = (entity: ApprovalEntity) => {
    setEntityToDelete(entity);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (entityToDelete) {
      const updatedEntities = entities.filter(e => e.id !== entityToDelete.id);
      onChange(updatedEntities);
      setEntityToDelete(null);
    }
    setDeleteConfirmOpen(false);
  };

  const updateSelectedEntity = (updates: Partial<ApprovalEntity>) => {
    if (selectedEntity) {
      setSelectedEntity({ ...selectedEntity, ...updates });
    }
  };

  // GPS system default fee (in CAD)
  const GPS_SYSTEM_DEFAULT_FEE = '150.00';

  const handleGpsToggle = (enabled: boolean) => {
    const updates: Partial<ApprovalEntity> = {
      gpsEnabled: enabled,
      gpsFee: enabled ? GPS_SYSTEM_DEFAULT_FEE : ''
    };
    updateSelectedEntity(updates);
  };



  const isFormValid = () => {
    if (!selectedEntity) return false;
    
    // Base required fields
    const baseValid = !!(
      selectedEntity.name &&
      selectedEntity.email &&
      selectedEntity.phone &&
      selectedEntity.lenderName &&
      selectedEntity.tier &&
      selectedEntity.interestRate &&
      selectedEntity.monthlyPayment
    );
    
    // GPS validation - if GPS is enabled, fee must be provided
    const gpsValid = !selectedEntity.gpsEnabled || (selectedEntity.gpsEnabled && selectedEntity.gpsFee);
    
    return baseValid && gpsValid;
  };

  if (isEditing && selectedEntity) {
    return (
      <Card className="bg-surface border-outline-variant">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-primary-50 text-white">
                <CreditCard className="h-5 w-5" />
              </div>
              <div>
                <CardTitle className="text-on-surface">
                  {entities.some(e => e.id === selectedEntity.id) ? 'Edit' : 'Add'} Lender Approval Details
                </CardTitle>
                <p className="text-sm text-on-surface-variant">
                  Configure lender approval details and financial terms
                </p>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">

          {/* Lender Approval Details - Streamlined with Required Fields Only */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5 text-primary-50" />
              <h4 className="font-semibold text-on-surface">Lender Approval Details</h4>
            </div>
            
            {/* 2x4 Grid Layout with GPS Toggle */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Row 1 */}
              <SelectField 
                value={selectedEntity.lenderName} 
                onValueChange={(value) => updateSelectedEntity({ lenderName: value })}
              >
                <SelectFieldTrigger label="Lender name">
                  <SelectFieldValue placeholder="Select lender" />
                </SelectFieldTrigger>
                <SelectFieldContent>
                  <SelectFieldItem value="RBC Royal Bank">RBC Royal Bank</SelectFieldItem>
                  <SelectFieldItem value="TD Bank">TD Bank</SelectFieldItem>
                  <SelectFieldItem value="BMO Bank of Montreal">BMO Bank of Montreal</SelectFieldItem>
                  <SelectFieldItem value="Scotiabank">Scotiabank</SelectFieldItem>
                  <SelectFieldItem value="CIBC">CIBC</SelectFieldItem>
                  <SelectFieldItem value="National Bank">National Bank</SelectFieldItem>
                  <SelectFieldItem value="Desjardins">Desjardins</SelectFieldItem>
                  <SelectFieldItem value="Honda Financial">Honda Financial</SelectFieldItem>
                  <SelectFieldItem value="Toyota Credit Canada">Toyota Credit Canada</SelectFieldItem>
                  <SelectFieldItem value="Ally Financial">Ally Financial</SelectFieldItem>
                  <SelectFieldItem value="Ford Credit">Ford Credit</SelectFieldItem>
                  <SelectFieldItem value="GM Financial">GM Financial</SelectFieldItem>
                </SelectFieldContent>
              </SelectField>

              <SelectField 
                value={selectedEntity.tier || ''} 
                onValueChange={(value) => updateSelectedEntity({ tier: value })}
              >
                <SelectFieldTrigger label="Tier">
                  <SelectFieldValue placeholder="Select tier" />
                </SelectFieldTrigger>
                <SelectFieldContent>
                  <SelectFieldItem value="A+">A+ (Excellent Credit)</SelectFieldItem>
                  <SelectFieldItem value="A">A (Good Credit)</SelectFieldItem>
                  <SelectFieldItem value="B+">B+ (Fair Credit)</SelectFieldItem>
                  <SelectFieldItem value="B">B (Sub-Prime)</SelectFieldItem>
                  <SelectFieldItem value="C">C (Deep Sub-Prime)</SelectFieldItem>
                  <SelectFieldItem value="D">D (High Risk)</SelectFieldItem>
                </SelectFieldContent>
              </SelectField>

              <TextField
                label="Rate %"
                value={selectedEntity.interestRate}
                onChange={(e) => updateSelectedEntity({ interestRate: e.target.value })}
                placeholder="4.99"
                type="number"
                step="0.01"
              />

              <TextField
                label="Payment ($)"
                value={selectedEntity.monthlyPayment}
                onChange={(e) => updateSelectedEntity({ monthlyPayment: e.target.value })}
                placeholder="450"
                type="number"
              />

              {/* Row 2 */}
              <TextField
                label="Max finance ($)"
                value={selectedEntity.maxFinance || ''}
                onChange={(e) => updateSelectedEntity({ maxFinance: e.target.value })}
                placeholder="50000"
                type="number"
              />

              <TextField
                label="Reserve ($)"
                value={selectedEntity.reserve || ''}
                onChange={(e) => updateSelectedEntity({ reserve: e.target.value })}
                placeholder="2500"
                type="number"
              />

              <TextField
                label="Holdback ($)"
                value={selectedEntity.holdback || ''}
                onChange={(e) => updateSelectedEntity({ holdback: e.target.value })}
                placeholder="1000"
                type="number"
              />

              {/* GPS Toggle and Conditional Fee Input */}
              <div className="space-y-3">
                {/* GPS Toggle */}
                <div className={`flex items-center space-x-3 p-3 border rounded-md transition-all duration-200 ${
                  selectedEntity.gpsEnabled 
                    ? 'border-primary-50 bg-primary-95' 
                    : 'border-outline-variant bg-surface-container-lowest'
                }`}>
                  <Checkbox
                    id="gps-enabled"
                    checked={selectedEntity.gpsEnabled || false}
                    onCheckedChange={handleGpsToggle}
                    className="w-5 h-5"
                  />
                  <div className="flex-1">
                    <label htmlFor="gps-enabled" className="text-sm font-medium text-on-surface cursor-pointer">
                      Include GPS?
                    </label>
                    <p className={`text-xs transition-colors duration-200 ${
                      selectedEntity.gpsEnabled 
                        ? 'text-primary-50 font-medium' 
                        : 'text-muted-foreground'
                    }`}>
                      {selectedEntity.gpsEnabled ? 'GPS tracking enabled' : 'No GPS tracking'}
                    </p>
                  </div>
                  {selectedEntity.gpsEnabled && (
                    <div className="flex items-center space-x-1 text-xs text-primary-50 font-medium">
                      <span>•</span>
                      <span>Active</span>
                    </div>
                  )}
                </div>

                {/* Conditional GPS Fee Input with Animation */}
                {selectedEntity.gpsEnabled && (
                  <div className="animate-fade-in">
                    <TextField
                      label="GPS fee ($)"
                      value={selectedEntity.gpsFee || ''}
                      onChange={(e) => updateSelectedEntity({ gpsFee: e.target.value })}
                      placeholder="150.00"
                      type="number"
                      step="0.01"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      System default: ${GPS_SYSTEM_DEFAULT_FEE}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={handleCancelEdit}>
              Cancel
            </Button>
            <Button 
              onClick={handleSaveEntity}
              disabled={!isFormValid()}
              className="bg-primary-50 text-white hover:bg-primary-60"
            >
              <CheckCircle2 className="h-4 w-4 mr-2" />
              Save Approval Details
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-full bg-primary-50 text-white">
            <Users className="h-5 w-5" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-on-surface">Lender Approval Details</h3>
            <p className="text-sm text-on-surface-variant">
              Configure approval details and financial terms for deal processing
            </p>
          </div>
        </div>
        
        {!readonly && (
          <Button
            onClick={handleAddEntity}
            className="bg-primary-50 text-white hover:bg-primary-60"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Approval Details
          </Button>
        )}
      </div>

      {/* Entity List */}
      {entities.length === 0 ? (
        <Card className="bg-surface border-outline-variant">
          <CardContent className="p-8 text-center">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h4 className="font-medium mb-2">No lender approval details added</h4>
            <p className="text-sm text-muted-foreground mb-4">
              Add lender approval details with financial terms for this deal.
            </p>
            {!readonly && (
              <Button onClick={handleAddEntity} className="bg-primary-50 text-white hover:bg-primary-60">
                <Plus className="h-4 w-4 mr-2" />
                Add First Approval Details
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {entities.map((entity, index) => (
            <Card key={entity.id} className="bg-surface border-outline-variant">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <Badge variant="outline" className="border-primary-50 text-primary-50 bg-primary-95">
                        <CreditCard className="h-3 w-3" />
                        <span className="ml-1">Approval #{index + 1}</span>
                      </Badge>
                      {index === 0 && (
                        <Badge variant="outline" className="border-green-500 text-green-500 bg-green-50">
                          Primary
                        </Badge>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <h4 className="font-semibold text-on-surface">Configuration #{index + 1}</h4>
                        <div className="space-y-1 text-sm text-on-surface-variant">
                          <div className="flex items-center space-x-1">
                            <Building className="h-3 w-3" />
                            <span>{entity.lenderName || 'Lender TBD'}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <DollarSign className="h-3 w-3" />
                            <span>{entity.tier || 'Tier TBD'}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h5 className="font-medium text-on-surface mb-1">Approval Information</h5>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Lender:</span>
                            <span className="font-medium text-on-surface">{entity.lenderName}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Tier:</span>
                            <span className="font-medium text-on-surface">{entity.tier}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Rate:</span>
                            <span className="text-on-surface">{entity.interestRate}%</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Payment:</span>
                            <span className="text-on-surface">${entity.monthlyPayment}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h5 className="font-medium text-on-surface mb-1">Financial Details</h5>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Max Finance:</span>
                            <span className="text-on-surface">${entity.maxFinance}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Reserve:</span>
                            <span className="text-on-surface">${entity.reserve}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Holdback:</span>
                            <span className="text-on-surface">${entity.holdback}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">GPS:</span>
                            <span className="text-on-surface">
                              {entity.gpsEnabled 
                                ? `Yes - ${entity.gpsFee || '0.00'}` 
                                : 'No'
                              }
                            </span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h5 className="font-medium text-on-surface mb-1">Employment</h5>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Status:</span>
                            <span className="text-on-surface">{entity.employmentStatus}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Income:</span>
                            <span className="text-on-surface">{entity.annualIncome}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-on-surface-variant">Lender:</span>
                            <span className="text-on-surface">{entity.lenderName}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {entity.bankruptcyHistory && (
                      <div className="mt-3 p-2 bg-error-container rounded border-l-4 border-error">
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="h-4 w-4 text-error" />
                          <span className="text-sm font-medium text-error">
                            Bankruptcy History: {entity.bankruptcyDate}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  {!readonly && (
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditEntity(entity)}
                        className="h-8 w-8 p-0"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteEntity(entity)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent className="bg-neutral-10 border-neutral-variant-30">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-neutral-95">Delete Approval Entity</AlertDialogTitle>
            <AlertDialogDescription className="text-neutral-80">
              Are you sure you want to delete this approval entity? This action cannot be undone and will remove all associated credit and personal information.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              onClick={() => setDeleteConfirmOpen(false)}
              className="bg-neutral-20 text-neutral-95 border-neutral-variant-30 hover:bg-neutral-30"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Entity
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}