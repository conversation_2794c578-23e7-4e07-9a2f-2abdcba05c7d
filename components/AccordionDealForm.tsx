/**
 * AccordionDealForm Component - Enhanced 5-Step Deal Builder
 * 
 * Vertical accordion layout for the deal builder with comprehensive
 * transaction calculations and professional UI. Optimized for 1366x768.
 * 
 * STEP ORDER:
 * 1. Customer Information
 * 2. Trade-in Details  
 * 3. Pre Approval Details
 * 4. Vehicle Selection
 * 5. Build Deal
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude <PERSON>
 */

import { useState } from 'react';
import { Card, CardContent } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion';

// Material Design 3 Components
import { TextField } from './ui/material/text-field';
import { 
  SelectField, 
  SelectFieldTrigger, 
  SelectFieldContent, 
  SelectFieldItem, 
  SelectFieldValue 
} from './ui/material/select-field';
import { Checkbox } from './ui/material/checkbox';
import { Label } from './ui/material/label';
import { Textarea } from './ui/material/textarea';
import { MaterialTable, MaterialTableBody, MaterialTableCell, MaterialTableHead, MaterialTableHeader, MaterialTableRow, MaterialTableContainer } from './ui/material/table';

// Enhanced Components
import { VehicleFilters, VehicleFilterState, initialFilters } from './VehicleFilters';
import { ApprovalEntityManager } from './ApprovalEntityManager';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { StepNotesButton } from './StepNotesButton';
import { PhotoUpload } from './PhotoUpload';

// Types and Utils
import { ApprovalEntity, DealNote } from '../types';
import { 
  capitalize, 
  capitalizeWords, 
  safeString, 
  formatName, 
  getInitials,
  formatPhone,
  formatEmail,
  formatCurrency
} from '../utils/stringUtils';

// Icons
import { 
  ArrowLeft, UserCheck, Building, CreditCard, ArrowLeftRight, 
  FileText, Edit, Phone, Mail, MapPin, Calendar, DollarSign,
  CheckCircle, Clock, Image as ImageIcon, Users, Search, 
  AlertTriangle, Briefcase, Home, IdCard, Loader2, Check,
  User, Car, ArrowRight, Shield
} from 'lucide-react';

import { toast } from 'sonner@2.0.3';

// ID options fallback
const ID_OPTIONS = [
  'Driver\'s License',
  'Passport', 
  'Health Card',
  'Social Insurance Number Card',
  'Birth Certificate',
  'Permanent Resident Card',
  'Citizenship Certificate',
  'Other Government ID'
];

// Form Data Interface
interface DealFormData {
  // Customer Information
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  phoneNumber: string;
  emailAddress: string;
  customerPhoto?: string;
  idType: string;
  idImageUpload?: string;
  suiteNumber?: string;
  address: string;
  streetName: string;
  city: string;
  province: string;
  status: 'Yes' | 'No';
  statusCard?: File | null;
  
  // Trade-in Details
  hasTradeIn: boolean;
  tradeInMake?: string;
  tradeInModel?: string;
  tradeInYear?: string;
  tradeInTrim?: string;
  tradeInVin?: string;
  tradeInMileage?: string;
  tradeInColor?: string;
  tradeInAllowance?: string;
  tradeInFcv?: string;
  tradeInLoan?: string;
  
  // Approval Entities
  approvalEntities: ApprovalEntity[];
  
  // Vehicle Selection
  selectedVehicles: number[];
  
  // Deal Notes
  dealNotes: string;
}

// Vehicle Interface
interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  price: string;
  location: string;
  vin: string;
  mileage: string;
  exterior: string;
  interior: string;
  transmission: string;
  fuelType: string;
  daysOnLot: number;
  cost: string;
  photos: string[];
  features: string[];
}

interface AccordionDealFormProps {
  onBack: () => void;
  onSubmit: (data: DealFormData) => void;
  locations: Array<{ id: string; name: string; count: number }>;
  isSubmitting?: boolean;
  notes: DealNote[];
  onNotesChange: (notes: DealNote[]) => void;
  currentStep: number;
  onCurrentStepChange: (step: number) => void;
  onNotesToggle: () => void;
}

export function AccordionDealForm({ 
  onBack, 
  onSubmit, 
  locations = [], 
  isSubmitting = false,
  notes,
  onNotesChange,
  currentStep,
  onCurrentStepChange,
  onNotesToggle
}: AccordionDealFormProps) {
  
  // State Management
  const [vehicleFilters, setVehicleFilters] = useState<VehicleFilterState>(initialFilters);
  const [formData, setFormData] = useState<DealFormData>({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    phoneNumber: '',
    emailAddress: '',
    customerPhoto: undefined,
    idType: '',
    idImageUpload: undefined,
    suiteNumber: '',
    address: '',
    streetName: '',
    city: '',
    province: '',
    status: 'Yes',
    statusCard: null,
    hasTradeIn: false,
    approvalEntities: [],
    selectedVehicles: [],
    dealNotes: ''
  });

  const [openAccordionItems, setOpenAccordionItems] = useState<string[]>(['step-1']);

  // Mock vehicle data
  const availableVehicles: Vehicle[] = [
    { 
      id: 1, make: 'Toyota', model: 'Camry', year: 2024, price: '$32,999',
      location: 'Toronto Downtown', vin: '1HGCM82633A123456', mileage: '12,450 km',
      exterior: 'Midnight Black', interior: 'Black Leather', transmission: 'CVT', fuelType: 'Hybrid',
      daysOnLot: 23, cost: '$28,500', 
      photos: ['https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=400&h=300&fit=crop'],
      features: ['Navigation', 'Backup Camera', 'Heated Seats']
    },
    { 
      id: 2, make: 'Honda', model: 'Civic', year: 2024, price: '$28,499',
      location: 'Mississauga', vin: '2HGCM82633A123457', mileage: '8,750 km',
      exterior: 'Pearl White', interior: 'Black Cloth', transmission: 'CVT', fuelType: 'Gasoline',
      daysOnLot: 15, cost: '$24,800', 
      photos: ['https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop'],
      features: ['Honda Sensing', 'Sunroof', 'Wireless Charging']
    },
    { 
      id: 3, make: 'Ford', model: 'F-150', year: 2023, price: '$45,999',
      location: 'Vancouver', vin: '3HGCM82633A123458', mileage: '25,100 km',
      exterior: 'Agate Black', interior: 'Medium Earth Gray', transmission: '10-Speed Automatic', fuelType: 'Gasoline',
      daysOnLot: 41, cost: '$41,200', 
      photos: ['https://images.unsplash.com/photo-1563720223185-11003d516935?w=400&h=300&fit=crop'],
      features: ['4WD', 'Tow Package', 'B&O Sound']
    },
    { 
      id: 4, make: 'BMW', model: 'X3', year: 2024, price: '$52,999',
      location: 'Toronto Downtown', vin: '6HGCM82633A123461', mileage: '15,200 km',
      exterior: 'Alpine White', interior: 'Black SensaTec', transmission: '8-Speed Automatic', fuelType: 'Gasoline',
      daysOnLot: 28, cost: '$47,500', 
      photos: ['https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=300&fit=crop'],
      features: ['xDrive AWD', 'Panoramic Roof', 'Harman Kardon Audio']
    },
    { 
      id: 5, make: 'Audi', model: 'Q5', year: 2024, price: '$48,999',
      location: 'Vancouver', vin: '7HGCM82633A123462', mileage: '9,800 km',
      exterior: 'Quantum Gray', interior: 'Black Leather', transmission: '7-Speed S tronic', fuelType: 'Gasoline',
      daysOnLot: 19, cost: '$43,800', 
      photos: ['https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop'],
      features: ['quattro AWD', 'Virtual Cockpit', 'Bang & Olufsen Audio']
    }
  ];

  // Helper Functions
  const updateFormData = (updates: Partial<DealFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const handleStatusCardUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    updateFormData({ statusCard: file });
  };

  const isStepComplete = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        return !!(formData.firstName && formData.lastName && formData.phoneNumber && formData.emailAddress);
      case 2:
        return !formData.hasTradeIn || !!(formData.tradeInMake && formData.tradeInModel && formData.tradeInYear);
      case 3:
        return formData.approvalEntities.length > 0;
      case 4:
        return formData.selectedVehicles.length > 0;
      case 5:
        return true;
      default:
        return false;
    }
  };

  const getSelectedVehicleDetails = () => {
    return availableVehicles.filter(v => formData.selectedVehicles.includes(v.id));
  };

  // Calculate transaction values
  const calculateTransactionValues = () => {
    const selectedVehicle = getSelectedVehicleDetails()[0];
    if (!selectedVehicle) return {
      cashPrice: 0,
      tradeInAllowance: 0,
      pstAmount: 0,
      gstAmount: 0,
      totalTaxes: 0,
      fees: 0,
      netSellingPrice: 0,
      amountToFinance: 0,
      totalMonthlyPayment: 0,
      advanceToDealer: 0
    };

    const cashPrice = parseFloat(selectedVehicle.price.replace(/[$,]/g, '')) || 0;
    const tradeInValue = parseFloat((formData.tradeInAllowance || '0').replace(/[$,]/g, '')) || 0;
    
    // Tax calculations
    const pstRate = formData.province === 'BC' ? 0.07 : formData.province === 'ON' ? 0.08 : 0.05;
    const gstRate = 0.05;
    
    const taxableAmount = cashPrice - tradeInValue;
    const pstAmount = taxableAmount * pstRate;
    const gstAmount = taxableAmount * gstRate;
    const totalTaxes = pstAmount + gstAmount;
    
    // Fees
    const ppsa = 100.51;
    const registrationFee = 38.50;
    const adminFee = 598.43;
    const totalFees = ppsa + registrationFee + adminFee;
    
    const netSellingPrice = cashPrice + totalTaxes + totalFees - tradeInValue;
    const amountToFinance = netSellingPrice;
    
    // Financing calculations (84 months at 16.49%)
    const term = 84;
    const interestRate = 0.1649 / 12;
    const monthlyPayment = amountToFinance * (interestRate * Math.pow(1 + interestRate, term)) / 
                          (Math.pow(1 + interestRate, term) - 1);
    
    const advanceToDealer = amountToFinance - totalFees;

    return {
      cashPrice,
      tradeInAllowance: tradeInValue,
      pstAmount,
      gstAmount,
      totalTaxes,
      fees: totalFees,
      netSellingPrice,
      amountToFinance,
      totalMonthlyPayment: monthlyPayment,
      advanceToDealer
    };
  };

  const transactionValues = calculateTransactionValues();

  const handleSubmit = () => {
    if (!isSubmitting) {
      const dataWithNotes = {
        ...formData,
        notes: notes
      };
      onSubmit(dataWithNotes);
    }
  };

  const handleVehicleSelection = (vehicleId: number) => {
    if (!isSubmitting) {
      setFormData(prev => ({
        ...prev,
        selectedVehicles: prev.selectedVehicles.includes(vehicleId)
          ? prev.selectedVehicles.filter(id => id !== vehicleId)
          : [...prev.selectedVehicles, vehicleId]
      }));
    }
  };

  // Accordion change handler
  const handleAccordionChange = (openItems: string[]) => {
    setOpenAccordionItems(openItems);
    
    if (openItems.includes('step-5')) {
      onCurrentStepChange(5);
    } else if (openItems.includes('step-4')) {
      onCurrentStepChange(4);
    } else if (openItems.includes('step-3')) {
      onCurrentStepChange(3);
    } else if (openItems.includes('step-2')) {
      onCurrentStepChange(2);
    } else {
      onCurrentStepChange(1);
    }
  };

  // Next step handler
  const handleNextStep = (currentStepNumber: number) => {
    if (isSubmitting) return;
    
    const nextStepNumber = currentStepNumber + 1;
    const nextStepKey = `step-${nextStepNumber}`;
    const currentStepKey = `step-${currentStepNumber}`;
    
    if (!isStepComplete(currentStepNumber)) {
      toast.error('Please complete the current step', {
        description: 'Fill in all required fields before proceeding.',
        duration: 2500,
      });
      return;
    }
    
    const newOpenItems = openAccordionItems.filter(item => item !== currentStepKey);
    if (!newOpenItems.includes(nextStepKey)) {
      newOpenItems.push(nextStepKey);
    }
    
    setOpenAccordionItems(newOpenItems);
    onCurrentStepChange(nextStepNumber);
    
    const stepNames = {
      2: 'Trade-in Details', 
      3: 'Pre Approval Details',
      4: 'Vehicle Selection',
      5: 'Build Deal'
    };
    
    toast.success('Step Completed', {
      description: `Moving to ${stepNames[nextStepNumber as keyof typeof stepNames]}...`,
      duration: 1500,
    });
  };

  // Accordion Step Trigger Component
  const AccordionStepTrigger = ({ 
    stepNumber, 
    title, 
    icon: Icon, 
    description 
  }: { 
    stepNumber: number; 
    title: string; 
    icon: any; 
    description: string; 
  }) => {
    const isComplete = isStepComplete(stepNumber);
    
    return (
      <AccordionTrigger className="hover:no-underline py-5 px-6">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-4">
            <div className={`
              flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 flex-shrink-0
              ${isComplete 
                ? 'bg-green-50 border-green-500 text-green-500' 
                : 'bg-primary-95 border-primary-50 text-primary-50'
              }
            `}>
              {isComplete ? (
                <Check className="h-6 w-6" />
              ) : (
                <Icon className="h-6 w-6" />
              )}
            </div>
            
            <div className="flex-1 text-left min-w-0">
              <div className="flex items-center space-x-3 mb-1">
                <h3 className="text-lg font-bold text-foreground">{title}</h3>
                <Badge variant="outline" className="text-xs px-2 py-1 font-medium">
                  Step {stepNumber}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">{description}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 flex-shrink-0 ml-4">
            {isComplete && (
              <Badge variant="outline" className="bg-green-50 border-green-500 text-green-700 text-sm px-3 py-1 font-medium">
                Complete
              </Badge>
            )}
          </div>
        </div>
      </AccordionTrigger>
    );
  };

  // Step Action Buttons Component
  const StepActionButtons = ({ stepNumber }: { stepNumber: number }) => {
    const isComplete = isStepComplete(stepNumber);
    const isLastStep = stepNumber === 5;
    
    return (
      <div className="flex justify-between items-center pt-6 border-t border-outline-variant mt-8">
        <StepNotesButton
          notes={notes}
          onNotesToggle={onNotesToggle}
          disabled={isSubmitting}
          currentStep={stepNumber}
        />
        
        {!isLastStep && (
          <Button
            onClick={() => handleNextStep(stepNumber)}
            disabled={!isComplete || isSubmitting}
            className="bg-brand-orange text-white hover:bg-brand-orange-dark flex items-center space-x-2 px-6 py-3 text-base font-semibold shadow-orange-md hover:shadow-orange-lg"
          >
            <span>Next Step</span>
            <ArrowRight className="h-5 w-5" />
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6 relative">
      {/* Progress Indicator */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <p className="text-sm text-muted-foreground">Deal Creation Progress</p>
          <div className="flex items-center space-x-2 mt-1">
            <Badge variant="outline" className="px-3 py-1 text-sm font-medium">
              {[1,2,3,4,5].filter(step => isStepComplete(step)).length} of 5 Complete
            </Badge>
            {isSubmitting && (
              <div className="flex items-center space-x-2 text-primary-50">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Creating deal...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Accordion Steps */}
      <Accordion 
        type="multiple" 
        value={openAccordionItems}
        onValueChange={handleAccordionChange}
        className="space-y-6"
      >
        {/* Step 1: Customer Information */}
        <AccordionItem value="step-1" className="border-2 border-outline-variant rounded-xl bg-card shadow-sm">
          <AccordionStepTrigger
            stepNumber={1}
            title="Customer Information"
            icon={UserCheck}
            description="Enter the customer's basic details and contact information"
          />
          <AccordionContent className="px-8 pb-8 pt-4">
            <div className="space-y-8">
              {/* Personal Information */}
              <div className="bg-gradient-to-br from-primary-99 to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-primary-90 mr-3">
                    <User className="h-5 w-5 text-primary-50" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">Personal Information</h4>
                    <p className="text-sm text-muted-foreground">Enter customer's basic details</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                  <TextField
                    label="First name *"
                    value={formData.firstName}
                    onChange={(e) => updateFormData({ firstName: e.target.value })}
                    placeholder="Enter first name"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />

                  <TextField
                    label="Last name *"
                    value={formData.lastName}
                    onChange={(e) => updateFormData({ lastName: e.target.value })}
                    placeholder="Enter last name"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />

                  <TextField
                    label="Date of birth *"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => updateFormData({ dateOfBirth: e.target.value })}
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <TextField
                    label="Phone number *"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => updateFormData({ phoneNumber: e.target.value })}
                    placeholder="(*************"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />

                  <TextField
                    label="Email address *"
                    type="email"
                    value={formData.emailAddress}
                    onChange={(e) => updateFormData({ emailAddress: e.target.value })}
                    placeholder="<EMAIL>"
                    required
                    disabled={isSubmitting}
                    className="form-input"
                  />
                </div>

                {/* Photo Upload */}
                <div className="mt-6 pt-6 border-t border-outline-variant">
                  <div className="flex items-start space-x-6">
                    <div className="flex-shrink-0">
                      <div className="w-20 h-20 rounded-lg bg-surface-container border-2 border-dashed border-outline-variant flex items-center justify-center">
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <PhotoUpload
                        label="Customer photo"
                        value={formData.customerPhoto}
                        onChange={(file) => updateFormData({ customerPhoto: file })}
                        disabled={isSubmitting}
                        type="photo"
                        placeholder="Upload customer photo (optional)"
                        className="w-full"
                      />
                      <p className="text-xs text-muted-foreground mt-2">
                        Upload a clear photo of the customer for identification purposes
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* ID Documentation */}
              <div className="bg-gradient-to-br from-surface-bright to-surface-container-low rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-secondary-90 mr-3">
                    <IdCard className="h-5 w-5 text-secondary-40" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">ID Documentation</h4>
                    <p className="text-sm text-muted-foreground">Required identification documents</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <SelectField 
                      value={formData.idType} 
                      onValueChange={(value) => updateFormData({ idType: value })}
                      disabled={isSubmitting}
                    >
                      <SelectFieldTrigger label="ID type *">
                        <SelectFieldValue placeholder="Select ID type" />
                      </SelectFieldTrigger>
                      <SelectFieldContent>
                        {ID_OPTIONS.map(idType => (
                          <SelectFieldItem key={idType} value={idType}>
                            {idType}
                          </SelectFieldItem>
                        ))}
                      </SelectFieldContent>
                    </SelectField>
                    <p className="text-xs text-muted-foreground">
                      Select the type of government-issued ID
                    </p>
                  </div>

                  <div className="space-y-2">
                    <PhotoUpload
                      label="Upload ID document *"
                      value={formData.idImageUpload}
                      onChange={(file) => updateFormData({ idImageUpload: file })}
                      disabled={isSubmitting}
                      type="document"
                      placeholder="Upload clear photo of ID"
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Upload both sides of the ID document
                    </p>
                  </div>
                </div>
              </div>

              {/* Residential Information */}
              <div className="bg-gradient-to-br from-surface-container-lowest to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-tertiary-90 mr-3">
                    <Home className="h-5 w-5 text-tertiary-40" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">Residential Information</h4>
                    <p className="text-sm text-muted-foreground">Customer's current address details</p>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <div className="lg:col-span-1">
                      <TextField
                        label="Suite/Unit #"
                        value={formData.suiteNumber || ''}
                        onChange={(e) => updateFormData({ suiteNumber: e.target.value })}
                        placeholder="e.g. 205"
                        disabled={isSubmitting}
                        className="form-input"
                      />
                    </div>

                    <div className="lg:col-span-1">
                      <TextField
                        label="Address #"
                        value={formData.address}
                        onChange={(e) => updateFormData({ address: e.target.value })}
                        placeholder="e.g. 123"
                        disabled={isSubmitting}
                        className="form-input"
                      />
                    </div>

                    <div className="lg:col-span-2">
                      <TextField
                        label="Street name *"
                        value={formData.streetName}
                        onChange={(e) => updateFormData({ streetName: e.target.value })}
                        placeholder="e.g. Main Street"
                        disabled={isSubmitting}
                        className="form-input"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <TextField
                      label="City *"
                      value={formData.city}
                      onChange={(e) => updateFormData({ city: e.target.value })}
                      placeholder="e.g. Toronto"
                      disabled={isSubmitting}
                      className="form-input"
                    />

                    <SelectField 
                      value={formData.province} 
                      onValueChange={(value) => updateFormData({ province: value })}
                      disabled={isSubmitting}
                    >
                      <SelectFieldTrigger label="Province *">
                        <SelectFieldValue placeholder="Select province" />
                      </SelectFieldTrigger>
                      <SelectFieldContent>
                        <SelectFieldItem value="ON">Ontario</SelectFieldItem>
                        <SelectFieldItem value="BC">British Columbia</SelectFieldItem>
                        <SelectFieldItem value="AB">Alberta</SelectFieldItem>
                        <SelectFieldItem value="QC">Quebec</SelectFieldItem>
                        <SelectFieldItem value="MB">Manitoba</SelectFieldItem>
                        <SelectFieldItem value="SK">Saskatchewan</SelectFieldItem>
                        <SelectFieldItem value="NS">Nova Scotia</SelectFieldItem>
                        <SelectFieldItem value="NB">New Brunswick</SelectFieldItem>
                        <SelectFieldItem value="PE">Prince Edward Island</SelectFieldItem>
                        <SelectFieldItem value="NL">Newfoundland and Labrador</SelectFieldItem>
                        <SelectFieldItem value="YT">Yukon</SelectFieldItem>
                        <SelectFieldItem value="NT">Northwest Territories</SelectFieldItem>
                        <SelectFieldItem value="NU">Nunavut</SelectFieldItem>
                      </SelectFieldContent>
                    </SelectField>
                  </div>
                </div>
              </div>

              {/* Customer Status */}
              <div className="bg-gradient-to-br from-primary-95 to-surface-bright rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="p-2.5 rounded-lg bg-primary-90 mr-3">
                    <CheckCircle className="h-5 w-5 text-primary-50" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-on-surface">Customer Status & Tax Exemption</h4>
                    <p className="text-sm text-muted-foreground">Customer status verification and tax exemption documentation</p>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                    <Label className="text-sm font-semibold text-on-surface mb-3 block">
                      Is this customer active? *
                    </Label>
                    
                    <div className="flex items-center space-x-8">
                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          id="status-yes"
                          name="status"
                          value="Yes"
                          checked={formData.status === 'Yes'}
                          onChange={(e) => updateFormData({ status: e.target.value as 'Yes' | 'No' })}
                          disabled={isSubmitting}
                          className="w-4 h-4 text-primary-50 bg-white border-2 border-outline focus:ring-2 focus:ring-primary-50 focus:ring-offset-2"
                        />
                        <Label htmlFor="status-yes" className="text-sm font-medium text-on-surface cursor-pointer">
                          Yes - Active Customer
                        </Label>
                        {formData.status === 'Yes' && (
                          <div className="flex items-center justify-center w-5 h-5 rounded-full bg-green-100">
                            <Check className="h-3 w-3 text-green-600" />
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          id="status-no"
                          name="status"
                          value="No"
                          checked={formData.status === 'No'}
                          onChange={(e) => updateFormData({ status: e.target.value as 'Yes' | 'No' })}
                          disabled={isSubmitting}
                          className="w-4 h-4 text-primary-50 bg-white border-2 border-outline focus:ring-2 focus:ring-primary-50 focus:ring-offset-2"
                        />
                        <Label htmlFor="status-no" className="text-sm font-medium text-on-surface cursor-pointer">
                          No - Inactive
                        </Label>
                        {formData.status === 'No' && (
                          <div className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-100">
                            <Check className="h-3 w-3 text-gray-600" />
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className={`rounded-lg px-3 py-2 border mt-4 ${
                      formData.status === 'Yes' 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-yellow-50 border-yellow-200'
                    }`}>
                      <p className={`text-xs ${
                        formData.status === 'Yes' 
                          ? 'text-green-700' 
                          : 'text-yellow-700'
                      }`}>
                        {formData.status === 'Yes' 
                          ? '✓ Active customers receive priority service and full access to deals'
                          : '⚠ Inactive customers may have limited access to certain services'
                        }
                      </p>
                    </div>
                  </div>

                  {/* Tax Exemption */}
                  <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                    <div className="flex items-center space-x-2 mb-4">
                      <Shield className="h-4 w-4 text-green-600" />
                      <Label className="text-sm font-semibold text-on-surface">Tax Exemption Status</Label>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="relative">
                        <input
                          type="file"
                          id="status-card"
                          onChange={handleStatusCardUpload}
                          accept=".pdf,.jpg,.jpeg,.png"
                          disabled={isSubmitting}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        <div className="bg-input-background border border-input-idle-border rounded-lg px-4 py-3 flex items-center justify-between hover:border-input-hover-border transition-colors cursor-pointer">
                          <span className="text-sm text-muted-foreground">
                            {formData.statusCard ? formData.statusCard.name : 'Upload status card (optional)'}
                          </span>
                          <FileText className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                      
                      {formData.statusCard ? (
                        <div className="bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                          <div className="flex items-start space-x-2">
                            <div className="flex items-center justify-center w-4 h-4 rounded-full bg-green-100 mt-0.5">
                              <Check className="h-3 w-3 text-green-600" />
                            </div>
                            <div className="flex-1">
                              <p className="text-xs font-medium text-green-800">
                                Status Card Uploaded
                              </p>
                              <p className="text-xs text-green-600 mt-0.5">
                                Customer eligible for tax exemption verification
                              </p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-neutral-50 border border-neutral-200 rounded-lg px-3 py-2">
                          <p className="text-xs text-neutral-600">
                            <strong>Optional:</strong> Upload First Nations Status Card, Métis Nation Card, 
                            or other tax exemption documents if applicable
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <StepActionButtons stepNumber={1} />
          </AccordionContent>
        </AccordionItem>

        {/* Step 2: Trade-in Details */}
        <AccordionItem value="step-2" className="border-2 border-outline-variant rounded-xl bg-card shadow-sm">
          <AccordionStepTrigger
            stepNumber={2}
            title="Trade-in Details"
            icon={ArrowLeftRight}
            description="Optional trade-in vehicle information and valuation"
          />
          <AccordionContent className="px-8 pb-8 pt-4">
            <div className="space-y-8">
              <div className="bg-gradient-to-br from-surface-bright to-surface-container-low rounded-xl p-6 border border-outline-variant shadow-sm">
                <div className="flex items-center space-x-4 mb-6">
                  <Checkbox
                    id="hasTradeIn"
                    checked={formData.hasTradeIn}
                    onCheckedChange={(checked) => updateFormData({ hasTradeIn: checked as boolean })}
                    disabled={isSubmitting}
                    className="w-6 h-6"
                  />
                  <Label htmlFor="hasTradeIn" className="text-lg font-semibold text-on-surface cursor-pointer">
                    Customer has a vehicle to trade in
                  </Label>
                </div>

                {formData.hasTradeIn && (
                  <div className="bg-surface-container-lowest rounded-xl p-6 border border-outline-variant">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      {/* Vehicle Details */}
                      <div className="space-y-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 rounded-lg bg-primary-90 mr-3">
                            <Car className="h-5 w-5 text-primary-50" />
                          </div>
                          <h4 className="font-bold text-lg text-on-surface">Vehicle Details & Specs</h4>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <TextField
                            label="Make *"
                            value={formData.tradeInMake || ''}
                            onChange={(e) => updateFormData({ tradeInMake: e.target.value })}
                            placeholder="Toyota"
                            disabled={isSubmitting}
                          />

                          <TextField
                            label="Model *"
                            value={formData.tradeInModel || ''}
                            onChange={(e) => updateFormData({ tradeInModel: e.target.value })}
                            placeholder="Camry"
                            disabled={isSubmitting}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <TextField
                            label="Year *"
                            value={formData.tradeInYear || ''}
                            onChange={(e) => updateFormData({ tradeInYear: e.target.value })}
                            placeholder="2020"
                            disabled={isSubmitting}
                          />

                          <TextField
                            label="Trim"
                            value={formData.tradeInTrim || ''}
                            onChange={(e) => updateFormData({ tradeInTrim: e.target.value })}
                            placeholder="LE"
                            disabled={isSubmitting}
                          />
                        </div>

                        <TextField
                          label="VIN"
                          value={formData.tradeInVin || ''}
                          onChange={(e) => updateFormData({ tradeInVin: e.target.value })}
                          placeholder="1HGCM82633A123456"
                          disabled={isSubmitting}
                        />

                        <div className="grid grid-cols-2 gap-4">
                          <TextField
                            label="Mileage"
                            value={formData.tradeInMileage || ''}
                            onChange={(e) => updateFormData({ tradeInMileage: e.target.value })}
                            placeholder="85,000 km"
                            disabled={isSubmitting}
                          />

                          <TextField
                            label="Exterior Color"
                            value={formData.tradeInColor || ''}
                            onChange={(e) => updateFormData({ tradeInColor: e.target.value })}
                            placeholder="Silver Metallic"
                            disabled={isSubmitting}
                          />
                        </div>
                      </div>

                      {/* Financial Valuation */}
                      <div className="space-y-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 rounded-lg bg-secondary-90 mr-3">
                            <DollarSign className="h-5 w-5 text-secondary-40" />
                          </div>
                          <h4 className="font-bold text-lg text-on-surface">Financial Valuation</h4>
                        </div>
                        
                        <TextField
                          label="Trade Allowance"
                          value={formData.tradeInAllowance || ''}
                          onChange={(e) => updateFormData({ tradeInAllowance: e.target.value })}
                          placeholder="$15,000"
                          disabled={isSubmitting}
                        />

                        <TextField
                          label="Fair Cash Value (FCV)"
                          value={formData.tradeInFcv || ''}
                          onChange={(e) => updateFormData({ tradeInFcv: e.target.value })}
                          placeholder="$13,500"
                          disabled={isSubmitting}
                        />

                        <TextField
                          label="Outstanding Loan"
                          value={formData.tradeInLoan || ''}
                          onChange={(e) => updateFormData({ tradeInLoan: e.target.value })}
                          placeholder="$8,000"
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <StepActionButtons stepNumber={2} />
          </AccordionContent>
        </AccordionItem>

        {/* Step 3: Pre Approval Details */}
        <AccordionItem value="step-3" className="border-2 border-outline-variant rounded-xl bg-card shadow-sm">
          <AccordionStepTrigger
            stepNumber={3}
            title="Pre Approval Details"
            icon={Users}
            description="Add multiple lender approvals"
          />
          <AccordionContent className="px-8 pb-8 pt-4">
            <div className="space-y-6">
              <ApprovalEntityManager
                entities={formData.approvalEntities}
                onChange={(entities) => updateFormData({ approvalEntities: entities })}
                disabled={isSubmitting}
              />
            </div>
            
            <StepActionButtons stepNumber={3} />
          </AccordionContent>
        </AccordionItem>

        {/* Step 4: Vehicle Selection */}
        <AccordionItem value="step-4" className="border-2 border-outline-variant rounded-xl bg-card shadow-sm">
          <AccordionStepTrigger
            stepNumber={4}
            title="Vehicle Selection"
            icon={Search}
            description="Choose vehicles from available inventory"
          />
          <AccordionContent className="px-8 pb-8 pt-4">
            <div className="space-y-6">
              <div className="flex items-center justify-between bg-gradient-to-r from-primary-99 to-surface-bright rounded-lg p-4 border border-outline-variant">
                <div className="flex items-center space-x-3">
                  <Car className="h-5 w-5 text-primary-50" />
                  <span className="text-base font-semibold text-on-surface">
                    Selected vehicles: {formData.selectedVehicles.length}
                  </span>
                </div>
                <Badge variant="outline" className="px-3 py-1">
                  {availableVehicles.length} Vehicles
                </Badge>
              </div>

              <VehicleFilters
                filters={vehicleFilters}
                onFiltersChange={setVehicleFilters}
                disabled={isSubmitting}
              />
              
              <div className="border-2 border-outline-variant rounded-xl overflow-hidden bg-surface-bright">
                <MaterialTableContainer>
                  <MaterialTable>
                    <MaterialTableHeader>
                      <MaterialTableRow>
                        <MaterialTableHead className="w-12"></MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Vehicle</MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Price</MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Mileage</MaterialTableHead>
                        <MaterialTableHead className="text-base font-semibold">Location</MaterialTableHead>
                      </MaterialTableRow>
                    </MaterialTableHeader>
                    <MaterialTableBody>
                      {availableVehicles.map((vehicle) => (
                        <MaterialTableRow 
                          key={vehicle.id}
                          className={`cursor-pointer hover:bg-surface-container-low transition-all duration-200 ${
                            formData.selectedVehicles.includes(vehicle.id) ? 'bg-primary-95 border-l-4 border-primary-50' : ''
                          } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={() => !isSubmitting && handleVehicleSelection(vehicle.id)}
                        >
                          <MaterialTableCell>
                            <Checkbox
                              checked={formData.selectedVehicles.includes(vehicle.id)}
                              onChange={() => handleVehicleSelection(vehicle.id)}
                              disabled={isSubmitting}
                              className="w-5 h-5"
                            />
                          </MaterialTableCell>
                          <MaterialTableCell>
                            <div className="flex items-center space-x-4">
                              <ImageWithFallback
                                src={vehicle.photos[0]}
                                alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                                className="w-16 h-16 rounded-lg object-cover border border-outline-variant"
                              />
                              <div>
                                <div className="font-semibold text-base text-on-surface">
                                  {vehicle.year} {vehicle.make} {vehicle.model}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  VIN: {vehicle.vin}
                                </div>
                              </div>
                            </div>
                          </MaterialTableCell>
                          <MaterialTableCell className="font-bold text-lg text-primary-50">
                            {vehicle.price}
                          </MaterialTableCell>
                          <MaterialTableCell className="text-base">{vehicle.mileage}</MaterialTableCell>
                          <MaterialTableCell className="text-base">{vehicle.location}</MaterialTableCell>
                        </MaterialTableRow>
                      ))}
                    </MaterialTableBody>
                  </MaterialTable>
                </MaterialTableContainer>
              </div>
            </div>
            
            <StepActionButtons stepNumber={4} />
          </AccordionContent>
        </AccordionItem>

        {/* Step 5: Build Deal */}
        <AccordionItem value="step-5" className="border-2 border-outline-variant rounded-xl bg-card shadow-sm">
          <AccordionStepTrigger
            stepNumber={5}
            title="Build Deal"
            icon={CheckCircle}
            description="Review transaction details and finalize your deal"
          />
          <AccordionContent className="px-8 pb-8 pt-4">
            <div className="space-y-6">
              {/* Deal Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="border-2 border-outline-variant shadow-sm">
                  <CardContent className="p-6">
                    <h4 className="font-bold text-lg mb-4 flex items-center space-x-2">
                      <UserCheck className="h-6 w-6 text-primary-50" />
                      <span>Customer Information</span>
                    </h4>
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16 border-2 border-outline-variant">
                        <AvatarImage src={formData.customerPhoto} alt={formatName(formData.firstName, formData.lastName)} />
                        <AvatarFallback className="text-lg font-semibold">{getInitials(formData.firstName, formData.lastName)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <h5 className="font-bold text-lg text-on-surface">{formatName(formData.firstName, formData.lastName)}</h5>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div>{formatEmail(formData.emailAddress)}</div>
                          <div>{formatPhone(formData.phoneNumber)}</div>
                          <div>{formData.city}, {formData.province}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-2 border-outline-variant shadow-sm">
                  <CardContent className="p-6">
                    <h4 className="font-bold text-lg mb-4 flex items-center space-x-2">
                      <Car className="h-6 w-6 text-primary-50" />
                      <span>Selected Vehicle</span>
                    </h4>
                    {getSelectedVehicleDetails().length > 0 ? (
                      <div className="space-y-3">
                        {getSelectedVehicleDetails().map((vehicle) => (
                          <div key={vehicle.id} className="flex items-center space-x-4">
                            <ImageWithFallback
                              src={vehicle.photos[0]}
                              alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                              className="w-12 h-12 rounded-lg object-cover border border-outline-variant"
                            />
                            <div className="flex-1">
                              <div className="font-semibold text-base">{vehicle.year} {vehicle.make} {vehicle.model}</div>
                              <div className="text-sm text-muted-foreground">VIN: {vehicle.vin}</div>
                              <div className="text-lg font-bold text-primary-50">{vehicle.price}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-sm text-muted-foreground">No vehicle selected</div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Transaction Details */}
              <div className="bg-gradient-to-br from-surface-bright to-surface-container-low rounded-xl p-6 border border-outline-variant shadow-sm">
                <h3 className="text-xl font-bold text-on-surface mb-6 flex items-center space-x-2">
                  <FileText className="h-6 w-6 text-primary-50" />
                  <span>Transaction Details</span>
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column */}
                  <div className="space-y-6">
                    {/* Purchase Details */}
                    <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                      <h4 className="font-semibold text-base mb-4 text-on-surface">Purchase Details</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Cash Price:</span>
                          <span className="font-semibold text-yellow-700 bg-yellow-100 px-2 py-1 rounded text-sm">
                            {formatCurrency(transactionValues.cashPrice)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Delivery Date:</span>
                          <div className="flex space-x-1 text-sm">
                            <span className="border px-2 py-1 rounded bg-white">28</span>
                            <span className="border px-2 py-1 rounded bg-white">12</span>
                            <span className="border px-2 py-1 rounded bg-white">2024</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Trade In Details */}
                    {formData.hasTradeIn && (
                      <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                        <h4 className="font-semibold text-base mb-4 text-on-surface">Trade In</h4>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-muted-foreground">Year:</span>
                            <input type="text" value={formData.tradeInYear || ''} className="w-full border px-2 py-1 rounded bg-white mt-1" readOnly />
                          </div>
                          <div>
                            <span className="text-muted-foreground">VIN:</span>
                            <input type="text" value={formData.tradeInVin || ''} className="w-full border px-2 py-1 rounded bg-white mt-1" readOnly />
                          </div>
                          <div>
                            <span className="text-muted-foreground">Make:</span>
                            <input type="text" value={formData.tradeInMake || ''} className="w-full border px-2 py-1 rounded bg-white mt-1" readOnly />
                          </div>
                          <div>
                            <span className="text-muted-foreground">Odometer:</span>
                            <input type="text" value={formData.tradeInMileage || ''} className="w-full border px-2 py-1 rounded bg-white mt-1" readOnly />
                          </div>
                          <div>
                            <span className="text-muted-foreground">Model:</span>
                            <input type="text" value={formData.tradeInModel || ''} className="w-full border px-2 py-1 rounded bg-white mt-1" readOnly />
                          </div>
                          <div>
                            <span className="text-muted-foreground">Allowance:</span>
                            <span className="font-semibold">{formatCurrency(transactionValues.tradeInAllowance)}</span>
                          </div>
                          <div className="col-span-2">
                            <span className="text-muted-foreground">Body Style:</span>
                            <input type="text" value="" className="w-full border px-2 py-1 rounded bg-white mt-1" placeholder="Auto-filled" readOnly />
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Lien Information */}
                    <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                      <h4 className="font-semibold text-base mb-4 text-on-surface">Lien</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Lien Amount:</span>
                          <span>$0.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Net Trade In Allowance:</span>
                          <span>{formatCurrency(transactionValues.tradeInAllowance)}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Balance Owed To:</span>
                          <input type="text" className="w-full border px-2 py-1 rounded bg-white mt-1" placeholder="N/A" readOnly />
                        </div>
                      </div>
                    </div>

                    {/* Taxes */}
                    <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                      <h4 className="font-semibold text-base mb-4 text-on-surface">Taxes</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Province:</span>
                          <select className="border px-2 py-1 rounded bg-white" defaultValue={formData.province}>
                            <option value="BC">British Columbia</option>
                            <option value="ON">Ontario</option>
                            <option value="AB">Alberta</option>
                          </select>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">PST:</span>
                          <div className="flex items-center space-x-2">
                            <select className="border px-2 py-1 rounded bg-white w-20">
                              <option>7.000</option>
                            </select>
                            <span>{formatCurrency(transactionValues.pstAmount)}</span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">GST/HST:</span>
                          <div className="flex items-center space-x-2">
                            <select className="border px-2 py-1 rounded bg-white w-20">
                              <option>5.00</option>
                            </select>
                            <span>{formatCurrency(transactionValues.gstAmount)}</span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Federal Luxury Tax:</span>
                          <span>$0.00</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    {/* Fees */}
                    <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                      <h4 className="font-semibold text-base mb-4 text-on-surface">Fees</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Cash Down:</span>
                          <span>$0.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Doc Fee:</span>
                          <span>$0.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Rebate:</span>
                          <span>$0.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Other Taxable:</span>
                          <span>$0.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">PPSA:</span>
                          <span className="font-semibold">$100.51</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Registration Fee:</span>
                          <span className="font-semibold">$38.50</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Administration Fee:</span>
                          <span className="font-semibold">$598.43</span>
                        </div>
                        <div className="flex justify-between font-semibold pt-2 border-t">
                          <span>Net Selling Price:</span>
                          <span className="text-primary-50">{formatCurrency(transactionValues.netSellingPrice)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Financing Terms */}
                    <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                      <h4 className="font-semibold text-base mb-4 text-on-surface">Financing Terms</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Term:</span>
                          <select className="border px-2 py-1 rounded bg-yellow-100 w-20">
                            <option>84</option>
                          </select>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Cost of Borrowing:</span>
                          <span className="font-semibold">{formatCurrency((transactionValues.totalMonthlyPayment * 84) - transactionValues.amountToFinance)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Payment Frequency:</span>
                          <select className="border px-2 py-1 rounded bg-yellow-100">
                            <option>Monthly</option>
                          </select>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Payment Frequency Amount:</span>
                          <span>{formatCurrency(transactionValues.totalMonthlyPayment)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Total Amount Financed:</span>
                          <span>{formatCurrency(transactionValues.amountToFinance)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Interest Rate:</span>
                          <select className="border px-2 py-1 rounded bg-yellow-100 w-20">
                            <option>16.49</option>
                          </select>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Total Monthly Payment:</span>
                          <span className="font-semibold">{formatCurrency(transactionValues.totalMonthlyPayment)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">APR:</span>
                          <span>18.37</span>
                        </div>
                      </div>
                    </div>

                    {/* Dealer Advance */}
                    <div className="bg-surface-container-lowest rounded-lg p-4 border border-outline-variant">
                      <h4 className="font-semibold text-base mb-4 text-on-surface">Dealer Advance</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Amount to Finance:</span>
                          <span className="font-semibold">{formatCurrency(transactionValues.amountToFinance)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Registration & Set up Costs:</span>
                          <span>{formatCurrency(transactionValues.fees)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Dealer Participation:</span>
                          <span>$0.00</span>
                        </div>
                        <div className="flex justify-between font-semibold pt-2 border-t">
                          <span>Dealer Extra Total:</span>
                          <span>{formatCurrency(transactionValues.fees)}</span>
                        </div>
                        <div className="flex justify-between font-semibold text-primary-50">
                          <span>Advance to Dealer:</span>
                          <span>{formatCurrency(transactionValues.advanceToDealer)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Deal Notes */}
              <Card className="border-2 border-outline-variant shadow-sm">
                <CardContent className="p-6">
                  <h4 className="font-bold text-lg mb-4 flex items-center space-x-2">
                    <FileText className="h-6 w-6 text-tertiary-50" />
                    <span>Deal Notes</span>
                  </h4>
                  <Textarea
                    placeholder="Add any additional notes or special conditions for this deal..."
                    value={formData.dealNotes}
                    onChange={(e) => updateFormData({ dealNotes: e.target.value })}
                    className="min-h-[120px] text-base"
                    disabled={isSubmitting}
                  />
                </CardContent>
              </Card>

              {/* Step 5 Notes Button */}
              <div className="flex justify-start pt-6 border-t border-outline-variant">
                <StepNotesButton
                  notes={notes}
                  onNotesToggle={onNotesToggle}
                  disabled={isSubmitting}
                  currentStep={5}
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4 p-6 bg-gradient-to-r from-surface-bright to-surface-container-low border-2 border-outline-variant rounded-xl mt-8 shadow-sm">
        <Button 
          variant="outline" 
          onClick={onBack} 
          disabled={isSubmitting}
          className="px-6 py-3 text-base font-medium"
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-brand-orange text-white hover:bg-brand-orange-dark px-8 py-3 text-base font-semibold shadow-orange-md hover:shadow-orange-lg transition-all"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              Creating Deal...
            </>
          ) : (
            <>
              <CheckCircle className="h-5 w-5 mr-2" />
              Create Deal
            </>
          )}
        </Button>
      </div>
    </div>
  );
}