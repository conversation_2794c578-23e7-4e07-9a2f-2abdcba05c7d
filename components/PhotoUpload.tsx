/**
 * PhotoUpload Component - File Upload with Preview for Deal Builder
 * 
 * Reusable photo upload component with drag & drop functionality,
 * image preview, and proper error handling. Designed for customer
 * photos and ID document uploads in the deal creation flow.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { useState, useRef, useCallback } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Upload, X, Image as ImageIcon, FileText } from 'lucide-react';
import { cn } from '../utils/cn';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface PhotoUploadProps {
  label?: string;
  value?: string;
  onChange?: (file: string | undefined) => void;
  onFileSelect?: (file: File | null) => void;
  currentFile?: File | null;
  disabled?: boolean;
  accept?: string;
  maxSize?: number; // in MB
  className?: string;
  placeholder?: string;
  type?: 'photo' | 'document';
}

export function PhotoUpload({
  label,
  value,
  onChange,
  onFileSelect,
  currentFile,
  disabled = false,
  accept = 'image/*',
  maxSize = 5,
  className,
  placeholder,
  type = 'photo'
}: PhotoUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((file: File) => {
    setError('');

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`File size must be less than ${maxSize}MB`);
      return;
    }

    // Validate file type if it's a photo
    if (type === 'photo' && !file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    // Use the new callback if provided, otherwise the old one
    if (onFileSelect) {
      onFileSelect(file);
    } else if (onChange) {
      // Create object URL for preview
      const imageUrl = URL.createObjectURL(file);
      onChange(imageUrl);
    }
  }, [maxSize, onChange, onFileSelect, type]);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect, disabled]);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleRemove = () => {
    if (displayValue && currentFile) {
      URL.revokeObjectURL(displayValue);
    }
    if (onFileSelect) {
      onFileSelect(null);
    } else if (onChange) {
      onChange(undefined);
    }
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const displayValue = value || (currentFile ? URL.createObjectURL(currentFile) : undefined);

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="text-sm font-medium text-on-surface">
          {label}
        </label>
      )}
      
      <Card
        className={cn(
          'relative border-2 border-dashed transition-all duration-200 cursor-pointer',
          'hover:border-primary-50 hover:bg-primary-95',
          isDragOver && 'border-primary-50 bg-primary-95',
          disabled && 'opacity-50 cursor-not-allowed',
          error && 'border-error bg-error-container',
          value && 'border-solid'
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        <div className="p-6">
          {displayValue ? (
            // Preview State
            <div className="relative">
              <div className="flex items-center justify-center mb-3">
                {type === 'photo' ? (
                  <ImageWithFallback
                    src={displayValue}
                    alt="Upload preview"
                    className="w-20 h-20 rounded-lg object-cover border"
                  />
                ) : (
                  <div className="w-20 h-20 rounded-lg bg-neutral-95 border flex items-center justify-center">
                    <FileText className="h-8 w-8 text-neutral-60" />
                  </div>
                )}
              </div>
              
              <div className="text-center">
                <p className="text-sm font-medium text-on-surface">
                  {type === 'photo' ? 'Photo uploaded' : 'Document uploaded'}
                </p>
                <p className="text-xs text-on-surface-variant mt-1">
                  Click to change or drag a new file
                </p>
              </div>

              {!disabled && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-error text-white hover:bg-error-dark"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove();
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          ) : (
            // Upload State
            <div className="text-center">
              <div className="flex justify-center mb-3">
                <div className="p-3 rounded-full bg-primary-95">
                  <Upload className="h-6 w-6 text-primary-50" />
                </div>
              </div>
              
              <h3 className="text-sm font-medium text-on-surface mb-1">
                {placeholder || `Upload ${type === 'photo' ? 'photo' : 'document'}`}
              </h3>
              
              <p className="text-xs text-on-surface-variant mb-3">
                Drag and drop or click to select
              </p>
              
              <div className="text-xs text-on-surface-variant">
                Maximum file size: {maxSize}MB
              </div>
            </div>
          )}
        </div>
      </Card>

      {error && (
        <p className="text-xs text-error mt-1">{error}</p>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
}