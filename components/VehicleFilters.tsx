/**
 * VehicleFilters Component - Ultra-Compact Vehicle Search Filters
 * 
 * Multi-row filter system for vehicle inventory search with Material Design 3
 * components and SubPrime Pro orange accent styling. Optimized for 1366x768
 * resolution with ultra-compact layout for minimal scrolling.
 * 
 * Features:
 * - 15 filter fields in ultra-compact organized grid layout
 * - Canadian automotive data (provinces, common makes/models)
 * - Mix of dropdown selects and text inputs
 * - Real-time filter state management
 * - Material Design 3 form components
 * - Enhanced interactive states with orange accents
 * 
 * Vision and Design by <PERSON>it<PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { useState } from 'react';
import { 
  SelectField, 
  SelectFieldTrigger, 
  SelectFieldContent, 
  SelectFieldItem, 
  SelectFieldValue 
} from './ui/material/select-field';
import { TextField } from './ui/material/text-field';

export interface VehicleFilterState {
  make: string;
  model: string;
  year: string;
  packageTrim: string;
  vin: string;
  price: string;
  doors: string;
  bodyType: string;
  fuelType: string;
  drivetrain: string;
  transmission: string;
  seating: string;
  exteriorColor: string;
  city: string;
  province: string;
  store: string;
  group: string;
}

interface VehicleFiltersProps {
  filters: VehicleFilterState;
  onFiltersChange: (filters: VehicleFilterState) => void;
  disabled?: boolean;
}

const initialFilters: VehicleFilterState = {
  make: '',
  model: '',
  year: '',
  packageTrim: '',
  vin: '',
  price: '',
  doors: '',
  bodyType: '',
  fuelType: '',
  drivetrain: '',
  transmission: '',
  seating: '',
  exteriorColor: '',
  city: '',
  province: '',
  store: '',
  group: ''
};

// Canadian automotive data
const MAKES = [
  'Acura', 'Audi', 'BMW', 'Buick', 'Cadillac', 'Chevrolet', 'Chrysler', 'Dodge',
  'Ford', 'GMC', 'Honda', 'Hyundai', 'Infiniti', 'Jeep', 'Kia', 'Lexus',
  'Lincoln', 'Mazda', 'Mercedes-Benz', 'Mitsubishi', 'Nissan', 'Ram', 'Subaru',
  'Toyota', 'Volkswagen', 'Volvo'
];

const MODELS = [
  'Accord', 'Altima', 'Camry', 'Civic', 'Corolla', 'CR-V', 'Elantra', 'Escape',
  'Explorer', 'F-150', 'Focus', 'Fusion', 'Jetta', 'Malibu', 'Pilot', 'RAV4',
  'Sentra', 'Sierra', 'Silverado', 'Sorento', 'Tucson', 'Wrangler'
];

const YEARS = Array.from({ length: 25 }, (_, i) => (2024 - i).toString());

const PACKAGE_TRIMS = [
  'Base', 'S', 'SE', 'SEL', 'SL', 'SV', 'L', 'LE', 'XLE', 'Limited', 'Touring',
  'Sport', 'Luxury', 'Premium', 'Platinum', 'Denali', 'Rubicon', 'Overland'
];

const DOORS = ['2', '3', '4', '5'];

const BODY_TYPES = [
  'Sedan', 'SUV', 'Truck', 'Coupe', 'Hatchback', 'Convertible', 'Wagon',
  'Crossover', 'Minivan', 'Pickup'
];

const FUEL_TYPES = [
  'Gasoline', 'Hybrid', 'Electric', 'Plug-in Hybrid', 'Diesel', 'Flex Fuel'
];

const DRIVETRAINS = ['FWD', 'RWD', 'AWD', '4WD'];

const TRANSMISSIONS = [
  'Manual', 'Automatic', 'CVT', '6-Speed Manual', '8-Speed Automatic',
  '9-Speed Automatic', '10-Speed Automatic'
];

const SEATING = ['2', '4', '5', '6', '7', '8'];

const EXTERIOR_COLORS = [
  'White', 'Black', 'Silver', 'Gray', 'Red', 'Blue', 'Green', 'Brown',
  'Gold', 'Orange', 'Yellow', 'Purple', 'Beige', 'Maroon'
];

const CANADIAN_CITIES = [
  'Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Edmonton', 'Ottawa',
  'Winnipeg', 'Quebec City', 'Hamilton', 'Kitchener', 'London', 'Victoria'
];

const CANADIAN_PROVINCES = [
  { code: 'ON', name: 'Ontario' },
  { code: 'BC', name: 'British Columbia' },
  { code: 'AB', name: 'Alberta' },
  { code: 'QC', name: 'Quebec' },
  { code: 'MB', name: 'Manitoba' },
  { code: 'SK', name: 'Saskatchewan' },
  { code: 'NS', name: 'Nova Scotia' },
  { code: 'NB', name: 'New Brunswick' },
  { code: 'PE', name: 'Prince Edward Island' },
  { code: 'NL', name: 'Newfoundland and Labrador' },
  { code: 'YT', name: 'Yukon' },
  { code: 'NT', name: 'Northwest Territories' },
  { code: 'NU', name: 'Nunavut' }
];

const DEALERSHIP_STORES = [
  'Downtown Toronto', 'North York', 'Mississauga', 'Brampton', 'Markham',
  'Vancouver Main', 'Burnaby', 'Richmond', 'Calgary Central', 'Edmonton West'
];

const DEALERSHIP_GROUPS = [
  'SubPrime Metro', 'SubPrime West', 'SubPrime Central', 'SubPrime East',
  'SubPrime Premium', 'SubPrime Express'
];

export function VehicleFilters({ filters, onFiltersChange, disabled = false }: VehicleFiltersProps) {
  const updateFilter = (key: keyof VehicleFilterState, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const clearAllFilters = () => {
    onFiltersChange(initialFilters);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className="bg-surface border border-outline-variant rounded-lg p-3 mb-3 space-y-2">
      {/* Ultra-Compact Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-semibold text-on-surface leading-tight">Vehicle Filters</h3>
          <p className="text-xs text-on-surface-variant leading-tight">
            Refine your vehicle search using the filters below
          </p>
        </div>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            disabled={disabled}
            className="btn-ghost text-primary-50 hover:text-primary-60 focus-orange text-xs font-medium disabled:opacity-50"
          >
            Clear All
          </button>
        )}
      </div>

      {/* Ultra-Compact Filter Grid */}
      <div className="space-y-2">
        {/* Row 1: Make, Model, Year, Package/trim, Doors, Body type - 6 columns */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
          <SelectField 
            value={filters.make} 
            onValueChange={(value) => updateFilter('make', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Make" compact>
              <SelectFieldValue placeholder="Make" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {MAKES.map(make => (
                <SelectFieldItem key={make} value={make}>{make}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.model} 
            onValueChange={(value) => updateFilter('model', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Model" compact>
              <SelectFieldValue placeholder="Model" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {MODELS.map(model => (
                <SelectFieldItem key={model} value={model}>{model}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.year} 
            onValueChange={(value) => updateFilter('year', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Year" compact>
              <SelectFieldValue placeholder="Year" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {YEARS.map(year => (
                <SelectFieldItem key={year} value={year}>{year}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.packageTrim} 
            onValueChange={(value) => updateFilter('packageTrim', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Package / trim" compact>
              <SelectFieldValue placeholder="Package / trim" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {PACKAGE_TRIMS.map(trim => (
                <SelectFieldItem key={trim} value={trim}>{trim}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.doors} 
            onValueChange={(value) => updateFilter('doors', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Doors" compact>
              <SelectFieldValue placeholder="Doors" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {DOORS.map(doors => (
                <SelectFieldItem key={doors} value={doors}>{doors}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.bodyType} 
            onValueChange={(value) => updateFilter('bodyType', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Body type" compact>
              <SelectFieldValue placeholder="Body type" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {BODY_TYPES.map(type => (
                <SelectFieldItem key={type} value={type}>{type}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>
        </div>

        {/* Row 2: VIN, Price, Fuel type, Drivetrain, Transmission, Seating - 6 columns */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
          <TextField
            label="VIN"
            value={filters.vin}
            onChange={(e) => updateFilter('vin', e.target.value)}
            placeholder="VIN"
            disabled={disabled}
            compact
          />

          <TextField
            label="Price"
            value={filters.price}
            onChange={(e) => updateFilter('price', e.target.value)}
            placeholder="Price"
            type="number"
            disabled={disabled}
            compact
          />

          <SelectField 
            value={filters.fuelType} 
            onValueChange={(value) => updateFilter('fuelType', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Fuel type" compact>
              <SelectFieldValue placeholder="Fuel type" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {FUEL_TYPES.map(fuel => (
                <SelectFieldItem key={fuel} value={fuel}>{fuel}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.drivetrain} 
            onValueChange={(value) => updateFilter('drivetrain', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Drivetrain" compact>
              <SelectFieldValue placeholder="Drivetrain" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {DRIVETRAINS.map(drive => (
                <SelectFieldItem key={drive} value={drive}>{drive}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.transmission} 
            onValueChange={(value) => updateFilter('transmission', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Transmission" compact>
              <SelectFieldValue placeholder="Transmission" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {TRANSMISSIONS.map(trans => (
                <SelectFieldItem key={trans} value={trans}>{trans}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.seating} 
            onValueChange={(value) => updateFilter('seating', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Seating" compact>
              <SelectFieldValue placeholder="Seating" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {SEATING.map(seats => (
                <SelectFieldItem key={seats} value={seats}>{seats}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>
        </div>

        {/* Row 3: Exterior color, City, Province, Store, Group - 5 columns */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
          <SelectField 
            value={filters.exteriorColor} 
            onValueChange={(value) => updateFilter('exteriorColor', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Exterior color" compact>
              <SelectFieldValue placeholder="Exterior color" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {EXTERIOR_COLORS.map(color => (
                <SelectFieldItem key={color} value={color}>{color}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.city} 
            onValueChange={(value) => updateFilter('city', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="City" compact>
              <SelectFieldValue placeholder="City" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {CANADIAN_CITIES.map(city => (
                <SelectFieldItem key={city} value={city}>{city}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.province} 
            onValueChange={(value) => updateFilter('province', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Province" compact>
              <SelectFieldValue placeholder="Province" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {CANADIAN_PROVINCES.map(province => (
                <SelectFieldItem key={province.code} value={province.code}>
                  {province.name}
                </SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.store} 
            onValueChange={(value) => updateFilter('store', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Store" compact>
              <SelectFieldValue placeholder="Store" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {DEALERSHIP_STORES.map(store => (
                <SelectFieldItem key={store} value={store}>{store}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>

          <SelectField 
            value={filters.group} 
            onValueChange={(value) => updateFilter('group', value)}
            disabled={disabled}
          >
            <SelectFieldTrigger label="Group" compact>
              <SelectFieldValue placeholder="Group" />
            </SelectFieldTrigger>
            <SelectFieldContent>
              {DEALERSHIP_GROUPS.map(group => (
                <SelectFieldItem key={group} value={group}>{group}</SelectFieldItem>
              ))}
            </SelectFieldContent>
          </SelectField>
        </div>
      </div>

      {/* Active Filters Summary - Ultra-Compact */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-1.5 pt-2 border-t border-outline-variant">
          <span className="text-xs font-medium text-on-surface-variant">Active:</span>
          {Object.entries(filters).map(([key, value]) => {
            if (!value) return null;
            const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            return (
              <div
                key={key}
                className="inline-flex items-center gap-1 px-1.5 py-0.5 bg-primary-95 text-primary-50 rounded text-xs"
              >
                <span>{label}: {value}</span>
                <button
                  onClick={() => updateFilter(key as keyof VehicleFilterState, '')}
                  disabled={disabled}
                  className="hover:text-primary-60 focus-orange disabled:opacity-50 text-xs"
                >
                  ×
                </button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

export { initialFilters };
export type { VehicleFilterState };