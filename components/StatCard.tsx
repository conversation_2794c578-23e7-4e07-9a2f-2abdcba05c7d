/**
 * Statistics Card Component - Enhanced Consistency System with Fixed Icon Sizing
 * 
 * Reusable stat card component for inventory snapshots and dashboard metrics.
 * Provides standardized styling, typography, and visual hierarchy across all stat displays.
 * 
 * DESIGN PRINCIPLES:
 * - Consistent spacing and layout structure
 * - Material Design 3 color integration with orange brand accents
 * - FIXED: Enforced icon sizing with proper CSS specificity
 * - Professional typography hierarchy
 * - Smooth transitions and hover effects
 * - Accessibility considerations with proper contrast
 * 
 * ICON SIZING FIX:
 * - Enhanced CSS specificity for icon sizing
 * - Consistent 24px icons across all inventory stat cards
 * - Proper icon container dimensions
 * - Removed conflicts with global icon styling
 * 
 * Vision and Design by <PERSON>itwi<PERSON> Gupte | Built by Figma Make - Claude Sonnet
 */

import { LucideIcon } from 'lucide-react';
import { Card, CardContent } from './ui/card';
import { cn } from '../utils/cn';

export type StatCardVariant = 'primary' | 'secondary' | 'accent' | 'highlight' | 'success' | 'warning' | 'neutral';
export type StatCardSize = 'compact' | 'default' | 'large';

interface StatCardProps {
  title: string;
  value: string;
  trend: string;
  trendLabel: string;
  icon: LucideIcon;
  variant?: StatCardVariant;
  size?: StatCardSize;
  className?: string;
  onClick?: () => void;
}

const variantClasses = {
  primary: {
    iconContainer: 'bg-primary-95 text-primary-40 stat-card-icon-primary',
    trendColor: 'text-primary-50'
  },
  secondary: {
    iconContainer: 'bg-primary-90 text-primary-50 stat-card-icon-secondary',
    trendColor: 'text-primary-60'
  },
  accent: {
    iconContainer: 'bg-primary-80 text-primary-60 stat-card-icon-accent',
    trendColor: 'text-primary-70'
  },
  highlight: {
    iconContainer: 'bg-orange-50 text-brand-orange stat-card-icon-highlight',
    trendColor: 'text-brand-orange'
  },
  success: {
    iconContainer: 'bg-green-50 text-green-600 stat-card-icon-success',
    trendColor: 'text-green-600'
  },
  warning: {
    iconContainer: 'bg-yellow-50 text-yellow-600 stat-card-icon-warning',
    trendColor: 'text-yellow-600'
  },
  neutral: {
    iconContainer: 'bg-neutral-95 text-neutral-60 stat-card-icon-neutral',
    trendColor: 'text-neutral-60'
  }
};

const sizeClasses = {
  compact: {
    card: 'p-4',
    iconContainer: 'w-10 h-10 p-2',
    iconSize: 'stat-card-icon-compact',
    valueText: 'text-2xl',
    titleText: 'text-xs',
    trendText: 'text-xs',
    spacing: 'space-x-3'
  },
  default: {
    card: 'p-6',
    iconContainer: 'w-12 h-12 p-3',
    iconSize: 'stat-card-icon-default',
    valueText: 'text-3xl',
    titleText: 'text-sm',
    trendText: 'text-sm',
    spacing: 'space-x-4'
  },
  large: {
    card: 'p-8',
    iconContainer: 'w-16 h-16 p-4',
    iconSize: 'stat-card-icon-large',
    valueText: 'text-4xl',
    titleText: 'text-base',
    trendText: 'text-base',
    spacing: 'space-x-6'
  }
};

/**
 * StatCard Component - Enhanced with Consistent Icon Sizing
 * 
 * Renders a consistently styled statistics card with icon, value, trend, and metadata.
 * Supports multiple variants and sizes for different dashboard contexts.
 * Now includes enforced icon sizing with CSS classes to prevent conflicts.
 */
export function StatCard({
  title,
  value,
  trend,
  trendLabel,
  icon: Icon,
  variant = 'primary',
  size = 'default',
  className,
  onClick
}: StatCardProps) {
  const variantStyle = variantClasses[variant];
  const sizeStyle = sizeClasses[size];

  return (
    <Card 
      className={cn(
        "border-0 shadow-md transition-all duration-200 hover:shadow-lg hover:scale-[1.02] stat-card-container",
        onClick && "cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      <CardContent className={cn("flex items-center", sizeStyle.spacing, sizeStyle.card)}>
        {/* Icon Container - Enhanced with Consistent Sizing */}
        <div className={cn(
          "rounded-lg flex items-center justify-center flex-shrink-0 transition-all duration-200 stat-card-icon-container",
          sizeStyle.iconContainer,
          variantStyle.iconContainer
        )}>
          <Icon 
            className={cn(
              sizeStyle.iconSize,
              "stroke-current stat-card-icon-enforced"
            )} 
            strokeWidth={1.5} 
          />
        </div>

        {/* Content Section */}
        <div className="flex-1 min-w-0">
          {/* Value and Trend Row */}
          <div className="flex items-center justify-between mb-1">
            <p className={cn(
              "font-bold text-foreground leading-tight stat-card-value",
              sizeStyle.valueText
            )}>
              {value}
            </p>
            <div className="text-right flex-shrink-0">
              <p className={cn(
                "font-medium leading-tight stat-card-trend",
                sizeStyle.trendText,
                variantStyle.trendColor
              )}>
                {trend}
              </p>
            </div>
          </div>

          {/* Title and Trend Label Row */}
          <div className="space-y-0.5">
            <p className={cn(
              "text-muted-foreground font-medium leading-tight stat-card-title",
              sizeStyle.titleText
            )}>
              {title}
            </p>
            <p className={cn(
              "text-muted-foreground leading-tight stat-card-trend-label",
              sizeStyle.trendText
            )}>
              {trendLabel}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Pre-configured StatCard Variants for Common Use Cases - Enhanced Consistency
 */

// Inventory Snapshot Card - Consistent design for 4-card grids
export function InventoryStatCard({ 
  title, 
  value, 
  trend, 
  trendLabel, 
  icon, 
  variant = 'primary',
  onClick 
}: Omit<StatCardProps, 'size'>) {
  return (
    <StatCard
      title={title}
      value={value}
      trend={trend}
      trendLabel={trendLabel}
      icon={icon}
      variant={variant}
      size="default"
      onClick={onClick}
      className="inventory-stat-card"
    />
  );
}

// Dashboard Stat Card - For main dashboard metrics
export function DashboardStatCard({ 
  title, 
  value, 
  trend, 
  trendLabel, 
  icon, 
  onClick 
}: Omit<StatCardProps, 'size' | 'variant'>) {
  return (
    <StatCard
      title={title}
      value={value}
      trend={trend}
      trendLabel={trendLabel}
      icon={icon}
      variant="primary"
      size="default"
      onClick={onClick}
      className="dashboard-stat-card"
    />
  );
}

// Performance Metric Card - For analytics sections
export function PerformanceStatCard({ 
  title, 
  value, 
  trend, 
  trendLabel, 
  icon, 
  variant = 'highlight',
  onClick 
}: Omit<StatCardProps, 'size'>) {
  return (
    <StatCard
      title={title}
      value={value}
      trend={trend}
      trendLabel={trendLabel}
      icon={icon}
      variant={variant}
      size="compact"
      onClick={onClick}
      className="performance-stat-card"
    />
  );
}