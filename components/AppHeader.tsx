/**
 * Application Header Component
 * 
 * Enhanced header with navigation toggle, brand identity, and user profile section.
 * Implements Material Design 3 principles with proper accessibility features.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> | Built by Figma Make - Claude Sonnet
 */

import { Button } from './ui/button';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { BrandIcon } from './BrandIcon';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface AppHeaderProps {
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
}

export function AppHeader({ sidebarCollapsed, onToggleSidebar }: AppHeaderProps) {
  return (
    <header className="border-b shadow-sm bg-brand-orange flex-shrink-0">
      <div className="w-full px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Navigation Toggle - Enhanced with accessibility */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleSidebar}
                  className="h-10 w-10 p-0 text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                  aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
                >
                  {sidebarCollapsed ? (
                    <ChevronRight className="h-5 w-5" />
                  ) : (
                    <ChevronLeft className="h-5 w-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>{sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}</p>
              </TooltipContent>
            </Tooltip>
            
            {/* Brand Identity Section */}
            <div className="p-2 rounded-lg bg-white/10 backdrop-blur-sm">
              <BrandIcon className="h-10 w-10 text-white" size={40} />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-white">SubPrime Pro</h1>
              <p className="text-sm text-white/80">Dealership Management System</p>
            </div>
          </div>
          
          {/* User Profile Section */}
          <div className="flex items-center space-x-3">
            <div className="hidden sm:block text-right text-white">
              <p className="text-sm font-medium">John Dealer</p>
              <p className="text-xs text-white/80">Admin</p>
            </div>
            <Avatar className="ring-2 ring-white/20 hover:ring-white/40 transition-all cursor-pointer">
              <AvatarImage src="/api/placeholder/32/32" alt="User avatar" />
              <AvatarFallback className="bg-white/20 text-white">JD</AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </header>
  );
}