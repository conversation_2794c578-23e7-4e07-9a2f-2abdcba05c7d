/**
 * StepTracker Demo Component - Shows all states
 * 
 * A demo component to showcase the enhanced StepTracker with
 * all states visible at once for testing purposes.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import React, { useState } from 'react';
import { StepTracker, Step } from './StepTracker';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { User, CreditCard, Car, Handshake, FileText } from 'lucide-react';

export function StepTrackerDemo() {
  const [currentStep, setCurrentStep] = useState(2);

  // Demo steps with mixed states to showcase all visual effects
  const demoSteps: Step[] = [
    {
      id: 'customer-info',
      title: 'Customer Info',
      subtitle: 'Step 1',
      icon: User,
      state: 'complete', // Completed step - should show green
      stepNumber: 1
    },
    {
      id: 'approval',
      title: 'Approval',
      subtitle: 'Step 2',
      icon: CreditCard,
      state: 'active', // Active step - should show orange
      stepNumber: 2
    },
    {
      id: 'trade-in',
      title: 'Trade-in',
      subtitle: 'Step 3',
      icon: Car,
      state: 'pending', // Pending step - should show grey
      stepNumber: 3
    },
    {
      id: 'vehicle',
      title: 'Vehicle',
      subtitle: 'Step 4',
      icon: Car,
      state: 'pending',
      stepNumber: 4
    },
    {
      id: 'finish-deal',
      title: 'Finish Deal',
      subtitle: 'Step 5',
      icon: Handshake,
      state: 'pending',
      stepNumber: 5
    }
  ];

  const handleStepClick = (stepNumber: number) => {
    setCurrentStep(stepNumber);
    console.log(`Clicked step ${stepNumber}`);
  };

  const simulateProgress = () => {
    setCurrentStep(prev => prev < 5 ? prev + 1 : 1);
  };

  // Update step states based on current step
  const getUpdatedSteps = (): Step[] => {
    return demoSteps.map(step => ({
      ...step,
      state: step.stepNumber < currentStep ? 'complete' :
             step.stepNumber === currentStep ? 'active' : 'pending'
    }));
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-primary-50" />
            <span>StepTracker Enhancement Demo</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              <strong>Features to test:</strong>
            </p>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Hover over any step to see the <strong>pale orange blur effect</strong></li>
              <li>• Notice the <strong>green completion states</strong> for completed steps</li>
              <li>• All icons use <strong>outline/stroke styling</strong> consistently</li>
              <li>• Text colors stay <strong>consistent on hover</strong> (no color changes)</li>
              <li>• <strong>Accessible contrast</strong> - Active state uses darker orange for better readability</li>
              <li>• Progress line spans precisely between step centers</li>
              <li>• Click any step to navigate and see state changes</li>
            </ul>
          </div>

          <div className="flex space-x-4">
            <Button 
              onClick={simulateProgress}
              className="bg-primary-50 text-white hover:bg-primary-60"
            >
              Advance Step ({currentStep}/5)
            </Button>
            <Button 
              variant="outline"
              onClick={() => setCurrentStep(1)}
              className="border-primary-50 text-primary-50 hover:bg-primary-95"
            >
              Reset to Step 1
            </Button>
          </div>

          {/* The Enhanced StepTracker */}
          <div className="bg-white border border-outline-variant rounded-lg p-6">
            <StepTracker 
              steps={getUpdatedSteps()} 
              onStepClick={handleStepClick}
              className="border-b border-outline-variant pb-4" 
            />
            
            <div className="mt-6 p-4 bg-surface-container-lowest rounded-lg">
              <h4 className="font-medium text-on-surface mb-2">Current State:</h4>
              <p className="text-sm text-on-surface-variant">
                Step {currentStep} of 5 - Click any step or use the buttons above to see the enhanced hover effects and state transitions.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default StepTrackerDemo;