/**
 * DealsBuiltTable Component - Professional Deals Management Table
 * 
 * Comprehensive table displaying previously built deals with enhanced visual design
 * and professional aesthetics. Optimized for 1366x768 resolution with proper
 * interactive states and Material Design 3 integration.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { useState } from 'react';
import { Deal } from '../types';
import { Card, CardContent, CardHeader } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { 
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from './ui/tooltip';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from './ui/table';
import { Input } from './ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select';
import {
  HandshakeIcon,
  Search,
  Filter,
  Eye,
  Edit,
  MoreHorizontal,
  Calendar,
  DollarSign,
  MapPin,
  User,
  Car,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { formatCurrency, formatName, getInitials } from '../utils/stringUtils';
import { toast } from 'sonner@2.0.3';

interface DealsBuiltTableProps {
  deals: Deal[];
  onStartNewDeal: () => void;
  onViewDeal: (deal: Deal) => void;
  isSubmitting?: boolean;
}

export function DealsBuiltTable({ deals, onStartNewDeal, onViewDeal, isSubmitting = false }: DealsBuiltTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [locationFilter, setLocationFilter] = useState<string>('all');

  // Filter deals based on search and filters
  const filteredDeals = deals.filter(deal => {
    const matchesSearch = 
      deal.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      deal.vehicleInfo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      deal.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || deal.status === statusFilter;
    const matchesLocation = locationFilter === 'all' || deal.location === locationFilter;
    
    return matchesSearch && matchesStatus && matchesLocation;
  });

  // Get unique locations for filter
  const uniqueLocations = Array.from(new Set(deals.map(deal => deal.location))).filter(Boolean);

  // Get status styling with explanatory tooltips - Enhanced with rounded badges
  const getStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase();
    
    // Define tooltip content for each status
    const getTooltipContent = (statusType: string) => {
      switch (statusType) {
        case 'approved':
          return 'This deal has been approved by all required parties and is ready for completion. The customer can proceed with the purchase.';
        case 'pending':
          return 'This deal is currently under review by the approval team. Additional documentation or verification may be required.';
        case 'rejected':
          return 'This deal has been rejected due to credit issues, documentation problems, or other factors. Review notes for details.';
        case 'draft':
          return 'This deal is still being built and has not been submitted for approval. Information may be incomplete.';
        default:
          return `Deal status: ${status}. Contact your supervisor for more information about this status.`;
      }
    };

    const tooltipContent = getTooltipContent(statusLower);

    switch (statusLower) {
      case 'approved':
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="bg-green-50 text-green-800 border-green-500 hover:bg-green-100 rounded-full px-3 py-1 text-xs font-semibold cursor-help transition-colors">
                Approved
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        );
      case 'pending':
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-500 hover:bg-yellow-100 rounded-full px-3 py-1 text-xs font-semibold cursor-help transition-colors">
                Pending
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        );
      case 'rejected':
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="bg-red-50 text-red-800 border-red-500 hover:bg-red-100 rounded-full px-3 py-1 text-xs font-semibold cursor-help transition-colors">
                Rejected
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        );
      case 'draft':
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="bg-gray-50 text-gray-800 border-gray-500 hover:bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold cursor-help transition-colors">
                Draft
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        );
      default:
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-500 hover:bg-blue-100 rounded-full px-3 py-1 text-xs font-semibold cursor-help transition-colors">
                {status}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        );
    }
  };

  // Handle row click
  const handleRowClick = (deal: Deal) => {
    onViewDeal(deal);
  };

  // Handle view deal button click
  const handleViewDeal = (deal: Deal, event: React.MouseEvent) => {
    event.stopPropagation();
    onViewDeal(deal);
  };

  return (
    <div className="space-y-6 max-w-full">
      {/* Primary CTA Section */}
      <div className="flex justify-end">
        <Button
          onClick={onStartNewDeal}
          disabled={isSubmitting}
          className="bg-brand-orange text-white hover:bg-brand-orange-dark flex items-center space-x-2 px-6 py-3 text-base font-semibold shadow-orange-md hover:shadow-orange-lg focus-orange transition-all"
        >
          <HandshakeIcon className="h-5 w-5" />
          <span>Start Building Deal</span>
        </Button>
      </div>



      {/* Enhanced Filters and Search - Expanded Layout */}
      <Card className="border-2 border-outline-variant">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-lg text-on-surface">Deal Management</h3>
            <Badge variant="outline" className="px-3 py-1 text-sm">
              {filteredDeals.length} of {deals.length} deals
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search deals by customer, vehicle, or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white border-outline-variant focus:border-primary-50"
              />
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full lg:w-52 bg-white border-outline-variant">
                <Filter className="h-4 w-4 mr-2 text-muted-foreground" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>

            {/* Location Filter */}
            <Select value={locationFilter} onValueChange={setLocationFilter}>
              <SelectTrigger className="w-full lg:w-52 bg-white border-outline-variant">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                <SelectValue placeholder="Filter by location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {uniqueLocations.map(location => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Enhanced Table with Orange Headers - Full Width Layout */}
          <div className="border border-outline-variant rounded-lg overflow-hidden bg-white shadow-sm">
            <Table className="w-full">
              <TableHeader>
                <TableRow className="bg-brand-orange hover:bg-brand-orange border-b border-brand-orange-dark">
                  <TableHead className="font-semibold text-white text-sm py-4 px-6">Deal ID</TableHead>
                  <TableHead className="font-semibold text-white text-sm py-4 px-6">Customer</TableHead>
                  <TableHead className="font-semibold text-white text-sm py-4 px-6">Vehicle</TableHead>
                  <TableHead className="font-semibold text-white text-sm py-4 px-6 text-right">Value</TableHead>
                  <TableHead className="font-semibold text-white text-sm py-4 px-6">Status</TableHead>
                  <TableHead className="font-semibold text-white text-sm py-4 px-6">Location</TableHead>
                  <TableHead className="font-semibold text-white text-sm py-4 px-6">Date Added</TableHead>
                  <TableHead className="font-semibold text-white text-sm py-4 px-6 text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDeals.length > 0 ? (
                  filteredDeals.map((deal) => (
                    <TableRow 
                      key={deal.id}
                      className="cursor-pointer hover:bg-gray-50 transition-colors duration-200 border-b border-gray-100 bg-white"
                      onClick={() => handleRowClick(deal)}
                    >
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 rounded-full bg-primary-50"></div>
                          <span className="text-sm font-semibold text-primary-50 font-mono">
                            {deal.id.split('-')[1]?.slice(-6) || deal.id.slice(-6)}
                          </span>
                        </div>
                      </TableCell>
                      
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10 border border-gray-200">
                            <AvatarFallback className="text-sm font-semibold bg-gray-100 text-gray-700">
                              {getInitials(deal.customerName || 'Unknown')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-semibold text-gray-900 text-sm">
                              {formatName(deal.customerName || 'Unknown Customer')}
                            </p>
                            <p className="text-xs text-gray-500">
                              {deal.approvalEntities?.length || 0} approval{(deal.approvalEntities?.length || 0) !== 1 ? 's' : ''}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center border border-gray-200">
                            <Car className="h-6 w-6 text-gray-500" />
                          </div>
                          <div>
                            <p className="font-semibold text-gray-900 text-sm">
                              {deal.vehicleInfo || 'Vehicle Information'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {deal.tradeInVehicle ? 'With trade-in' : 'No trade-in'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell className="py-4 px-6 text-right">
                        <div className="text-right">
                          <p className="font-bold text-lg text-primary-50">
                            {formatCurrency(deal.dealValue || 0)}
                          </p>
                          <p className="text-xs text-gray-500">Deal Value</p>
                        </div>
                      </TableCell>
                      
                      <TableCell className="py-4 px-6">
                        {getStatusBadge(deal.status || 'pending')}
                      </TableCell>
                      
                      <TableCell className="py-4 px-6">
                        <div className="text-sm text-gray-900 font-medium">
                          {deal.location || 'Unknown Location'}
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <MapPin className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">Location</span>
                        </div>
                      </TableCell>
                      
                      <TableCell className="py-4 px-6">
                        <div className="text-sm text-gray-900 font-medium">
                          {deal.createdDate ? new Date(deal.createdDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit'
                          }) : 'N/A'}
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <Calendar className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">Added</span>
                        </div>
                      </TableCell>
                      
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center justify-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-gray-100 hover:text-primary-50 text-gray-600"
                            onClick={(e) => handleViewDeal(deal, e)}
                            title="View deal details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-gray-100 hover:text-primary-50 text-gray-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              toast.info('More Options', {
                                description: 'Deal options menu',
                                duration: 2000,
                              });
                            }}
                            title="More options"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow className="bg-white">
                    <TableCell colSpan={8} className="h-32 text-center py-12">
                      <div className="flex flex-col items-center space-y-4">
                        <div className="p-4 rounded-full bg-gray-50 border border-gray-200">
                          <Search className="h-8 w-8 text-gray-400" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900 text-lg">No deals found</p>
                          <p className="text-sm text-gray-500 mt-1">
                            {searchTerm || statusFilter !== 'all' || locationFilter !== 'all'
                              ? 'Try adjusting your search or filters to find deals'
                              : 'Start building your first deal to see it here'}
                          </p>
                        </div>
                        {(!searchTerm && statusFilter === 'all' && locationFilter === 'all') && (
                          <Button
                            onClick={onStartNewDeal}
                            className="mt-4 bg-brand-orange text-white hover:bg-brand-orange-dark px-6 py-2 rounded-lg font-semibold shadow-orange-sm hover:shadow-orange-md transition-all"
                          >
                            <HandshakeIcon className="h-4 w-4 mr-2" />
                            Build Your First Deal
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Table Footer with Pagination Info */}
          {filteredDeals.length > 0 && (
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 bg-gray-50 px-6 py-3 rounded-b-lg">
              <p className="text-sm text-gray-600 font-medium">
                Showing {filteredDeals.length} of {deals.length} deals
              </p>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs px-3 py-1 border-gray-500 text-gray-600 rounded-full">
                  Latest deals shown first
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}