import React from 'react';
import { X, LucideIcon } from 'lucide-react';
import { Button } from './ui/button';
import { cn } from '../utils/cn';

interface SliderHeaderProps {
  title: string;
  subtitle?: string;
  triggerIcon?: LucideIcon | null;
  triggerAction?: string;
  onClose: () => void;
  className?: string;
}

/**
 * SliderHeader Component - Dark Themed
 * 
 * A reusable header component for slider panels and popups that displays:
 * - The trigger icon that opened the slider (for visual continuity)
 * - Title and optional subtitle
 * - Close button
 * 
 * Now styled with dark theme to match the navigation pane.
 */
export const SliderHeader: React.FC<SliderHeaderProps> = ({
  title,
  subtitle,
  triggerIcon: TriggerIcon,
  triggerAction,
  onClose,
  className
}) => {
  return (
    <div className={cn(
      "flex items-center justify-between p-6 border-b border-neutral-variant-30 bg-neutral-10",
      className
    )}>
      <div className="flex items-center space-x-4">
        {/* Trigger Icon - Shows the icon that opened this slider */}
        {TriggerIcon && (
          <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-brand-orange/20 border border-brand-orange/30">
            <TriggerIcon className="h-6 w-6 text-brand-orange" />
          </div>
        )}
        
        <div className="flex flex-col">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-neutral-95">{title}</h2>
            {triggerAction && (
              <div className="px-3 py-1 bg-brand-orange/20 border border-brand-orange/30 rounded-lg">
                <span className="text-xs font-medium text-brand-orange">{triggerAction}</span>
              </div>
            )}
          </div>
          {subtitle && (
            <p className="text-sm text-neutral-80 mt-1">{subtitle}</p>
          )}
        </div>
      </div>

      {/* Close Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onClose}
        className="h-10 w-10 p-0 rounded-xl hover:bg-neutral-20 text-neutral-95 hover:text-white transition-colors"
      >
        <X className="h-5 w-5" />
        <span className="sr-only">Close</span>
      </Button>
    </div>
  );
};

export default SliderHeader;