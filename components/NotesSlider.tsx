/**
 * NotesSlider Component - Deal Flow Notes Management System (Dark Theme with Orange Accents)
 * 
 * Comprehensive right-side slider for managing step-by-step notes throughout
 * the deal creation process. Features full CRUD operations with step tracking.
 * 
 * Updated with dark theme, opacity background, and orange accents for UI elements.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> ; Built by Figma Make - Claude Sonnet
 */

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Textarea } from './ui/textarea';
import { Card, CardContent, CardHeader } from './ui/card';
import { SliderHeader } from './SliderHeader';

import { DealNote } from '../types';
import { 
  StickyNote, 
  Plus, 
  Edit2, 
  Trash2, 
  Save, 
  RotateCcw,
  Clock,
  Hash,
  FileText
} from 'lucide-react';

import { toast } from 'sonner@2.0.3';
import { cn } from '../utils/cn';

// Define step names locally to avoid import issues
const STEP_NAMES: Record<number, string> = {
  1: 'Customer Information',
  2: 'Approval Entities', 
  3: 'Trade-in Details',
  4: 'Vehicle Selection',
  5: 'Deal Summary'
};

interface NotesSliderProps {
  isOpen: boolean;
  onClose: () => void;
  notes: DealNote[];
  onNotesChange: (notes: DealNote[]) => void;
  currentStep: number;
  disabled?: boolean;
}

interface EditingNote {
  id: string | null;
  content: string;
}

export function NotesSlider({
  isOpen,
  onClose,
  notes = [], // Default to empty array
  onNotesChange,
  currentStep = 1, // Default to step 1
  disabled = false
}: NotesSliderProps) {
  const [editingNote, setEditingNote] = useState<EditingNote>({ id: null, content: '' });
  const [isCreating, setIsCreating] = useState(false);

  // Ensure notes is always an array and currentStep is valid
  const safeNotes = Array.isArray(notes) ? notes : [];
  const safeCurrentStep = typeof currentStep === 'number' && currentStep >= 1 && currentStep <= 5 ? currentStep : 1;
  
  // Sort notes by creation date (newest first) with error handling
  const sortedNotes = [...safeNotes].sort((a, b) => {
    try {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA;
    } catch (error) {
      console.warn('Error sorting notes by date:', error);
      return 0;
    }
  });

  // Generate unique ID for new notes
  const generateNoteId = (): string => {
    return `note-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  // Get step name safely with comprehensive error handling
  const getStepName = (step: number | undefined | null): string => {
    try {
      // Handle invalid step values
      if (typeof step !== 'number' || isNaN(step) || step < 1 || step > 5) {
        console.warn('Invalid step value:', step);
        return 'Unknown Step';
      }
      
      // Return step name from local definition
      return STEP_NAMES[step] || `Step ${step}`;
    } catch (error) {
      console.error('Error getting step name:', error);
      return 'Unknown Step';
    }
  };

  // Create new note
  const handleCreateNote = () => {
    if (!editingNote.content || editingNote.content.trim() === '') {
      toast.error('Note content cannot be empty');
      return;
    }

    try {
      const newNote: DealNote = {
        id: generateNoteId(),
        step: safeCurrentStep,
        stepName: getStepName(safeCurrentStep),
        content: editingNote.content.trim(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const updatedNotes = [...safeNotes, newNote];
      onNotesChange(updatedNotes);
      
      setEditingNote({ id: null, content: '' });
      setIsCreating(false);
      
      toast.success('Note Added', {
        description: `Added note to ${newNote.stepName}`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error creating note:', error);
      toast.error('Failed to create note');
    }
  };

  // Update existing note
  const handleUpdateNote = (noteId: string) => {
    if (!editingNote.content || editingNote.content.trim() === '') {
      toast.error('Note content cannot be empty');
      return;
    }

    try {
      const updatedNotes = safeNotes.map(note => 
        note.id === noteId 
          ? { 
              ...note, 
              content: editingNote.content.trim(),
              updatedAt: new Date().toISOString()
            }
          : note
      );

      onNotesChange(updatedNotes);
      setEditingNote({ id: null, content: '' });
      
      toast.success('Note Updated', {
        description: 'Your note has been successfully updated',
        duration: 3000
      });
    } catch (error) {
      console.error('Error updating note:', error);
      toast.error('Failed to update note');
    }
  };

  // Delete note
  const handleDeleteNote = (noteId: string) => {
    try {
      const updatedNotes = safeNotes.filter(note => note.id !== noteId);
      onNotesChange(updatedNotes);
      
      // Clear editing state if deleting the note being edited
      if (editingNote.id === noteId) {
        setEditingNote({ id: null, content: '' });
        setIsCreating(false);
      }
      
      toast.success('Note Deleted', {
        description: 'Note has been permanently removed',
        duration: 3000
      });
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error('Failed to delete note');
    }
  };

  // Start editing note
  const handleStartEdit = (note: DealNote) => {
    setEditingNote({ id: note.id, content: note.content });
    setIsCreating(false);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingNote({ id: null, content: '' });
    setIsCreating(false);
  };

  // Start creating new note
  const handleStartCreate = () => {
    setEditingNote({ id: null, content: '' });
    setIsCreating(true);
  };

  // Cancel and close - following the established pattern
  const handleCancel = () => {
    setEditingNote({ id: null, content: '' });
    setIsCreating(false);
    onClose();
  };

  // Format date for display with error handling
  const formatDate = (dateString: string): string => {
    try {
      if (!dateString || typeof dateString !== 'string') {
        return 'Unknown date';
      }
      
      const date = new Date(dateString);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffInHours < 1) {
        const diffInMinutes = Math.floor(diffInHours * 60);
        return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`;
      } else if (diffInHours < 24) {
        return `${Math.floor(diffInHours)}h ago`;
      } else {
        return date.toLocaleDateString('en-CA', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'Unknown date';
    }
  };

  // Get step badge color with orange theme
  const getStepBadgeColor = (step: number): string => {
    const colors: Record<number, string> = {
      1: 'bg-blue-600 text-white border-blue-500',
      2: 'bg-purple-600 text-white border-purple-500', 
      3: 'bg-amber-600 text-white border-amber-500',
      4: 'bg-green-600 text-white border-green-500',
      5: 'bg-brand-orange text-white border-brand-orange'
    };
    return colors[step] || 'bg-neutral-60 text-white border-neutral-50';
  };

  // Don't render if not open
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden notes-panel-overlay">
      {/* Dark Backdrop with Opacity */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-md transition-all duration-300"
        onClick={handleCancel}
      />
      
      {/* Dark Panel with Orange Accents */}
      <div className="absolute right-0 top-0 h-full w-full max-w-2xl notes-panel-container">
        <div className="flex h-full flex-col">
          {/* Dark Header with Orange Accents */}
          <div className="notes-panel-header">
            <SliderHeader
              title="Deal Notes"
              subtitle={`Step-by-step documentation • Step ${safeCurrentStep}: ${getStepName(safeCurrentStep)}`}
              triggerIcon={StickyNote}
              triggerAction="Notes"
              onClose={handleCancel}
            />
          </div>

          {/* Current Step Summary with Orange Highlight */}
          <div className="notes-panel-summary">
            <div className="flex items-center space-x-4">
              <div className="p-3 rounded-xl bg-brand-orange/20 border border-brand-orange/30 notes-step-indicator">
                <Hash className="h-6 w-6 text-brand-orange" />
              </div>
              <div>
                <h3 className="font-semibold text-white text-base">
                  Currently on Step {safeCurrentStep}
                </h3>
                <div className="flex items-center space-x-4 text-sm text-neutral-300">
                  <span>{getStepName(safeCurrentStep)}</span>
                  <Badge variant="outline" className="text-xs border-brand-orange/30 text-brand-orange bg-brand-orange/10">
                    {safeNotes.length} total notes
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Actions Section with Orange CTA */}
          <div className="notes-panel-actions">
            {!isCreating && editingNote.id === null && (
              <Button
                onClick={handleStartCreate}
                disabled={disabled}
                className="w-full bg-brand-orange text-white hover:bg-brand-orange-dark border border-brand-orange/30 shadow-lg notes-cta-button"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Note to Current Step
              </Button>
            )}

            {/* New Note Creation with Dark Theme */}
            {isCreating && (
              <div className="space-y-4 notes-create-section">
                <div className="flex items-center space-x-2">
                  <Badge className={cn("text-xs border", getStepBadgeColor(safeCurrentStep))}>
                    Step {safeCurrentStep}
                  </Badge>
                  <span className="text-sm font-medium text-white">New Note</span>
                </div>
                
                <Textarea
                  placeholder={`Add a note for ${getStepName(safeCurrentStep)}...`}
                  value={editingNote.content}
                  onChange={(e) => setEditingNote({ ...editingNote, content: e.target.value })}
                  className="min-h-[120px] notes-textarea"
                  disabled={disabled}
                />
                
                <div className="flex space-x-3">
                  <Button
                    onClick={handleCreateNote}
                    disabled={disabled || editingNote.content.trim() === ''}
                    size="sm"
                    className="bg-brand-orange text-white hover:bg-brand-orange-dark notes-save-button"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Note
                  </Button>
                  <Button
                    onClick={handleCancelEdit}
                    disabled={disabled}
                    variant="outline"
                    size="sm"
                    className="notes-cancel-button"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {/* Selection indicator with Orange Accent */}
            {safeNotes.length > 0 && (
              <div className="notes-indicator">
                <p className="text-sm font-medium text-brand-orange">
                  {safeNotes.length} note{safeNotes.length === 1 ? '' : 's'} created across all steps
                </p>
              </div>
            )}
          </div>

          {/* Notes List with Dark Theme */}
          <div className="flex-1 overflow-y-auto notes-panel-content">
            <div className="p-6 space-y-3">
              {sortedNotes.length === 0 ? (
                <div className="text-center py-12 notes-empty-state">
                  <FileText className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
                  <h3 className="font-medium mb-2 text-white">No notes yet</h3>
                  <p className="text-sm text-neutral-400">
                    Start documenting your deal process by adding notes at each step.
                  </p>
                </div>
              ) : (
                sortedNotes.map((note) => (
                  <div
                    key={note.id}
                    className={cn(
                      "notes-item",
                      editingNote.id === note.id
                        ? "notes-item-editing"
                        : "notes-item-default"
                    )}
                  >
                    {/* Step Badge */}
                    <div className="flex-shrink-0">
                      <Badge className={cn("text-xs border", getStepBadgeColor(note.step || 1))}>
                        Step {note.step || 1}
                      </Badge>
                    </div>

                    {/* Note Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-white text-sm">
                          {note.stepName || getStepName(note.step)}
                        </h4>
                        
                        {editingNote.id !== note.id && (
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleStartEdit(note)}
                              disabled={disabled}
                              className="h-8 w-8 p-0 text-neutral-400 hover:text-white hover:bg-neutral-700 notes-action-button"
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteNote(note.id)}
                              disabled={disabled}
                              className="h-8 w-8 p-0 text-neutral-400 hover:text-red-400 hover:bg-neutral-700 notes-action-button"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 text-xs text-neutral-500 mb-3">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(note.createdAt)}</span>
                        {note.updatedAt && note.updatedAt !== note.createdAt && (
                          <>
                            <span>•</span>
                            <span>Edited {formatDate(note.updatedAt)}</span>
                          </>
                        )}
                      </div>

                      {editingNote.id === note.id ? (
                        <div className="space-y-3">
                          <Textarea
                            value={editingNote.content}
                            onChange={(e) => setEditingNote({ ...editingNote, content: e.target.value })}
                            className="min-h-[80px] notes-textarea"
                            disabled={disabled}
                          />
                          <div className="flex space-x-2">
                            <Button
                              onClick={() => handleUpdateNote(note.id)}
                              disabled={disabled || editingNote.content.trim() === ''}
                              size="sm"
                              className="bg-brand-orange text-white hover:bg-brand-orange-dark notes-save-button"
                            >
                              <Save className="h-4 w-4 mr-1" />
                              Update
                            </Button>
                            <Button
                              onClick={handleCancelEdit}
                              disabled={disabled}
                              variant="outline"
                              size="sm"
                              className="notes-cancel-button"
                            >
                              <RotateCcw className="h-4 w-4 mr-1" />
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-neutral-200 whitespace-pre-wrap leading-relaxed">
                          {note.content}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Footer with Dark Theme */}
          <div className="notes-panel-footer">
            <div className="flex items-center justify-between">
              <p className="text-sm text-neutral-400">
                {safeNotes.length} of {safeNotes.length} notes across all steps
              </p>
              <div className="flex space-x-3">
                <Button 
                  variant="outline" 
                  onClick={handleCancel}
                  className="notes-close-button"
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NotesSlider;