import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { Checkbox } from './ui/checkbox';
import { Search, Users, Building, Star, CreditCard, Phone, Mail } from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  photo: string;
  email: string;
  phone: string;
  city: string;
  province: string;
  customerType: string;
  status: string;
  creditRating: string;
  vehicleInterests: string[];
  lifetimeValue: string;
  salesRep: string;
}

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  price: string;
}

interface CustomerMatchingModalProps {
  isOpen: boolean;
  onClose: () => void;
  vehicle: Vehicle | null;
  customers: Customer[];
  onSaveMatches: (vehicleId: number, selectedCustomerIds: string[]) => void;
}

export function CustomerMatchingModal({ 
  isOpen, 
  onClose, 
  vehicle, 
  customers, 
  onSaveMatches 
}: CustomerMatchingModalProps) {
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  if (!vehicle) return null;

  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.vehicleInterests.some(interest => 
      interest.toLowerCase().includes(vehicle.make.toLowerCase()) ||
      interest.toLowerCase().includes('sedan') ||
      interest.toLowerCase().includes('suv') ||
      interest.toLowerCase().includes('truck')
    )
  );

  const handleCustomerToggle = (customerId: string) => {
    setSelectedCustomers(prev => 
      prev.includes(customerId) 
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    );
  };

  const handleSave = () => {
    onSaveMatches(vehicle.id, selectedCustomers);
    setSelectedCustomers([]);
    setSearchQuery('');
    onClose();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'prospect': return 'bg-blue-100 text-blue-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'vip': return 'bg-purple-100 text-purple-800';
      case 'corporate': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCreditColor = (rating: string) => {
    switch (rating) {
      case 'A+': return 'text-green-400';
      case 'A': return 'text-green-300';
      case 'A-': return 'text-green-200';
      case 'B+': return 'text-yellow-400';
      case 'B': return 'text-yellow-300';
      default: return 'text-neutral-400';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col bg-neutral-10 text-neutral-95 border-neutral-variant-30 dark">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-neutral-95">
            <Users className="h-5 w-5 text-primary-60" />
            <span>Match Customers to Vehicle</span>
          </DialogTitle>
          <DialogDescription className="text-neutral-70">
            Select customers who might be interested in the {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.price}
          </DialogDescription>
        </DialogHeader>

        {/* Search and filters */}
        <div className="flex items-center space-x-4 py-4 border-b border-neutral-variant-30">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-neutral-60" />
            <Input 
              placeholder="Search customers by name, email, or vehicle interests..." 
              className="pl-10 bg-neutral-20 border-neutral-variant-30 text-neutral-95 placeholder:text-neutral-60"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Badge variant="outline" className="flex items-center space-x-1 border-neutral-variant-40 text-neutral-80">
            <Users className="h-3 w-3" />
            <span>{selectedCustomers.length} selected</span>
          </Badge>
        </div>

        {/* Customer list */}
        <div className="flex-1 overflow-y-auto">
          <div className="space-y-2">
            {filteredCustomers.map((customer) => (
              <div 
                key={customer.id} 
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors cursor-pointer hover:bg-neutral-20 ${
                  selectedCustomers.includes(customer.id) 
                    ? 'bg-primary-10 border-primary-40' 
                    : 'border-neutral-variant-30 hover:border-neutral-variant-40'
                }`}
                onClick={() => handleCustomerToggle(customer.id)}
              >
                <Checkbox 
                  checked={selectedCustomers.includes(customer.id)}
                  onCheckedChange={() => handleCustomerToggle(customer.id)}
                  className="border-neutral-variant-50 data-[state=checked]:bg-primary-60 data-[state=checked]:border-primary-60"
                />
                
                <Avatar className="h-12 w-12">
                  {customer.customerType === 'Business' ? (
                    <AvatarImage src={customer.photo} alt={customer.name} />
                  ) : (
                    <ImageWithFallback 
                      src={customer.photo} 
                      alt={customer.name}
                      className="h-full w-full object-cover rounded-full"
                    />
                  )}
                  <AvatarFallback className="text-xs bg-neutral-30 text-neutral-90">
                    {customer.customerType === 'Business' ? (
                      <Building className="h-4 w-4" />
                    ) : (
                      customer.name.split(' ').map(n => n[0]).join('')
                    )}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-neutral-95">{customer.name}</p>
                      <div className="flex items-center space-x-2 text-xs text-neutral-60">
                        <div className="flex items-center space-x-1">
                          <Mail className="h-3 w-3" />
                          <span>{customer.email}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Phone className="h-3 w-3" />
                          <span>{customer.phone}</span>
                        </div>
                      </div>
                      <p className="text-xs text-neutral-60">{customer.city}, {customer.province}</p>
                    </div>
                    
                    <div className="text-right space-y-1">
                      <div className="flex space-x-1">
                        <Badge className={getStatusColor(customer.status)} variant="secondary">
                          {customer.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs border-neutral-variant-40 text-neutral-80">
                          {customer.customerType}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-end space-x-2">
                        <div className="flex items-center space-x-1">
                          <CreditCard className="h-3 w-3 text-neutral-60" />
                          <span className={`text-xs font-medium ${getCreditColor(customer.creditRating)}`}>
                            {customer.creditRating}
                          </span>
                        </div>
                        <span className="text-xs font-medium text-neutral-95">{customer.lifetimeValue}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-2">
                    <div className="flex flex-wrap gap-1">
                      {customer.vehicleInterests.map((interest, index) => (
                        <Badge 
                          key={index} 
                          variant="outline" 
                          className="text-xs bg-secondary-30 text-secondary-90 border-secondary-60"
                        >
                          {interest}
                        </Badge>
                      ))}
                    </div>
                    <p className="text-xs text-neutral-60 mt-1">Sales Rep: {customer.salesRep}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-neutral-variant-30">
          <p className="text-sm text-neutral-60">
            {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''} selected for sales follow-up
          </p>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={onClose}
              className="border-neutral-variant-40 text-neutral-95 hover:bg-neutral-20"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSave}
              className="bg-primary-60 text-primary-20 hover:bg-primary-70"
              disabled={selectedCustomers.length === 0}
            >
              Create {selectedCustomers.length} Opportunit{selectedCustomers.length !== 1 ? 'ies' : 'y'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}