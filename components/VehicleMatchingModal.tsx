import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogDescription } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { Checkbox } from './ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Search, Car, MapPin, Clock, Filter, Image as ImageIcon } from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  photo: string;
  email: string;
  phone: string;
  city: string;
  province: string;
  customerType: string;
  status: string;
  creditRating: string;
  vehicleInterests: string[];
  lifetimeValue: string;
  salesRep: string;
}

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  price: string;
  status: string;
  location: string;
  vin: string;
  mileage: string;
  exterior: string;
  interior: string;
  transmission: string;
  fuelType: string;
  daysOnLot: number;
  cost: string;
  photos: string[];
  features: string[];
}

interface VehicleMatchingModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
  vehicles: Vehicle[];
  onSaveMatches: (customerId: string, selectedVehicleIds: number[]) => void;
}

export function VehicleMatchingModal({ 
  isOpen, 
  onClose, 
  customer, 
  vehicles, 
  onSaveMatches 
}: VehicleMatchingModalProps) {
  const [selectedVehicles, setSelectedVehicles] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [locationFilter, setLocationFilter] = useState('all');

  if (!customer) return null;

  // Filter vehicles based on search and filters
  const filteredVehicles = vehicles.filter(vehicle => {
    const matchesSearch = 
      vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.vin.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${vehicle.year}`.includes(searchQuery);

    const matchesStatus = statusFilter === 'all' || vehicle.status.toLowerCase() === statusFilter;
    const matchesLocation = locationFilter === 'all' || vehicle.location === locationFilter;
    
    // Filter out sold vehicles
    const isAvailable = vehicle.status.toLowerCase() !== 'sold';

    // Smart matching based on customer interests
    const matchesInterests = customer.vehicleInterests.length === 0 || 
      customer.vehicleInterests.some(interest => 
        vehicle.make.toLowerCase().includes(interest.toLowerCase()) ||
        interest.toLowerCase().includes('sedan') && (vehicle.model.toLowerCase().includes('camry') || vehicle.model.toLowerCase().includes('civic') || vehicle.model.toLowerCase().includes('altima')) ||
        interest.toLowerCase().includes('suv') && (vehicle.model.toLowerCase().includes('equinox') || vehicle.model.toLowerCase().includes('crv') || vehicle.model.toLowerCase().includes('rav4')) ||
        interest.toLowerCase().includes('truck') && (vehicle.model.toLowerCase().includes('f-150') || vehicle.model.toLowerCase().includes('silverado') || vehicle.model.toLowerCase().includes('ram')) ||
        interest.toLowerCase().includes('hybrid') && vehicle.fuelType.toLowerCase().includes('hybrid') ||
        interest.toLowerCase().includes('electric') && vehicle.fuelType.toLowerCase().includes('electric')
      );

    return matchesSearch && matchesStatus && matchesLocation && isAvailable && matchesInterests;
  });

  const handleVehicleToggle = (vehicleId: number) => {
    setSelectedVehicles(prev => 
      prev.includes(vehicleId) 
        ? prev.filter(id => id !== vehicleId)
        : [...prev, vehicleId]
    );
  };

  const handleSave = () => {
    onSaveMatches(customer.id, selectedVehicles);
    setSelectedVehicles([]);
    setSearchQuery('');
    setStatusFilter('all');
    setLocationFilter('all');
    onClose();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'reserved': return 'bg-yellow-100 text-yellow-800';
      case 'sold': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDaysOnLotColor = (days: number) => {
    if (days <= 14) return 'text-green-400';
    if (days <= 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  const locations = [...new Set(vehicles.map(v => v.location))];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden flex flex-col bg-neutral-10 text-neutral-95 border-neutral-variant-30 dark">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-neutral-95">
            <Car className="h-5 w-5 text-primary-60" />
            <span>Match Vehicles to Customer</span>
          </DialogTitle>
          <DialogDescription className="text-neutral-70">
            Select vehicle options for {customer.name} - {customer.email}
          </DialogDescription>
          {customer.vehicleInterests.length > 0 && (
            <div className="flex items-center space-x-2 mt-2">
              <span className="text-xs text-neutral-60">Customer Interests:</span>
              {customer.vehicleInterests.map((interest, index) => (
                <Badge key={index} variant="outline" className="text-xs border-neutral-variant-40 text-neutral-80">
                  {interest}
                </Badge>
              ))}
            </div>
          )}
        </DialogHeader>

        {/* Search and filters */}
        <div className="flex items-center space-x-4 py-4 border-b border-neutral-variant-30">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-neutral-60" />
            <Input 
              placeholder="Search by make, model, VIN, or year..." 
              className="pl-10 bg-neutral-20 border-neutral-variant-30 text-neutral-95 placeholder:text-neutral-60"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40 bg-neutral-20 border-neutral-variant-30 text-neutral-95">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent className="bg-neutral-10 border-neutral-variant-30">
              <SelectItem value="all" className="text-neutral-95 focus:bg-neutral-20">All Status</SelectItem>
              <SelectItem value="available" className="text-neutral-95 focus:bg-neutral-20">Available</SelectItem>
              <SelectItem value="reserved" className="text-neutral-95 focus:bg-neutral-20">Reserved</SelectItem>
            </SelectContent>
          </Select>

          <Select value={locationFilter} onValueChange={setLocationFilter}>
            <SelectTrigger className="w-48 bg-neutral-20 border-neutral-variant-30 text-neutral-95">
              <SelectValue placeholder="Location" />
            </SelectTrigger>
            <SelectContent className="bg-neutral-10 border-neutral-variant-30">
              <SelectItem value="all" className="text-neutral-95 focus:bg-neutral-20">All Locations</SelectItem>
              {locations.map((location) => (
                <SelectItem key={location} value={location} className="text-neutral-95 focus:bg-neutral-20">
                  {location}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Badge variant="outline" className="flex items-center space-x-1 border-neutral-variant-40 text-neutral-80">
            <Car className="h-3 w-3" />
            <span>{selectedVehicles.length} selected</span>
          </Badge>
        </div>

        {/* Vehicle grid */}
        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredVehicles.map((vehicle) => (
              <div 
                key={vehicle.id} 
                className={`border rounded-lg p-4 transition-all cursor-pointer hover:shadow-md ${
                  selectedVehicles.includes(vehicle.id) 
                    ? 'border-primary-60 bg-primary-10 shadow-md' 
                    : 'border-neutral-variant-30 hover:border-neutral-variant-40 hover:bg-neutral-20'
                }`}
                onClick={() => handleVehicleToggle(vehicle.id)}
              >
                <div className="flex items-start space-x-3">
                  <Checkbox 
                    checked={selectedVehicles.includes(vehicle.id)}
                    onCheckedChange={() => handleVehicleToggle(vehicle.id)}
                    className="border-neutral-variant-50 data-[state=checked]:bg-primary-60 data-[state=checked]:border-primary-60"
                  />
                  
                  <div className="flex-1 min-w-0">
                    {/* Vehicle Image */}
                    <div className="relative mb-3">
                      <ImageWithFallback
                        src={vehicle.photos[0]}
                        alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <div className="absolute top-2 right-2 flex space-x-1">
                        <Badge variant="outline" className="text-xs bg-neutral-10 border-neutral-variant-40 text-neutral-95">
                          <ImageIcon className="h-2 w-2 mr-1" />
                          {vehicle.photos.length}
                        </Badge>
                        <Badge className={getStatusColor(vehicle.status)}>
                          {vehicle.status}
                        </Badge>
                      </div>
                    </div>

                    {/* Vehicle Details */}
                    <div className="space-y-2">
                      <div>
                        <h4 className="font-medium text-sm text-neutral-95">{vehicle.year} {vehicle.make} {vehicle.model}</h4>
                        <p className="text-xs text-neutral-60 font-mono">VIN: {vehicle.vin}</p>
                      </div>

                      <div className="flex items-center justify-between">
                        <p className="font-semibold text-sm text-primary-60">
                          {vehicle.price}
                        </p>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3 text-neutral-60" />
                          <span className={`text-xs ${getDaysOnLotColor(vehicle.daysOnLot)}`}>
                            {vehicle.daysOnLot}d
                          </span>
                        </div>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-3 w-3 text-neutral-60" />
                          <span className="text-xs text-neutral-60">{vehicle.location}</span>
                        </div>
                        <p className="text-xs text-neutral-60">
                          {vehicle.mileage} • {vehicle.exterior} • {vehicle.fuelType}
                        </p>
                      </div>

                      {/* Features */}
                      <div className="flex flex-wrap gap-1">
                        {vehicle.features.slice(0, 3).map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-neutral-variant-40 text-neutral-80">
                            {feature}
                          </Badge>
                        ))}
                        {vehicle.features.length > 3 && (
                          <Badge variant="outline" className="text-xs border-neutral-variant-40 text-neutral-80">
                            +{vehicle.features.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredVehicles.length === 0 && (
            <div className="text-center py-12">
              <Car className="h-12 w-12 text-neutral-60 mx-auto mb-4" />
              <h3 className="font-medium mb-2 text-neutral-95">No vehicles found</h3>
              <p className="text-sm text-neutral-60">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-neutral-variant-30">
          <p className="text-sm text-neutral-60">
            {selectedVehicles.length} vehicle{selectedVehicles.length !== 1 ? 's' : ''} selected as options for {customer.name}
          </p>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={onClose}
              className="border-neutral-variant-40 text-neutral-95 hover:bg-neutral-20"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSave}
              className="bg-primary-60 text-primary-20 hover:bg-primary-70"
              disabled={selectedVehicles.length === 0}
            >
              Create Deal with {selectedVehicles.length} Option{selectedVehicles.length !== 1 ? 's' : ''}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}