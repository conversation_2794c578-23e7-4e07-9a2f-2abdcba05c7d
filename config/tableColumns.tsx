import React from 'react';
import { Eye, Edit, Trash2, Users, Car, Clock, Phone, Mail, CreditCard, ExternalLink } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { Tooltip, TooltipContent, TooltipTrigger } from '../components/ui/tooltip';
import { ImageWithFallback } from '../components/figma/ImageWithFallback';
import { TableColumn } from '../components/ui/material/table';
import { Vehicle, Customer, RecentSale } from '../types';
import { getStatusColor, getCreditColor, getDaysOnLotColor } from '../utils/helpers';
import { toast } from 'sonner@2.0.3';

/**
 * Table Column Configurations for SubPrime Pro - Enhanced Compact Design
 * 
 * UPDATED FOR COMPACT INVENTORY & ENHANCED ACCESSIBILITY:
 * - Compact inventory rows matching deals and customer grids
 * - Larger action icons (h-5 w-5) for better accessibility
 * - Increased spacing between action buttons (space-x-2)
 * - Reorganized inventory columns for optimal space utilization
 * - Consistent tooltip integration across all table grids
 * 
 * Comprehensive table column definitions for all data tables in the application.
 * Includes rendering functions, sorting, filtering, and action handlers.
 */

interface TableColumnHandlers {
  handleVehicleClick: (vehicle: Vehicle) => void;
  handleMatchCustomers: (vehicle: Vehicle) => void;
  handleDeleteClick: (item: Customer | Vehicle, type: 'customer' | 'vehicle') => void;
  handleCustomerNameClick: (customer: Customer) => void;
  handleMatchVehicles: (customer: Customer) => void;
  getCustomerMatchCount: (vehicleId: number) => number;
  getVehicleMatchCount: (customerId: string) => number;
  getReservedCustomer: (vehicleId: number) => Customer | null;
}

export const createInventoryColumns = (handlers: TableColumnHandlers): TableColumn<Vehicle>[] => [
  // VEHICLE DETAILS - COMPACT DESIGN
  {
    id: 'vehicle',
    label: 'Vehicle Details',
    accessor: (vehicle) => `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
    sortable: true,
    filterable: true,
    width: 280,
    render: (_, vehicle) => (
      <div className="flex items-center space-x-3 min-w-[280px] py-1">
        <div className="relative flex-shrink-0">
          <ImageWithFallback
            src={vehicle.photos[0]}
            alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            className="w-12 h-12 object-cover rounded-lg shadow-sm border border-outline-variant"
          />
          {vehicle.photos.length > 1 && (
            <div className="absolute -bottom-1 -right-1 bg-black/70 text-white text-xs px-1 py-0.5 rounded text-center min-w-[14px] h-4 flex items-center justify-center">
              +{vehicle.photos.length - 1}
            </div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-on-surface text-sm leading-tight cursor-pointer hover:text-primary-50 transition-colors"
              onClick={() => handlers.handleVehicleClick(vehicle)}>
            {vehicle.year} {vehicle.make} {vehicle.model}
          </h4>
          <div className="flex items-center space-x-1 text-xs text-on-surface-variant mt-0.5">
            <span className="font-mono">{vehicle.vin.slice(-6)}</span>
            <span>•</span>
            <span>{vehicle.mileage}</span>
            <span>•</span>
            <span className="truncate">{vehicle.exterior}</span>
          </div>
        </div>
      </div>
    )
  },

  // PRICE & COST - COMPACT
  {
    id: 'price',
    label: 'Price & Cost',
    accessor: 'price',
    sortable: true,
    filterable: true,
    alignment: 'right',
    width: 120,
    render: (_, vehicle) => (
      <div className="text-right min-w-[120px] py-1">
        <p className="font-semibold text-primary-50 text-sm">{vehicle.price}</p>
        <p className="text-xs text-on-surface-variant">Cost: {vehicle.cost}</p>
      </div>
    )
  },

  // LOCATION ONLY - STATUS REMOVED AS NOT FETCHED BY SYSTEM
  {
    id: 'location',
    label: 'Location',
    accessor: 'location',
    sortable: true,
    filterable: true,
    width: 120,
    render: (_, vehicle) => (
      <div className="min-w-[120px] py-1">
        <p className="font-medium text-brand-orange text-sm">{vehicle.location}</p>
      </div>
    )
  },

  // DAYS ON LOT - COMPACT
  {
    id: 'daysOnLot',
    label: 'Days on Lot',
    accessor: 'daysOnLot',
    sortable: true,
    alignment: 'center',
    width: 110,
    render: (_, vehicle) => (
      <div className="flex items-center justify-center space-x-1.5 min-w-[110px] py-1">
        <Clock className="h-3.5 w-3.5 text-on-surface-variant" />
        <span className={`font-medium text-sm ${getDaysOnLotColor(vehicle.daysOnLot)}`}>
          {vehicle.daysOnLot}d
        </span>
      </div>
    )
  },

  // ACTIONS - ENHANCED ACCESSIBILITY
  {
    id: 'actions',
    label: 'Actions',
    accessor: () => '',
    sortable: false,
    filterable: false,
    width: 140,
    render: (_, vehicle) => (
      <div className="flex items-center space-x-2 min-w-[140px]">
        {/* View Vehicle Details */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handlers.handleVehicleClick(vehicle);
              }}
              className="h-8 w-8 p-0 hover:bg-primary-95"
              aria-label={`View details for ${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            >
              <Eye className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>View vehicle details</p>
          </TooltipContent>
        </Tooltip>

        {/* Match Customers */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handlers.handleMatchCustomers(vehicle);
              }}
              className="h-8 w-8 p-0 relative hover:bg-primary-95"
              aria-label={`Match customers to ${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            >
              <Users className="h-5 w-5" />
              {handlers.getCustomerMatchCount(vehicle.id) > 0 && (
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-primary-60 rounded-full border border-white flex items-center justify-center">
                  <span className="text-xs font-bold text-white">
                    {handlers.getCustomerMatchCount(vehicle.id)}
                  </span>
                </div>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>Match customers</p>
          </TooltipContent>
        </Tooltip>

        {/* Delete Vehicle */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handlers.handleDeleteClick(vehicle, 'vehicle');
              }}
              className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-red-50"
              aria-label={`Delete ${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>Delete vehicle</p>
          </TooltipContent>
        </Tooltip>
      </div>
    )
  }
];

export const createCustomerColumns = (handlers: TableColumnHandlers): TableColumn<Customer>[] => [
  // CUSTOMER DETAILS - ALREADY COMPACT
  {
    id: 'customer',
    label: 'Customer Details',
    accessor: 'name',
    sortable: true,
    filterable: true,
    width: '300px',
    render: (_, customer) => (
      <div className="flex items-center space-x-4">
        <Avatar className="h-12 w-12 border border-outline-variant">
          <AvatarImage src={customer.photo} alt={customer.name} />
          <AvatarFallback>
            {customer.name.split(' ').map(n => n[0]).join('')}
          </AvatarFallback>
        </Avatar>
        <div>
          <h4 className="font-semibold text-on-surface hover:text-primary cursor-pointer">
            {customer.name}
          </h4>
          <div className="flex items-center space-x-2 mt-1">
            <Badge variant="outline" className={`text-xs ${
              customer.customerType === 'Individual' 
                ? 'border-primary-50 text-primary-50' 
                : 'border-secondary-50 text-secondary-50'
            }`}>
              {customer.customerType}
            </Badge>
            <span className="text-xs text-on-surface-variant">ID: {customer.id}</span>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'contact',
    label: 'Contact Info',
    accessor: 'email',
    sortable: true,
    filterable: true,
    render: (_, customer) => (
      <div className="space-y-1">
        <div className="flex items-center space-x-2 text-sm">
          <Mail className="h-3 w-3 text-on-surface-variant" />
          <span className="text-on-surface-variant">{customer.email}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <Phone className="h-3 w-3 text-on-surface-variant" />
          <span className="text-on-surface-variant">{customer.phone}</span>
        </div>
      </div>
    )
  },
  {
    id: 'status',
    label: 'Status',
    accessor: 'status',
    sortable: true,
    filterable: true,
    render: (_, customer) => (
      <div className="space-y-2">
        <Badge variant="outline" className={getStatusColor(customer.status)}>
          {customer.status}
        </Badge>
        <div className="flex items-center space-x-1">
          <CreditCard className="h-3 w-3 text-on-surface-variant" />
          <span className={`text-xs font-medium ${getCreditColor(customer.creditRating)}`}>
            {customer.creditRating}
          </span>
        </div>
      </div>
    )
  },
  {
    id: 'location',
    label: 'Location',
    accessor: 'assignedDealer',
    sortable: true,
    filterable: true,
    render: (_, customer) => (
      <div className="space-y-1">
        <p className="font-medium text-brand-orange">{customer.assignedDealer}</p>
        <p className="text-xs text-on-surface-variant">
          {customer.city}, {customer.province}
        </p>
      </div>
    )
  },
  {
    id: 'salesRep',
    label: 'Sales Rep',
    accessor: 'salesRep',
    sortable: true,
    filterable: true,
    render: (_, customer) => (
      <div className="space-y-1">
        <p className="font-medium text-on-surface">{customer.salesRep}</p>
        <p className="text-xs text-on-surface-variant">
          Last contact: {customer.lastContact}
        </p>
      </div>
    )
  },
  {
    id: 'ltv',
    label: 'LTV',
    accessor: 'lifetimeValue',
    sortable: true,
    alignment: 'right',
    render: (_, customer) => (
      <div className="text-right space-y-1">
        <p className="font-bold text-lg text-primary">{customer.lifetimeValue}</p>
        <p className="text-xs text-on-surface-variant">
          {customer.totalPurchases} purchase{customer.totalPurchases === 1 ? '' : 's'}
        </p>
      </div>
    )
  },

  // ACTIONS - ENHANCED ACCESSIBILITY
  {
    id: 'actions',
    label: 'Actions',
    accessor: () => '',
    sortable: false,
    filterable: false,
    width: '140px',
    render: (_, customer) => (
      <div className="flex items-center space-x-2">
        {/* View Customer Details */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handlers.handleCustomerNameClick(customer);
              }}
              className="h-8 w-8 p-0 hover:bg-primary-95"
              aria-label={`View details for ${customer.name}`}
            >
              <Eye className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>View customer details</p>
          </TooltipContent>
        </Tooltip>

        {/* Match Vehicles */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handlers.handleMatchVehicles(customer);
              }}
              className="h-8 w-8 p-0 relative hover:bg-primary-95"
              aria-label={`Match vehicles to ${customer.name}`}
            >
              <Car className="h-5 w-5" />
              {handlers.getVehicleMatchCount(customer.id) > 0 && (
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-primary-60 rounded-full border border-white flex items-center justify-center">
                  <span className="text-xs font-bold text-white">
                    {handlers.getVehicleMatchCount(customer.id)}
                  </span>
                </div>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>Match vehicles</p>
          </TooltipContent>
        </Tooltip>

        {/* Delete Customer */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handlers.handleDeleteClick(customer, 'customer');
              }}
              className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-red-50"
              aria-label={`Delete ${customer.name}`}
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>Delete customer</p>
          </TooltipContent>
        </Tooltip>
      </div>
    )
  }
];

export const createRecentSalesColumns = (): TableColumn<RecentSale>[] => [
  {
    id: 'customer',
    label: 'Customer',
    accessor: 'customer',
    sortable: true,
    filterable: true,
    render: (_, sale) => (
      <div>
        <p className="font-semibold text-on-surface">{sale.customer}</p>
        <p className="text-xs text-on-surface-variant">Sales Rep: {sale.salesperson}</p>
      </div>
    )
  },
  {
    id: 'vehicle',
    label: 'Vehicle',
    accessor: 'vehicle',
    sortable: true,
    filterable: true,
    render: (_, sale) => (
      <p className="font-medium text-on-surface">{sale.vehicle}</p>
    )
  },
  {
    id: 'amount',
    label: 'Amount',
    accessor: 'amount',
    sortable: true,
    alignment: 'right',
    render: (_, sale) => (
      <p className="font-bold text-primary">{sale.amount}</p>
    )
  },
  {
    id: 'date',
    label: 'Date',
    accessor: 'date',
    sortable: true,
    render: (_, sale) => (
      <p className="text-on-surface">{sale.date}</p>
    )
  },
  {
    id: 'location',
    label: 'Location',
    accessor: 'location',
    sortable: true,
    filterable: true,
    render: (_, sale) => (
      <p className="font-medium text-brand-orange">{sale.location}</p>
    )
  },

  // ACTIONS - ENHANCED ACCESSIBILITY
  {
    id: 'actions',
    label: '',
    accessor: () => '',
    sortable: false,
    filterable: false,
    width: '60px',
    render: (_, sale) => (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              toast.info(`View details for sale #${sale.id}`);
            }}
            className="h-8 w-8 p-0 hover:bg-primary-95"
            aria-label={`View details for sale #${sale.id}`}
          >
            <ExternalLink className="h-5 w-5" />
          </Button>
        </TooltipTrigger>
        <TooltipContent side="top">
          <p>View sale details</p>
        </TooltipContent>
      </Tooltip>
    )
  }
];