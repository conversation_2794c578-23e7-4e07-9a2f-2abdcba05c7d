/**
 * Deal Table Columns Configuration - Enhanced Material Design 3 Implementation
 * 
 * Comprehensive table column definitions for the deals management system,
 * showcasing multi-approval entity details, vehicle information, financial summaries,
 * and status tracking with proper Canadian auto finance context.
 * 
 * COMPACT DESIGN UPDATE:
 * - Reduced padding and spacing for more efficient use of vertical space
 * - Optimized avatar sizes and element proportions
 * - Maintained readability while increasing information density
 * - Enhanced visual hierarchy with improved typography and spacing
 * 
 * TOOLTIP ENHANCEMENT:
 * - Added comprehensive tooltips to all action buttons in the deals grid
 * - Contextual tooltip content that matches action functionality
 * - Leverages global dark tooltip theme for consistent user experience
 * - Enhanced accessibility and usability for deal management actions
 * 
 * ACCESSIBILITY IMPROVEMENTS:
 * - Enhanced action icon sizes from h-3.5 w-3.5 to h-5 w-5 for better visibility
 * - Increased spacing between action buttons from space-x-0.5 to space-x-2
 * - Improved touch targets and visual hierarchy for action elements
 * 
 * Features:
 * - Multi-approval entity summary display
 * - Vehicle details with images and status
 * - Financial information (amounts, payments, rates)
 * - Deal status and priority indicators
 * - Timeline tracking and contact information
 * - Interactive elements for deal management with enhanced tooltips
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> | Built by Figma Make - Claude Sonnet
 */

import { TableColumn } from '../components/ui/material/table';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { Tooltip, TooltipContent, TooltipTrigger } from '../components/ui/tooltip';
import { ImageWithFallback } from '../components/figma/ImageWithFallback';
import { Deal } from '../types';
import { 
  Eye, Edit, Trash2, Users, Car, DollarSign, Calendar, 
  Phone, Mail, MapPin, CreditCard, Building, User, 
  FileText, Clock, TrendingUp, AlertTriangle
} from 'lucide-react';

interface DealTableHandlers {
  handleDealClick: (deal: Deal) => void;
  handleEditDeal: (deal: Deal) => void;
  handleDeleteDeal: (deal: Deal) => void;
  handleContactCustomer: (deal: Deal) => void;
}

export const createDealColumns = (handlers: DealTableHandlers): TableColumn<Deal>[] => [
  // DEAL ID & CUSTOMER INFORMATION - COMPACT
  {
    id: 'dealInfo',
    label: 'Deal Information',
    accessor: 'customerName',
    sortable: true,
    filterable: true,
    width: 260,
    render: (value, deal) => (
      <div className="flex items-center space-x-2.5 min-w-[260px] py-1">
        <Avatar className="h-10 w-10 flex-shrink-0">
          <AvatarImage src={deal.customerPhoto} alt={deal.customerName} />
          <AvatarFallback className="bg-primary-95 text-primary-30">
            {deal.customerType === 'Business' ? (
              <Building className="h-4 w-4" />
            ) : (
              deal.customerName.split(' ').map(n => n[0]).join('')
            )}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1.5">
            <p className="font-semibold text-on-surface cursor-pointer hover:text-primary-50 transition-colors text-sm leading-tight"
               onClick={() => handlers.handleDealClick(deal)}>
              {deal.customerName}
            </p>
            <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-5">
              {deal.customerType === 'Business' ? (
                <Building className="h-2.5 w-2.5 mr-0.5" />
              ) : (
                <User className="h-2.5 w-2.5 mr-0.5" />
              )}
              {deal.customerType === 'Business' ? 'Biz' : 'Ind'}
            </Badge>
          </div>
          <div className="flex items-center space-x-1 text-xs text-on-surface-variant mt-0.5">
            <span className="font-mono font-medium text-primary-50">{deal.id}</span>
            <span>•</span>
            <Mail className="h-2.5 w-2.5" />
            <span className="truncate">{deal.customerEmail}</span>
          </div>
          <div className="flex items-center space-x-1 text-xs text-on-surface-variant">
            <Phone className="h-2.5 w-2.5" />
            <span>{deal.customerPhone}</span>
            <span>•</span>
            <MapPin className="h-2.5 w-2.5" />
            <span>{deal.customerLocation}</span>
          </div>
        </div>
      </div>
    )
  },

  // APPROVAL ENTITIES SUMMARY - COMPACT
  {
    id: 'approvalEntities',
    label: 'Approval Details',
    accessor: (deal) => deal.approvalEntities.length,
    sortable: true,
    filterable: false,
    width: 180,
    render: (value, deal) => {
      const entities = deal.approvalEntities;
      const primaryEntity = entities.find(e => e.type === 'Primary Applicant');
      const totalApproval = entities.reduce((sum, entity) => {
        const amount = parseInt(entity.approvalAmount.replace(/[^0-9]/g, ''));
        return sum + amount;
      }, 0);

      return (
        <div className="space-y-1.5 min-w-[180px] py-1">
          <div className="flex items-center space-x-1.5">
            <Users className="h-3.5 w-3.5 text-primary-50" />
            <span className="font-medium text-on-surface text-sm">
              {entities.length} {entities.length === 1 ? 'Entity' : 'Entities'}
            </span>
          </div>
          
          {primaryEntity && (
            <div className="space-y-0.5">
              <div className="flex items-center justify-between">
                <span className="text-xs text-on-surface-variant">Credit:</span>
                <span className={`text-xs font-medium ${
                  parseInt(primaryEntity.creditScore) >= 750 ? 'text-green-600' :
                  parseInt(primaryEntity.creditScore) >= 650 ? 'text-yellow-600' :
                  parseInt(primaryEntity.creditScore) >= 550 ? 'text-orange-600' :
                  'text-red-600'
                }`}>
                  {primaryEntity.creditScore}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-on-surface-variant">Approval:</span>
                <span className="text-xs font-semibold text-primary-50">
                  ${totalApproval.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-on-surface-variant">Rate:</span>
                <span className="text-xs text-on-surface">{primaryEntity.interestRate}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-on-surface-variant">Payment:</span>
                <span className="text-xs font-medium text-on-surface">{primaryEntity.monthlyPayment}</span>
              </div>
            </div>
          )}

          {/* Show entity types - compact */}
          <div className="flex flex-wrap gap-0.5">
            {entities.map((entity, index) => (
              <Badge 
                key={`entity-${index}`}
                variant="outline" 
                className={`text-xs px-1 py-0 h-4 ${
                  entity.type === 'Primary Applicant' ? 'border-primary-50 text-primary-50 bg-primary-95' :
                  entity.type === 'Co-Applicant' ? 'border-blue-500 text-blue-500 bg-blue-50' :
                  entity.type === 'Co-Signer' ? 'border-green-500 text-green-500 bg-green-50' :
                  'border-purple-500 text-purple-500 bg-purple-50'
                }`}
              >
                {entity.type === 'Primary Applicant' ? 'Pri' :
                 entity.type === 'Co-Applicant' ? 'Co-A' :
                 entity.type === 'Co-Signer' ? 'Co-S' : 'Guar'}
              </Badge>
            ))}
          </div>

          {/* Show bankruptcy warning if applicable */}
          {entities.some(e => e.bankruptcyHistory) && (
            <div className="flex items-center space-x-1 text-xs text-red-600">
              <AlertTriangle className="h-2.5 w-2.5" />
              <span>Bankruptcy</span>
            </div>
          )}
        </div>
      );
    }
  },

  // VEHICLE INFORMATION - COMPACT
  {
    id: 'vehicleOptions',
    label: 'Vehicle Details',
    accessor: (deal) => deal.vehicleOptions.length,
    sortable: true,
    filterable: false,
    width: 220,
    render: (value, deal) => {
      const selectedVehicle = deal.vehicleOptions.find(v => v.id === deal.selectedVehicle);
      const primaryVehicle = selectedVehicle || deal.vehicleOptions[0];

      if (!primaryVehicle) {
        return (
          <div className="flex items-center space-x-2 text-muted-foreground min-w-[180px] py-1">
            <Car className="h-3.5 w-3.5" />
            <span className="text-sm">No vehicle selected</span>
          </div>
        );
      }

      return (
        <div className="flex items-center space-x-2.5 min-w-[220px] py-1">
          <ImageWithFallback
            src={primaryVehicle.photos[0]}
            alt={primaryVehicle.details}
            className="w-10 h-10 rounded-lg object-cover border border-outline-variant flex-shrink-0"
          />
          <div className="flex-1 min-w-0">
            <p className="font-medium text-on-surface text-sm leading-tight">
              {primaryVehicle.details}
            </p>
            <div className="flex items-center space-x-1.5 mt-0.5">
              <span className="font-semibold text-primary-50 text-sm">
                {primaryVehicle.price}
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs px-1.5 py-0 h-4 ${
                  primaryVehicle.status === 'Available' ? 'border-green-500 text-green-500' :
                  primaryVehicle.status === 'Reserved' ? 'border-yellow-500 text-yellow-500' :
                  primaryVehicle.status === 'Sold' ? 'border-blue-500 text-blue-500' :
                  'border-gray-500 text-gray-500'
                }`}
              >
                {primaryVehicle.status}
              </Badge>
            </div>
            <div className="flex items-center space-x-1 text-xs text-on-surface-variant mt-0.5">
              <MapPin className="h-2.5 w-2.5" />
              <span className="truncate">{primaryVehicle.location}</span>
              <span>•</span>
              <span className="font-mono">{primaryVehicle.vin.slice(-6)}</span>
            </div>
            {deal.vehicleOptions.length > 1 && (
              <div className="text-xs text-primary-50 mt-0.5">
                +{deal.vehicleOptions.length - 1} more
              </div>
            )}
          </div>
        </div>
      );
    }
  },

  // DEAL STATUS & PRIORITY - COMPACT
  {
    id: 'status',
    label: 'Status',
    accessor: 'status',
    sortable: true,
    filterable: true,
    width: 100,
    render: (value, deal) => (
      <div className="space-y-1.5 min-w-[100px] py-1">
        <Badge 
          variant="outline"
          className={`px-2 py-0.5 font-medium text-xs h-5 ${
            deal.status === 'Finalized' ? 'border-green-500 text-green-500 bg-green-50' :
            deal.status === 'Pending' ? 'border-yellow-500 text-yellow-500 bg-yellow-50' :
            deal.status === 'Cancelled' ? 'border-red-500 text-red-500 bg-red-50' :
            'border-gray-500 text-gray-500 bg-gray-50'
          }`}
        >
          {deal.status}
        </Badge>
        <Badge 
          variant="outline"
          className={`px-1.5 py-0 text-xs h-4 ${
            deal.priority === 'High' ? 'border-red-400 text-red-600 bg-red-50' :
            deal.priority === 'Medium' ? 'border-yellow-400 text-yellow-600 bg-yellow-50' :
            'border-blue-400 text-blue-600 bg-blue-50'
          }`}
        >
          {deal.priority}
        </Badge>
      </div>
    )
  },

  // TIMELINE & SALES REP - COMPACT
  {
    id: 'timeline',
    label: 'Timeline & Rep',
    accessor: 'createdDate',
    sortable: true,
    filterable: false,
    width: 140,
    render: (value, deal) => {
      const createdDate = new Date(deal.createdDate);
      const lastContactDate = deal.lastContact ? new Date(deal.lastContact) : null;
      const daysSinceCreated = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
      const daysSinceContact = lastContactDate ? 
        Math.floor((Date.now() - lastContactDate.getTime()) / (1000 * 60 * 60 * 24)) : null;

      return (
        <div className="space-y-1.5 min-w-[140px] py-1">
          <div className="flex items-center space-x-1.5">
            <User className="h-3.5 w-3.5 text-primary-50" />
            <span className="font-medium text-on-surface text-sm">{deal.salesRep}</span>
          </div>
          
          <div className="space-y-0.5 text-xs">
            <div className="flex items-center space-x-1">
              <Calendar className="h-2.5 w-2.5 text-on-surface-variant" />
              <span className="text-on-surface">
                {createdDate.toLocaleDateString('en-CA')}
              </span>
            </div>
            <div className="text-on-surface-variant">
              {daysSinceCreated === 0 ? 'Today' : `${daysSinceCreated}d ago`}
            </div>
            
            {lastContactDate && (
              <>
                <div className="flex items-center space-x-1">
                  <Clock className="h-2.5 w-2.5 text-on-surface-variant" />
                  <span className="text-on-surface">
                    {lastContactDate.toLocaleDateString('en-CA')}
                  </span>
                </div>
                <div className={`text-xs ${
                  daysSinceContact === 0 ? 'text-green-600' :
                  daysSinceContact! <= 3 ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {daysSinceContact === 0 ? 'Today' : 
                   daysSinceContact === 1 ? 'Yesterday' :
                   `${daysSinceContact}d ago`}
                </div>
              </>
            )}
          </div>
        </div>
      );
    }
  },

  // ACTIONS - COMPACT WITH ENHANCED TOOLTIPS & ACCESSIBILITY
  {
    id: 'actions',
    label: 'Actions',
    accessor: 'id',
    sortable: false,
    filterable: false,
    width: 140,
    render: (value, deal) => (
      <div className="flex items-center space-x-2 min-w-[140px]">
        {/* View Deal Details Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handlers.handleDealClick(deal)}
              className="h-8 w-8 p-0 hover:bg-primary-95"
              aria-label={`View details for deal ${deal.id}`}
            >
              <Eye className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>View deal details</p>
          </TooltipContent>
        </Tooltip>
        
        {/* Edit Deal Button - Only show for active deals */}
        {deal.status !== 'Finalized' && deal.status !== 'Cancelled' && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handlers.handleEditDeal(deal)}
                className="h-8 w-8 p-0 hover:bg-primary-95"
                aria-label={`Edit deal ${deal.id}`}
              >
                <Edit className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Edit deal</p>
            </TooltipContent>
          </Tooltip>
        )}
        
        {/* Contact Customer Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handlers.handleContactCustomer(deal)}
              className="h-8 w-8 p-0 hover:bg-green-50 text-green-600"
              aria-label={`Contact ${deal.customerName}`}
            >
              <Phone className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>Contact {deal.customerName}</p>
          </TooltipContent>
        </Tooltip>
        
        {/* Delete Deal Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handlers.handleDeleteDeal(deal)}
              className="h-8 w-8 p-0 hover:bg-red-50 text-red-600"
              aria-label={`Delete deal ${deal.id}`}
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>Delete deal</p>
          </TooltipContent>
        </Tooltip>
      </div>
    )
  }
];

// Filter options for deal status
export const dealStatusFilters = [
  { label: 'All Deals', value: 'all' },
  { label: 'Pending', value: 'pending' },
  { label: 'Finalized', value: 'finalized' },
  { label: 'Cancelled', value: 'cancelled' }
];

// Filter options for deal priority
export const dealPriorityFilters = [
  { label: 'All Priorities', value: 'all' },
  { label: 'High Priority', value: 'high' },
  { label: 'Medium Priority', value: 'medium' },
  { label: 'Low Priority', value: 'low' }
];

// Export utility functions for deal management
export const getDealStatusColor = (status: Deal['status']) => {
  switch (status) {
    case 'Finalized':
      return 'border-green-500 text-green-500 bg-green-50';
    case 'Pending':
      return 'border-yellow-500 text-yellow-500 bg-yellow-50';
    case 'Cancelled':
      return 'border-red-500 text-red-500 bg-red-50';
    default:
      return 'border-gray-500 text-gray-500 bg-gray-50';
  }
};

export const getDealPriorityColor = (priority: Deal['priority']) => {
  switch (priority) {
    case 'High':
      return 'border-red-400 text-red-600 bg-red-50';
    case 'Medium':
      return 'border-yellow-400 text-yellow-600 bg-yellow-50';
    case 'Low':
      return 'border-blue-400 text-blue-600 bg-blue-50';
    default:
      return 'border-gray-400 text-gray-600 bg-gray-50';
  }
};