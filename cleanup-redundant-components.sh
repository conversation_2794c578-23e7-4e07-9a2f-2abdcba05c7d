#!/bin/bash

# SubPrime Pro - Component Cleanup Script
# Removes redundant and unused components for streamlined UX

echo "🧹 SubPrime Pro - Removing redundant components..."

# Remove unused form components (keeping only AddDealForm and AccordionDealForm)
if [ -f "components/AddCustomerForm.tsx" ]; then
  echo "Removing AddCustomerForm.tsx..."
  rm -f components/AddCustomerForm.tsx
fi

if [ -f "components/AddVehicleForm.tsx" ]; then
  echo "Removing AddVehicleForm.tsx..."
  rm -f components/AddVehicleForm.tsx
fi

if [ -f "components/HorizontalDealForm.tsx" ]; then
  echo "Removing HorizontalDealForm.tsx..."
  rm -f components/HorizontalDealForm.tsx
fi

# Remove unused dashboard components (deal builder focus)
if [ -f "components/DashboardContent.tsx" ]; then
  echo "Removing DashboardContent.tsx..."
  rm -f components/DashboardContent.tsx
fi

if [ -f "components/StatCard.tsx" ]; then
  echo "Removing StatCard.tsx..."
  rm -f components/StatCard.tsx
fi

if [ -f "components/StatsIcon.tsx" ]; then
  echo "Removing StatsIcon.tsx..."
  rm -f components/StatsIcon.tsx
fi

# Remove unused modal and panel components
if [ -f "components/CustomerMatchingModal.tsx" ]; then
  echo "Removing CustomerMatchingModal.tsx..."
  rm -f components/CustomerMatchingModal.tsx
fi

if [ -f "components/CustomerMatchingPanel.tsx" ]; then
  echo "Removing CustomerMatchingPanel.tsx..."
  rm -f components/CustomerMatchingPanel.tsx
fi

if [ -f "components/VehicleMatchingModal.tsx" ]; then
  echo "Removing VehicleMatchingModal.tsx..."
  rm -f components/VehicleMatchingModal.tsx
fi

if [ -f "components/VehicleMatchingPanel.tsx" ]; then
  echo "Removing VehicleMatchingPanel.tsx..."
  rm -f components/VehicleMatchingPanel.tsx
fi

# Remove unused detail components (now using DealDetailsView)
if [ -f "components/CustomerDetails.tsx" ]; then
  echo "Removing CustomerDetails.tsx..."
  rm -f components/CustomerDetails.tsx
fi

if [ -f "components/VehicleDetails.tsx" ]; then
  echo "Removing VehicleDetails.tsx..."
  rm -f components/VehicleDetails.tsx
fi

# Remove unused navigation components (now using unified header)
if [ -f "components/AppHeader.tsx" ]; then
  echo "Removing AppHeader.tsx..."
  rm -f components/AppHeader.tsx
fi

if [ -f "components/AppSidebar.tsx" ]; then
  echo "Removing AppSidebar.tsx..."
  rm -f components/AppSidebar.tsx
fi

# Remove unused content components
if [ -f "components/DealsContent.tsx" ]; then
  echo "Removing DealsContent.tsx..."
  rm -f components/DealsContent.tsx
fi

# Remove unused step tracking components (replaced by simplified progress)
if [ -f "components/StepTracker.tsx" ]; then
  echo "Removing StepTracker.tsx..."
  rm -f components/StepTracker.tsx
fi

if [ -f "components/StepTrackerDemo.tsx" ]; then
  echo "Removing StepTrackerDemo.tsx..."
  rm -f components/StepTrackerDemo.tsx
fi

# Remove unused slider components
if [ -f "components/SliderHeader.tsx" ]; then
  echo "Removing SliderHeader.tsx..."
  rm -f components/SliderHeader.tsx
fi

# Remove unused hook files
if [ -f "hooks/useAppHandlers.ts" ]; then
  echo "Removing useAppHandlers.ts..."
  rm -f hooks/useAppHandlers.ts
fi

# Remove redundant config files
if [ -f "config/dealColumns.tsx" ]; then
  echo "Removing dealColumns.tsx..."
  rm -f config/dealColumns.tsx
fi

if [ -f "config/tableColumns.tsx" ]; then
  echo "Removing tableColumns.tsx..."
  rm -f config/tableColumns.tsx
fi

# Remove unused CSS files
if [ -f "styles/components.css" ]; then
  echo "Removing components.css..."
  rm -f styles/components.css
fi

if [ -f "styles/themes.css" ]; then
  echo "Removing themes.css..."
  rm -f styles/themes.css
fi

if [ -f "styles/variables.css" ]; then
  echo "Removing variables.css..."
  rm -f styles/variables.css
fi

# Remove unused import files
if [ -d "imports/" ]; then
  echo "Removing imports directory..."
  rm -rf imports/
fi

echo ""
echo "✅ Cleanup completed! Streamlined component architecture."
echo ""
echo "📦 Remaining core components:"
echo "  ✓ AccordionDealForm.tsx (5-step deal builder)"
echo "  ✓ AddDealForm.tsx (deal form wrapper)"
echo "  ✓ DealsBuiltTable.tsx (deals management)"
echo "  ✓ DealDetailsView.tsx (deal summary)"
echo "  ✓ NotesSlider.tsx (notes system)"
echo "  ✓ ApprovalEntityManager.tsx (approval management)"
echo "  ✓ VehicleFilters.tsx (vehicle selection)"
echo "  ✓ PhotoUpload.tsx (document uploads)"
echo "  ✓ StepNotesButton.tsx (notes integration)"
echo "  ✓ BrandIcon.tsx (branding)"
echo ""
echo "🎯 UX Improvements:"
echo "  ✓ Unified header navigation system"
echo "  ✓ Consistent badge styling (outline variants)"
echo "  ✓ Streamlined component interactions"
echo "  ✓ Reduced UI element variety"
echo "  ✓ Clean, focused deal builder workflow"