/**
 * Mock Deals Data - SubPrime Pro Deal Builder System
 * 
 * Enhanced mock data for the deal builder application with comprehensive
 * deal examples featuring varied statuses, approval entities, and realistic
 * Canadian dealership scenarios.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> | Built by Figma Make - Claude Sonnet
 */

import { Deal, ApprovalEntity } from '../types';

// Enhanced mock deals for the deal builder system with varied statuses
export const mockDeals: Deal[] = [
  {
    id: 'DEAL-2024-001',
    customerName: '<PERSON>',
    vehicleInfo: '2024 Toyota Camry Hybrid LE',
    dealValue: 32999,
    status: 'approved',
    location: 'Toronto Downtown',
    createdDate: '2024-01-15',
    approvalEntities: [
      {
        id: 'AE-001-01',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1985-06-15',
        socialInsuranceNumber: '123-456-789',
        drivingLicenseNumber: 'S1234567890',
        address: '123 Main St',
        city: 'Toronto',
        province: 'ON',
        postalCode: 'M5V 3A8',
        employmentStatus: 'Employed',
        employer: 'Tech Solutions Inc',
        jobTitle: 'Software Engineer',
        annualIncome: 82000
      }
    ],
    notes: [
      {
        id: 'NOTE-001-01',
        step: 1,
        stepName: 'Customer Information',
        content: 'Customer provided all required documents',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      }
    ]
  },
  {
    id: 'DEAL-2024-002',
    customerName: 'David Rodriguez',
    vehicleInfo: '2024 BMW X3 xDrive30i',
    dealValue: 52999,
    status: 'pending',
    location: 'Vancouver',
    createdDate: '2024-01-22',
    approvalEntities: [
      {
        id: 'AE-002-01',
        firstName: 'David',
        lastName: 'Rodriguez',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1978-03-22',
        socialInsuranceNumber: '234-567-890',
        drivingLicenseNumber: 'BC234567890',
        address: '456 Oak Ave',
        city: 'Vancouver',
        province: 'BC',
        postalCode: 'V6B 1A1',
        employmentStatus: 'Employed',
        employer: 'Pacific Industries',
        jobTitle: 'Operations Manager',
        annualIncome: 95000
      }
    ],
    notes: []
  },
  {
    id: 'DEAL-2024-003',
    customerName: 'Amanda Thompson',
    vehicleInfo: '2024 Honda Civic Sport',
    dealValue: 28499,
    status: 'approved',
    location: 'Calgary',
    createdDate: '2024-01-20',
    approvalEntities: [
      {
        id: 'AE-003-01',
        firstName: 'Amanda',
        lastName: 'Thompson',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1992-11-08',
        socialInsuranceNumber: '345-678-901',
        drivingLicenseNumber: 'AB345678901',
        address: '789 Pine St',
        city: 'Calgary',
        province: 'AB',
        postalCode: 'T2P 1J9',
        employmentStatus: 'Employed',
        employer: 'Alberta Health Services',
        jobTitle: 'Registered Nurse',
        annualIncome: 68000
      },
      {
        id: 'AE-003-02',
        firstName: 'Robert',
        lastName: 'Thompson',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Parent',
        dateOfBirth: '1965-05-12',
        socialInsuranceNumber: '456-789-012',
        drivingLicenseNumber: 'AB456789012',
        address: '789 Pine St',
        city: 'Calgary',
        province: 'AB',
        postalCode: 'T2P 1J9',
        employmentStatus: 'Employed',
        employer: 'Suncor Energy',
        jobTitle: 'Engineering Manager',
        annualIncome: 125000
      }
    ],
    notes: [
      {
        id: 'NOTE-003-01',
        step: 2,
        stepName: 'Approval Entities',
        content: 'Added parent as co-signer for better approval terms',
        createdAt: '2024-01-20T14:15:00Z',
        updatedAt: '2024-01-20T14:15:00Z'
      }
    ]
  },
  {
    id: 'DEAL-2024-004',
    customerName: 'Michael Chen',
    vehicleInfo: '2024 Ford F-150 XLT',
    dealValue: 45999,
    status: 'rejected',
    location: 'Montreal',
    createdDate: '2024-01-18',
    approvalEntities: [
      {
        id: 'AE-004-01',
        firstName: 'Michael',
        lastName: 'Chen',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1987-09-30',
        socialInsuranceNumber: '567-890-123',
        drivingLicenseNumber: 'QC567890123',
        address: '321 Maple Dr',
        city: 'Montreal',
        province: 'QC',
        postalCode: 'H3A 0G4',
        employmentStatus: 'Self-Employed',
        employer: 'Chen Consulting',
        jobTitle: 'Business Consultant', 
        annualIncome: 55000
      }
    ],
    notes: [
      {
        id: 'NOTE-004-01',
        step: 2,
        stepName: 'Approval Entities',
        content: 'Credit score below threshold, additional documentation required',
        createdAt: '2024-01-18T16:45:00Z',
        updatedAt: '2024-01-18T16:45:00Z'
      }
    ]
  },
  {
    id: 'DEAL-2024-005',
    customerName: 'Jennifer Walsh',
    vehicleInfo: '2024 Audi Q5 Progressiv',
    dealValue: 48999,
    status: 'draft',
    location: 'Ottawa',
    createdDate: '2024-01-25',
    approvalEntities: [
      {
        id: 'AE-005-01',
        firstName: 'Jennifer',
        lastName: 'Walsh',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1980-12-14',
        socialInsuranceNumber: '678-901-234',
        drivingLicenseNumber: 'ON678901234',
        address: '654 Cedar Ln',
        city: 'Ottawa',
        province: 'ON',
        postalCode: 'K1A 0A6',
        employmentStatus: 'Employed',
        employer: 'Government of Canada',
        jobTitle: 'Policy Analyst',
        annualIncome: 78000
      }
    ],
    notes: [
      {
        id: 'NOTE-005-01',
        step: 1,
        stepName: 'Customer Information',
        content: 'Draft saved - customer reviewing financing options',
        createdAt: '2024-01-25T11:20:00Z',
        updatedAt: '2024-01-25T11:20:00Z'
      }
    ]
  },
  {
    id: 'DEAL-2024-006',
    customerName: 'Robert Kim',
    vehicleInfo: '2024 Tesla Model 3',
    dealValue: 54999,
    status: 'pending',
    location: 'Toronto Downtown',
    createdDate: '2024-01-23',
    approvalEntities: [
      {
        id: 'AE-006-01',
        firstName: 'Robert',
        lastName: 'Kim',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1975-04-18',
        socialInsuranceNumber: '789-012-345',
        drivingLicenseNumber: 'ON789012345',
        address: '987 Elm St',
        city: 'Toronto',
        province: 'ON',
        postalCode: 'M4W 1A8',
        employmentStatus: 'Employed',
        employer: 'Financial Corp',
        jobTitle: 'Investment Advisor',
        annualIncome: 110000
      }
    ],
    notes: []
  },
  {
    id: 'DEAL-2024-007',
    customerName: 'Lisa Anderson',
    vehicleInfo: '2024 Mazda CX-5 GT',
    dealValue: 36999,
    status: 'approved',
    location: 'Winnipeg',
    createdDate: '2024-01-19',
    approvalEntities: [
      {
        id: 'AE-007-01',
        firstName: 'Lisa',
        lastName: 'Anderson',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1990-07-25',
        socialInsuranceNumber: '890-123-456',
        drivingLicenseNumber: 'MB890123456',
        address: '147 River Rd',
        city: 'Winnipeg',
        province: 'MB',
        postalCode: 'R3T 2N2',
        employmentStatus: 'Employed',
        employer: 'Manitoba Hydro',
        jobTitle: 'Electrical Engineer',
        annualIncome: 89000
      }
    ],
    notes: [
      {
        id: 'NOTE-007-01',
        step: 4,
        stepName: 'Vehicle Selection',
        content: 'Customer chose premium package upgrade',
        createdAt: '2024-01-19T13:30:00Z',
        updatedAt: '2024-01-19T13:30:00Z'
      }
    ]
  },
  {
    id: 'DEAL-2024-008',
    customerName: 'Mark Williams',
    vehicleInfo: '2024 Chevrolet Silverado 1500',
    dealValue: 41999,
    status: 'pending',
    location: 'Halifax',
    createdDate: '2024-01-21',
    approvalEntities: [
      {
        id: 'AE-008-01',
        firstName: 'Mark',
        lastName: 'Williams',
        email: '<EMAIL>',
        phone: '(*************',
        relationship: 'Primary',
        dateOfBirth: '1983-02-28',
        socialInsuranceNumber: '901-234-567',
        drivingLicenseNumber: 'NS901234567',
        address: '258 Harbor St',
        city: 'Halifax',
        province: 'NS',
        postalCode: 'B3H 2Y9',
        employmentStatus: 'Employed',
        employer: 'Maritime Construction',
        jobTitle: 'Project Manager',
        annualIncome: 72000
      }
    ],
    notes: []
  }
];

// Enhanced deal statistics for dashboard
export const dealStats = {
  totalDeals: mockDeals.length,
  approvedDeals: mockDeals.filter(deal => deal.status === 'approved').length,
  pendingDeals: mockDeals.filter(deal => deal.status === 'pending').length,
  rejectedDeals: mockDeals.filter(deal => deal.status === 'rejected').length,
  draftDeals: mockDeals.filter(deal => deal.status === 'draft').length,
  totalValue: mockDeals.reduce((sum, deal) => sum + deal.dealValue, 0),
  averageDealValue: mockDeals.length > 0 ? mockDeals.reduce((sum, deal) => sum + deal.dealValue, 0) / mockDeals.length : 0
};

// Export helper functions
export const getApprovedDeals = () => mockDeals.filter(deal => deal.status === 'approved');
export const getPendingDeals = () => mockDeals.filter(deal => deal.status === 'pending');
export const getRejectedDeals = () => mockDeals.filter(deal => deal.status === 'rejected');
export const getDraftDeals = () => mockDeals.filter(deal => deal.status === 'draft');
export const getDealsByStatus = (status: Deal['status']) => mockDeals.filter(deal => deal.status === status);
export const getDealsByLocation = (location: string) => mockDeals.filter(deal => deal.location === location);
export const getRecentDeals = (days = 7) => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  return mockDeals.filter(deal => new Date(deal.createdDate) >= cutoffDate);
};