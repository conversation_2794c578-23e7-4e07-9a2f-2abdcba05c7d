import { 
  Car, Users, DollarSign, TrendingUp, User, Building, CheckCircle, Clock, Award 
} from 'lucide-react';
import { 
  Customer, Vehicle, RecentSale, DashboardStat, PopularModel, 
  GrossEarningsDataPoint, DealsClosedDataPoint, StatCard 
} from '../types';

/**
 * Canadian Dealership Locations - Simple structure for deal forms
 * Represents the SubPrime Pro dealership network across Canada
 */
export const locations: Array<{ id: string; name: string; count: number }> = [
  { id: 'all', name: 'All Locations', count: 847 },
  { id: 'toronto', name: 'Toronto Downtown', count: 234 },
  { id: 'mississauga', name: 'Mississauga', count: 189 },
  { id: 'vancouver', name: 'Vancouver', count: 167 },
  { id: 'calgary', name: 'Calgary', count: 145 },
  { id: 'montreal', name: 'Montreal', count: 112 }
];

/**
 * Dashboard Statistics - Enhanced with Consistent Icon Styling
 * High-level metrics displayed on the main dashboard
 * Now uses standardized StatsIcon component instead of color properties
 */
export const dashboardStats: DashboardStat[] = [
  { title: 'Total Inventory', value: '847', icon: Car, trend: '+12%' },
  { title: 'Active Customers', value: '2,341', icon: Users, trend: '+8%' },
  { title: 'Monthly Sales', value: '$1.2M', icon: DollarSign, trend: '+15%' },
  { title: 'Revenue Growth', value: '23%', icon: TrendingUp, trend: '+5%' },
  { title: 'Average LTV', value: '$89K', icon: DollarSign, trend: '+6%' }
];

/**
 * Analytics Data for Charts
 */
export const popularModels: PopularModel[] = [
  { model: 'Toyota Camry', sales: 23, trend: '+18%', percentage: 28 },
  { model: 'Honda Civic', sales: 19, trend: '+12%', percentage: 23 },
  { model: 'Ford F-150', sales: 16, trend: '+8%', percentage: 19 },
  { model: 'Chevrolet Equinox', sales: 13, trend: '+15%', percentage: 16 },
  { model: 'Nissan Altima', sales: 11, trend: '+5%', percentage: 14 }
];

export const grossEarningsData: GrossEarningsDataPoint[] = [
  { month: 'Aug 2023', earnings: 145000, target: 140000 },
  { month: 'Sep 2023', earnings: 162000, target: 150000 },
  { month: 'Oct 2023', earnings: 158000, target: 155000 },
  { month: 'Nov 2023', earnings: 178000, target: 160000 },
  { month: 'Dec 2023', earnings: 195000, target: 170000 },
  { month: 'Jan 2024', earnings: 187000, target: 175000 }
];

export const dealsClosedData: DealsClosedDataPoint[] = [
  { date: 'Jan 1', deals: 2 }, { date: 'Jan 2', deals: 1 }, { date: 'Jan 3', deals: 3 },
  { date: 'Jan 4', deals: 2 }, { date: 'Jan 5', deals: 4 }, { date: 'Jan 6', deals: 1 },
  { date: 'Jan 7', deals: 0 }, { date: 'Jan 8', deals: 3 }, { date: 'Jan 9', deals: 2 },
  { date: 'Jan 10', deals: 5 }, { date: 'Jan 11', deals: 3 }, { date: 'Jan 12', deals: 2 },
  { date: 'Jan 13', deals: 4 }, { date: 'Jan 14', deals: 1 }, { date: 'Jan 15', deals: 6 },
  { date: 'Jan 16', deals: 3 }, { date: 'Jan 17', deals: 2 }, { date: 'Jan 18', deals: 4 },
  { date: 'Jan 19', deals: 5 }, { date: 'Jan 20', deals: 2 }, { date: 'Jan 21', deals: 0 },
  { date: 'Jan 22', deals: 3 }, { date: 'Jan 23', deals: 4 }, { date: 'Jan 24', deals: 2 },
  { date: 'Jan 25', deals: 5 }, { date: 'Jan 26', deals: 3 }, { date: 'Jan 27', deals: 4 },
  { date: 'Jan 28', deals: 2 }, { date: 'Jan 29', deals: 6 }, { date: 'Jan 30', deals: 3 }
];

/**
 * Vehicle Inventory Data
 */
export const initialInventory: Vehicle[] = [
  { 
    id: 1, 
    make: 'Toyota', 
    model: 'Camry', 
    year: 2024, 
    price: '$32,999', 
    status: 'Available', 
    location: 'Toronto Downtown', 
    vin: '1HGCM82633A123456',
    mileage: '12,450 km',
    exterior: 'Midnight Black',
    interior: 'Black Leather',
    transmission: 'CVT',
    fuelType: 'Hybrid',
    daysOnLot: 23,
    cost: '$28,500',
    photos: [
      'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=400&h=300&fit=crop'
    ],
    features: ['Navigation', 'Backup Camera', 'Heated Seats', 'Apple CarPlay', 'Android Auto', 'Adaptive Cruise']
  },
  { 
    id: 2, 
    make: 'Honda', 
    model: 'Civic', 
    year: 2024, 
    price: '$28,499', 
    status: 'Reserved', 
    location: 'Mississauga', 
    vin: '2HGCM82633A123457',
    mileage: '8,750 km',
    exterior: 'Pearl White',
    interior: 'Black Cloth',
    transmission: 'CVT',
    fuelType: 'Gasoline',
    daysOnLot: 15,
    cost: '$24,800',
    photos: [
      'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=400&h=300&fit=crop'
    ],
    features: ['Honda Sensing', 'Sunroof', 'Wireless Charging', 'LED Headlights', 'Lane Keep Assist', 'Collision Mitigation']
  },
  { 
    id: 3, 
    make: 'Ford', 
    model: 'F-150', 
    year: 2023, 
    price: '$45,999', 
    status: 'Available', 
    location: 'Vancouver', 
    vin: '3HGCM82633A123458',
    mileage: '25,100 km',
    exterior: 'Agate Black',
    interior: 'Medium Earth Gray',
    transmission: '10-Speed Automatic',
    fuelType: 'Gasoline',
    daysOnLot: 41,
    cost: '$41,200',
    photos: [
      'https://images.unsplash.com/photo-1563720223185-11003d516935?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1594736797933-d0b22090b064?w=400&h=300&fit=crop'
    ],
    features: ['4WD', 'Tow Package', 'B&O Sound', 'Power Tailgate', 'Bed Liner', 'Trailer Brake', 'Hill Descent', 'Skid Plates']
  },
  { 
    id: 4, 
    make: 'Chevrolet', 
    model: 'Equinox', 
    year: 2024, 
    price: '$35,299', 
    status: 'Sold', 
    location: 'Calgary', 
    vin: '4HGCM82633A123459',
    mileage: '5,230 km',
    exterior: 'Summit White',
    interior: 'Jet Black',
    transmission: 'CVT',
    fuelType: 'Gasoline',
    daysOnLot: 8,
    cost: '$31,100',
    photos: [
      'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=400&h=300&fit=crop'
    ],
    features: ['AWD', 'Remote Start', 'Bose Audio', 'Safety Package', 'Heated Steering', 'Power Liftgate']
  },
  { 
    id: 5, 
    make: 'Nissan', 
    model: 'Altima', 
    year: 2024, 
    price: '$30,799', 
    status: 'Available', 
    location: 'Montreal', 
    vin: '5HGCM82633A123460',
    mileage: '18,650 km',
    exterior: 'Gun Metallic',
    interior: 'Charcoal',
    transmission: 'CVT',
    fuelType: 'Gasoline',
    daysOnLot: 35,
    cost: '$26,950',
    photos: [
      'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=300&fit=crop'
    ],
    features: ['ProPILOT Assist', 'Zero Gravity Seats', 'NissanConnect', 'Intelligent AWD', 'Blind Spot Monitor', 'Emergency Braking']
  },
  { 
    id: 6, 
    make: 'BMW', 
    model: 'X3', 
    year: 2024, 
    price: '$52,999', 
    status: 'Available', 
    location: 'Toronto Downtown', 
    vin: '6HGCM82633A123461',
    mileage: '15,200 km',
    exterior: 'Alpine White',
    interior: 'Black SensaTec',
    transmission: '8-Speed Automatic',
    fuelType: 'Gasoline',
    daysOnLot: 28,
    cost: '$47,500',
    photos: [
      'https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=300&fit=crop'
    ],
    features: ['xDrive AWD', 'Panoramic Roof', 'Harman Kardon Audio', 'Wireless Charging', 'Gesture Control', 'Head-Up Display']
  },
  { 
    id: 7, 
    make: 'Audi', 
    model: 'Q5', 
    year: 2024, 
    price: '$48,999', 
    status: 'Available', 
    location: 'Vancouver', 
    vin: '7HGCM82633A123462',
    mileage: '9,800 km',
    exterior: 'Quantum Gray',
    interior: 'Black Leather',
    transmission: '7-Speed S tronic',
    fuelType: 'Gasoline',
    daysOnLot: 19,
    cost: '$43,800',
    photos: [
      'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop'
    ],
    features: ['quattro AWD', 'Virtual Cockpit', 'Bang & Olufsen Audio', 'Adaptive Suspension', 'Matrix LED', 'Park Assist']
  },
  { 
    id: 8, 
    make: 'Mercedes-Benz', 
    model: 'GLC', 
    year: 2024, 
    price: '$55,999', 
    status: 'Available', 
    location: 'Calgary', 
    vin: '8HGCM82633A123463',
    mileage: '11,500 km',
    exterior: 'Obsidian Black',
    interior: 'Artico Leather',
    transmission: '9-Speed Automatic',
    fuelType: 'Gasoline',
    daysOnLot: 31,
    cost: '$50,200',
    photos: [
      'https://images.unsplash.com/photo-1618843479313-40f8afb4b4d8?w=400&h=300&fit=crop'
    ],
    features: ['4MATIC AWD', 'MBUX System', 'Burmester Audio', 'AMG Line', 'Air Suspension', 'Night Package']
  }
];

/**
 * Customer Database
 */
export const initialCustomers: Customer[] = [
  { 
    id: 'CU001', 
    name: 'Sarah Johnson', 
    photo: 'https://images.unsplash.com/photo-1494790108755-2616b332446c?w=120&h=120&fit=crop&crop=face',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: 'S234567890123456',
    address: '123 Queen St W',
    city: 'Toronto',
    province: 'ON',
    postalCode: 'M5H 2M9',
    customerType: 'Individual',
    status: 'Active', 
    creditRating: 'A+',
    joinDate: '2023-03-15',
    lastContact: '2024-01-15', 
    assignedDealer: 'Toronto Downtown',
    salesRep: 'John Smith',
    lifetimeValue: '$67,500',
    totalPurchases: 2,
    vehicleInterests: ['Sedan', 'SUV'],
    leadSource: 'Website'
  },
  { 
    id: 'CU002', 
    name: 'Mike Chen', 
    photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: 'C567890123456789',
    address: '456 Dundas St E',
    city: 'Mississauga',
    province: 'ON',
    postalCode: 'L5A 1W4',
    customerType: 'Individual',
    status: 'Prospect', 
    creditRating: 'A',
    joinDate: '2023-11-22',
    lastContact: '2024-01-14', 
    assignedDealer: 'Mississauga',
    salesRep: 'Jane Doe',
    lifetimeValue: '$28,499',
    totalPurchases: 1,
    vehicleInterests: ['Compact', 'Hybrid'],
    leadSource: 'Referral'
  },
  { 
    id: 'CU003', 
    name: 'Emily Rodriguez', 
    photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=120&h=120&fit=crop&crop=face',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: '1234567',
    address: '789 Granville St',
    city: 'Vancouver',
    province: 'BC',
    postalCode: 'V6Z 1K3',
    customerType: 'Individual',
    status: 'Active', 
    creditRating: 'B+',
    joinDate: '2022-08-10',
    lastContact: '2024-01-13', 
    assignedDealer: 'Vancouver',
    salesRep: 'Bob Wilson',
    lifetimeValue: '$91,200',
    totalPurchases: 2,
    vehicleInterests: ['Truck', 'SUV'],
    leadSource: 'Social Media'
  },
  { 
    id: 'CU004', 
    name: 'David Thompson', 
    photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: '987654321',
    address: '321 17th Ave SW',
    city: 'Calgary',
    province: 'AB',
    postalCode: 'T2S 0A1',
    customerType: 'Business',
    status: 'VIP', 
    creditRating: 'A+',
    joinDate: '2021-12-05',
    lastContact: '2024-01-10', 
    assignedDealer: 'Calgary',
    salesRep: 'Lisa Park',
    lifetimeValue: '$245,800',
    totalPurchases: 5,
    vehicleInterests: ['Truck', 'Commercial'],
    leadSource: 'Trade Show'
  },
  { 
    id: 'CU005', 
    name: 'Marie Dubois', 
    photo: 'https://images.unsplash.com/photo-**********-94ddf0286df2?w=120&h=120&fit=crop&crop=face',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: 'D567812345612',
    address: '567 Rue Sainte-Catherine E',
    city: 'Montreal',
    province: 'QC',
    postalCode: 'H2L 2C9',
    customerType: 'Individual',
    status: 'Active', 
    creditRating: 'A',
    joinDate: '2023-05-18',
    lastContact: '2024-01-12', 
    assignedDealer: 'Montreal',
    salesRep: 'Pierre Martin',
    lifetimeValue: '$42,300',
    totalPurchases: 1,
    vehicleInterests: ['Compact', 'Electric'],
    leadSource: 'Google Ads'
  },
  { 
    id: 'CU006', 
    name: 'James Wilson', 
    photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=120&h=120&fit=crop&crop=face',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: 'W789012345678901',
    address: '890 Yonge St',
    city: 'Toronto',
    province: 'ON',
    postalCode: 'M4W 2H1',
    customerType: 'Individual',
    status: 'Inactive', 
    creditRating: 'B',
    joinDate: '2022-01-30',
    lastContact: '2023-12-20', 
    assignedDealer: 'Toronto Downtown',
    salesRep: 'John Smith',
    lifetimeValue: '$35,799',
    totalPurchases: 1,
    vehicleInterests: ['SUV'],
    leadSource: 'Walk-in'
  },
  { 
    id: 'CU007', 
    name: 'Tech Solutions Inc.', 
    photo: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=120&h=120&fit=crop&crop=center',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: '',
    address: '234 Corporate Blvd',
    city: 'Markham',
    province: 'ON',
    postalCode: 'L3R 8T4',
    customerType: 'Business',
    status: 'Corporate', 
    creditRating: 'A+',
    joinDate: '2022-06-12',
    lastContact: '2024-01-11', 
    assignedDealer: 'Mississauga',
    salesRep: 'Jane Doe',
    lifetimeValue: '$380,500',
    totalPurchases: 12,
    vehicleInterests: ['Fleet', 'Sedan', 'SUV'],
    leadSource: 'B2B Sales'
  },
  { 
    id: 'CU008', 
    name: 'Amanda Foster', 
    photo: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=120&h=120&fit=crop&crop=face',
    email: '<EMAIL>', 
    phone: '(*************', 
    drivingLicenseNumber: '7654321',
    address: '678 Robson St',
    city: 'Vancouver',
    province: 'BC',
    postalCode: 'V6B 2E8',
    customerType: 'Individual',
    status: 'Prospect', 
    creditRating: 'A-',
    joinDate: '2024-01-05',
    lastContact: '2024-01-14', 
    assignedDealer: 'Vancouver',
    salesRep: 'Bob Wilson',
    lifetimeValue: '$0',
    totalPurchases: 0,
    vehicleInterests: ['Electric', 'Hybrid'],
    leadSource: 'Email Campaign'
  }
];

/**
 * Recent Sales Data - Enhanced with unique IDs
 */
export const recentSales: RecentSale[] = [
  { id: 1, customer: 'Sarah Johnson', vehicle: '2024 Toyota Camry', amount: '$32,999', date: '2024-01-15', salesperson: 'John Smith', location: 'Toronto Downtown' },
  { id: 2, customer: 'Mike Chen', vehicle: '2024 Honda Civic', amount: '$28,499', date: '2024-01-14', salesperson: 'Jane Doe', location: 'Mississauga' },
  { id: 3, customer: 'Emily Rodriguez', vehicle: '2023 Ford F-150', amount: '$45,999', date: '2024-01-13', salesperson: 'Bob Wilson', location: 'Vancouver' },
  { id: 4, customer: 'David Thompson', vehicle: '2024 Chevrolet Equinox', amount: '$35,299', date: '2024-01-12', salesperson: 'Lisa Park', location: 'Calgary' },
  { id: 5, customer: 'Marie Dubois', vehicle: '2024 Nissan Altima', amount: '$30,799', date: '2024-01-11', salesperson: 'Pierre Martin', location: 'Montreal' }
];

/**
 * Helper function to create remaining customer stat cards (without Average LTV)
 * Average LTV has been moved to the main dashboard
 */
export const createCustomerStats = (customers: Customer[]): StatCard[] => {
  const totalCustomers = customers.length;
  const individualCustomers = customers.filter(c => c.customerType === 'Individual').length;
  const businessCustomers = customers.filter(c => c.customerType === 'Business').length;

  return [
    {
      title: 'Total Customers',
      value: totalCustomers.toString(),
      trend: '+12%',
      trendLabel: 'vs last month',
      icon: Users,
      iconBg: 'bg-primary-95',
      iconColor: 'text-primary-40'
    },
    {
      title: 'Individual',
      value: individualCustomers.toString(),
      trend: '+8%',
      trendLabel: 'vs last month',
      icon: User,
      iconBg: 'bg-primary-90',
      iconColor: 'text-primary-50'
    },
    {
      title: 'Business',
      value: businessCustomers.toString(),
      trend: '+15%',
      trendLabel: 'vs last month',
      icon: Building,
      iconBg: 'bg-primary-80',
      iconColor: 'text-primary-60'
    }
  ];
};

export const createInventoryStats = (inventory: Vehicle[]): StatCard[] => {
  const totalInventory = inventory.length;
  // Status-based filtering removed since status is not fetched by system
  // Using simplified stats based on total inventory
  const newInventory = Math.floor(totalInventory * 0.6); // Approximate new vehicles
  const usedInventory = totalInventory - newInventory; // Remaining as used
  const monthlyAdditions = Math.floor(totalInventory * 0.15); // Approximate monthly additions

  return [
    {
      title: 'Total Inventory',
      value: totalInventory.toString(),
      trend: '+5%',
      trendLabel: 'vs last month',
      icon: Car,
      iconBg: 'bg-primary-95',
      iconColor: 'text-primary-40'
    },
    {
      title: 'New Vehicles',
      value: newInventory.toString(),
      trend: '-3%',
      trendLabel: 'vs last month',
      icon: CheckCircle,
      iconBg: 'bg-primary-90',
      iconColor: 'text-primary-50'
    },
    {
      title: 'Used Vehicles',
      value: usedInventory.toString(),
      trend: '+25%',
      trendLabel: 'vs last month',
      icon: Clock,
      iconBg: 'bg-primary-80',
      iconColor: 'text-primary-60'
    },
    {
      title: 'Added This Month',
      value: monthlyAdditions.toString(),
      trend: '+18%',
      trendLabel: 'vs last month',
      icon: TrendingUp,
      iconBg: 'bg-orange-50',
      iconColor: 'text-[var(--brand-orange)]'
    }
  ];
};