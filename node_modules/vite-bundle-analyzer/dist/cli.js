"use strict";var e=require("./index-X2LWyYMI.js"),t=require("fs"),r=require("path"),o=require("url");function n(e){return null==e?[]:Array.isArray(e)?e:[e]}require("stream"),require("zlib"),require("util"),require("child_process"),require("os"),require("events"),require("http"),require("net");let i="\n";"win32"===process.platform&&(i="\r\n");const a="Analyzer modes. Should be `server`, `static` or `json`. "+i+e.ansis.green("In `server` mode analyzer will start HTTP server to show bundle report.")+i+e.ansis.green("In `static` mode single HTML file with bundle report will be generated.(If you use this mode with `openAnalyzer` option also start HTTP server).")+i+e.ansis.green("In `json` mode single JSON file with bundle report will be generated."),l=process.cwd();function s(n){let i=e.searchForWorkspaceRoot(l),a=function(e,o){let n=o;for(;n!==r.parse(n).root;){let o=r.join(n,"node_modules",e);if(t.statSync(o).isDirectory())return o;n=r.dirname(n)}return null}(n,i);if(!a)throw Error(`Cannot find module '${n}' in '${i}'`);let s=r.join(a,"package.json"),f=JSON.parse(t.readFileSync(s,"utf-8")),u=f.module||f.main,c="module"===f.type;if(f.exports&&!u)for(u=f.exports["."]||f.exports["./index"]||f.exports["./index.js"];"string"!=typeof u;)if(u)u=c?u.import||u.default:u.require||u.default;else break;if(!u)throw Error(`Cannot find entry point for module '${n}'`);if(u=r.join(a,u),t.existsSync(u)&&t.statSync(u).isFile())return o.pathToFileURL(u).href;throw Error(`Cannot resolve entry point for package '${n}'`)}const f=["js","mjs","cjs","ts","mts","cts"];async function u(o){let{config:n,mode:i,filename:a,port:l,open:u,summary:c,...d}=o,p=function(o){if(o)return o;let n=e.searchForPackageRoot(process.cwd());for(let e of f){let o=r.join(n,"vite.config."+e);if(t.existsSync(o))return o}throw Error("Missing Vite configuration file. Use --config <path> to specify location")}(n),g=await import(s("vite"));await g.build({configFile:p,plugins:[e.analyzer({analyzerMode:i,fileName:a,openAnalyzer:u,summary:c,analyzerPort:+l,...d})]})}const c={mode:{alias:"m",desc:a,default:"server",flag:"<mode>"},filename:{alias:"f",desc:"Output file name. If not specified will be generated automatically.",default:"stats"},port:{alias:"p",desc:"Port for HTTP server.",default:"8888",flag:"<port>"},reportTitle:{alias:"t",desc:"Title for bundle report.",default:"vite-bundle-analyzer",flag:"[title]"},open:{alias:"o",desc:"Open report in default browser.",default:!0,flag:"[bool]"},defaultSizes:{alias:"d",desc:"Default size type. Should be `stat` , `parsed` , `gzip` or `brotli`.",default:"stat",flag:"<string>"},summary:{alias:"s",desc:"Show full chunk info to stdout.",default:!0,flag:"[bool]"},config:{alias:"c",desc:"Path to vite config file. Automic search for vite configuration in your current workspace.",default:"",flag:"<path>"}},d=function(e,t){t=t||{};var r,o,i,a,l,s={_:[]},f=0,u=0,c=0,d=(e=e||[]).length;let p=void 0!==t.alias,g=void 0!==t.unknown,h=void 0!==t.default;if(t.alias=t.alias||{},t.string=n(t.string),t.boolean=n(t.boolean),p)for(r in t.alias)for(f=0,o=t.alias[r]=n(t.alias[r]);f<o.length;f++)(t.alias[o[f]]=o.concat(r)).splice(f,1);for(f=t.boolean.length;f-- >0;)for(u=(o=t.alias[t.boolean[f]]||[]).length;u-- >0;)t.boolean.push(o[u]);for(f=t.string.length;f-- >0;)for(u=(o=t.alias[t.string[f]]||[]).length;u-- >0;)t.string.push(o[u]);if(h){for(r in t.default)if(a=typeof t.default[r],o=t.alias[r]=t.alias[r]||[],void 0!==t[a])for(t[a].push(r),f=0;f<o.length;f++)t[a].push(o[f])}let b=g?Object.keys(t.alias):[];for(f=0;f<d;f++){if("--"===(i=e[f])){s._=s._.concat(e.slice(++f));break}for(u=0;u<i.length&&45===i.charCodeAt(u);u++);if(0===u)s._.push(i);else if("no-"===i.substring(u,u+3)){if(a=i.substring(u+3),g&&!~b.indexOf(a))return t.unknown(i);s[a]=!1}else{for(c=u+1;c<i.length&&61!==i.charCodeAt(c);c++);for(a=i.substring(u,c),l=i.substring(++c)||f+1===d||45===(""+e[f+1]).charCodeAt(0)||e[++f],o=2===u?[a]:a,c=0;c<o.length;c++){if(a=o[c],g&&!~b.indexOf(a))return t.unknown("-".repeat(u)+a);!function(e,t,r,o){var n,i=e[t],a=~o.string.indexOf(t)?null==r||!0===r?"":String(r):"boolean"==typeof r?r:~o.boolean.indexOf(t)?"false"!==r&&("true"===r||(e._.push(0*(n=+r)==0?n:r),!!r)):0*(n=+r)==0?n:r;e[t]=null==i?a:Array.isArray(i)?i.concat(a):[i,a]}(s,a,c+1<o.length||l,t)}}}if(h)for(r in t.default)void 0===s[r]&&(s[r]=t.default[r]);if(p)for(r in s)for(o=t.alias[r]||[];o.length>0;)s[o.shift()]=s[r];return s}(process.argv.slice(2),{alias:Object.fromEntries(Object.entries(c).map(([e,{alias:t}])=>[t,e])),default:Object.fromEntries(Object.entries(c).map(([e,{default:t}])=>[e,t])),boolean:["open","summary"]});(d.h||d.help)&&(function(){console.log("Usage: vite-bundle-analyzer [options]"+i),console.log("Options:");let e=Object.entries(c).map(([e,t])=>`-${t.alias}, --${e} ${t.flag||""}`).reduce((e,t)=>Math.max(e,t.length),0),t=" ".repeat(e+6);for(let[r,o]of Object.entries(c)){let n=`-${o.alias}, --${r} ${o.flag||""}`.padEnd(e+4),a=o.default?` (default: ${function(e){if(null==e)return"";switch(typeof e){case"boolean":case"number":return e.toString();case"string":return e;case"object":return"[object]";case"function":return"[function]";default:return""}}(o.default)})`:"",[l,...s]=o.desc.split(i);if(!s.length){console.log(`  ${n}${l}${a}`);continue}console.log(`  ${n}${l}`);let f=s.length-1;for(let e=0;e<s.length;e++){let r=s[e];if(e===f){console.log(`${t}${r}${a}`);break}console.log(`${t}${r}`)}console.log("")}}(),process.exit(0)),u(d).catch(console.error),exports.importMetaResolve=s;
