let e;import t from"fs";import r from"path";import{Readable as n}from"stream";import i from"zlib";import s from"util";import o from"child_process";import l from"os";import{EventEmitter as a}from"events";import u from"http";import c from"net";var p,f={exports:{}};Object.defineProperty(f.exports,"__esModule",{value:!0});let{round:d,floor:h,max:m}=Math,g=e=>{let[,t]=/([a-f\d]{3,6})/i.exec(e)||[],r=t?t.length:0;if(3===r)t=t[0]+t[0]+t[1]+t[1]+t[2]+t[2];else if(6!==r)return[0,0,0];let n=parseInt(t,16);return[n>>16&255,n>>8&255,255&n]},y=(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:d((e-8)/247*24)+232:16+36*d(e/51)+6*d(t/51)+d(r/51),w=e=>{let t,r,n,i,s,o;return e<8?30+e:e<16?e-8+90:(e>=232?t=r=n=(10*(e-232)+8)/255:(o=(e-=16)%36,t=h(e/36)/5,r=h(o/6)/5,n=o%6/5),0==(i=2*m(t,r,n))?30:(s=30+(d(n)<<2|d(r)<<1|d(t)),2===i?s+60:s))},S=(e,t,r)=>w(y(e,t,r)),z=(e=>{let t=e=>!!a.find(t=>e.test(t)),r=globalThis,n=r.Deno,i=null!=n,s=r.process||n||{},o=s.stdout,l="win32"===(i?n.build.os:s.platform),a=s.argv||s.args||[],u=s.env||{},c=-1;if(i)try{u=u.toObject()}catch(e){c=0}let p="FORCE_COLOR",f=u[p],d=parseInt(f),h="false"===f?0:isNaN(d)?3:d,m="NO_COLOR"in u||0===h||t(/^-{1,2}(no-color|color=(false|never))$/),g=p in u&&h||t(/^-{1,2}color=?(true|always)?$/),y=(u.NEXT_RUNTIME||"").indexOf("edge")>-1||"PM2_HOME"in u&&"pm_id"in u||(i?n.isatty(1):o&&"isTTY"in o);return m?0:(c<0&&(c=((e,t,r)=>{let{TERM:n,COLORTERM:i}=e;return"TF_BUILD"in e?1:"TEAMCITY_VERSION"in e?2:"CI"in e?["GITHUB_ACTIONS","GITEA_ACTIONS"].some(t=>t in e)?3:1:!t||/-mono|dumb/i.test(n)?0:r||"truecolor"===i||"24bit"===i||"xterm-kitty"===n?3:/-256(colou?r)?$/i.test(n)?2:/^screen|^tmux|^xterm|^vt[1-5][0-9]([0-9])?|^ansi|color|cygwin|linux|mintty|rxvt/i.test(n)?1:3})(u,y,l)),g&&0===c?3:c)})(),b=z>0,v={open:"",close:""},x=b?(e,t)=>({open:`[${e}m`,close:`[${t}m`}):()=>v,O=e=>(t,r,n)=>e(y(t,r,n)),$=e=>t=>{let[r,n,i]=g(t);return e(r,n,i)},_=e=>x(`38;5;${e}`,39),k=e=>x(`48;5;${e}`,49),M=(e,t,r)=>x(`38;2;${e};${t};${r}`,39),E=(e,t,r)=>x(`48;2;${e};${t};${r}`,49);1===z?(_=e=>x(w(e),39),k=e=>x(w(e)+10,49),M=(e,t,r)=>x(S(e,t,r),39),E=(e,t,r)=>x(S(e,t,r)+10,49)):2===z&&(M=O(_),E=O(k));let C,I,j={ansi256:_,bgAnsi256:k,fg:_,bg:k,rgb:M,bgRgb:E,hex:$(M),bgHex:$(E),visible:v,reset:x(0,0),inverse:x(7,27),hidden:x(8,28),bold:x(1,22),dim:x(2,22),italic:x(3,23),underline:x(4,24),strikethrough:x(9,29),strike:x(9,29),grey:x(90,39),gray:x(90,39),bgGrey:x(100,49),bgGray:x(100,49)},R="Bright",A=30;for(C of["black","red","green","yellow","blue","magenta","cyan","white"])I="bg"+C[0].toUpperCase()+C.slice(1),j[C]=x(A,39),j[C+R]=x(A+60,39),j[I]=x(A+10,49),j[I+R]=x(A+70,49),A++;let{defineProperty:N,defineProperties:P,setPrototypeOf:B}=Object,L=/[][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g,T=/(\r?\n)/g,F={},D=({_p:t},{open:r,close:n})=>{let i=(e,...t)=>{if(!e)return"";let r=i._p,{_a:n,_b:s}=r,o=null!=e.raw?String.raw(e,...t):""+e;if(o.includes("\x1b"))for(;null!=r;){let e=r.close,t=e.length;if(t){let n,i=0,s="";for(;~(n=o.indexOf(e,i));)s+=o.slice(i,n)+r.open,i=n+t;i&&(o=s+o.slice(i))}r=r._p}return o.includes("\n")&&(o=o.replace(T,s+"$1"+n)),n+o+s},s=r,o=n;return null!=t&&(s=t._a+r,o=n+t._b),B(i,e),i._p={open:r,close:n,_a:s,_b:o,_p:t},i.open=s,i.close=o,i},U=function(){let t=e=>""+e;return t.isSupported=()=>b,t.strip=e=>e.replace(L,""),t.extend=r=>{for(let e in r){let t=r[e],n=typeof t,i="string"===n?M(...g(t)):t;F[e]="function"===n?{get(){return(...e)=>D(this,t(...e))}}:{get(){let t=D(this,i);return N(this,e,{value:t}),t}}}B(t,e=P({},F))},t.extend(j),t},W=new U;f.exports=W,f.exports.Ansis=U;var H=(p=f.exports)&&p.__esModule&&Object.prototype.hasOwnProperty.call(p,"default")?p.default:p;let G=t.promises,q=new TextEncoder,J=new TextDecoder,Y=s.promisify(i.gzip),K=s.promisify(i.brotliCompress),V={level:i.constants.Z_DEFAULT_LEVEL},Z={params:{[i.constants.BROTLI_PARAM_QUALITY]:i.constants.BROTLI_MAX_QUALITY}};function Q(e){return"string"==typeof e?q.encode(e):e}let X=process.env.DEBUG||process.env.ANALYZE_DEBUG?(...e)=>{console.log(H.hex("#5B45DE")("[vite:bundle-analyzer]"),...e)}:()=>{},ee="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",et=new Uint8Array(64),er=new Uint8Array(128);for(let e=0;e<ee.length;e++){let t=ee.charCodeAt(e);et[e]=t,er[t]=e}function en(e,t,r,n){let i=0,s=0,o=0;do i|=(31&(o=er[e.charCodeAt(t++)]))<<s,s+=5;while(32&o);let l=1&i;return i>>>=1,l&&(i=-2147483648|-i),r[n]+=i,t}function ei(e,t,r){return!(t>=r)&&44!==e.charCodeAt(t)}function es(e,t){return e[0]-t[0]}let eo=/^[\w+.-]+:\/\//,el=/^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?(\?[^#]*)?(#.*)?/,ea=/^file:(?:\/\/((?![a-z]:)[^/#?]*)?)?(\/?[^#?]*)(\?[^#]*)?(#.*)?/i;function eu(e){return e.startsWith("/")}function ec(e){return/^[.?#]/.test(e)}function ep(e){let t=el.exec(e);return ef(t[1],t[2]||"",t[3],t[4]||"",t[5]||"/",t[6]||"",t[7]||"")}function ef(e,t,r,n,i,s,o){return{scheme:e,user:t,host:r,port:n,path:i,query:s,hash:o,type:7}}function ed(e){if(e.startsWith("//")){let t=ep("http:"+e);return t.scheme="",t.type=6,t}if(eu(e)){let t=ep("http://foo.com"+e);return t.scheme="",t.host="",t.type=5,t}if(e.startsWith("file:"))return function(e){let t=ea.exec(e),r=t[2];return ef("file:","",t[1]||"","",eu(r)?r:"/"+r,t[3]||"",t[4]||"")}(e);if(eo.test(e))return ep(e);let t=ep("http://foo.com/"+e);return t.scheme="",t.host="",t.type=e?e.startsWith("?")?3:e.startsWith("#")?2:4:1,t}function eh(e,t){let r=t<=4,n=e.path.split("/"),i=1,s=0,o=!1;for(let e=1;e<n.length;e++){let t=n[e];if(!t){o=!0;continue}if(o=!1,"."!==t){if(".."===t){s?(o=!0,s--,i--):r&&(n[i++]=t);continue}n[i++]=t,s++}}let l="";for(let e=1;e<i;e++)l+="/"+n[e];l&&(!o||l.endsWith("/.."))||(l+="/"),e.path=l}function em(e,t){return t&&!t.endsWith("/")&&(t+="/"),function(e,t){if(!e&&!t)return"";let r=ed(e),n=r.type;if(t&&7!==n){let e=ed(t),i=e.type;switch(n){case 1:r.hash=e.hash;case 2:r.query=e.query;case 3:case 4:eh(e,e.type),"/"===r.path?r.path=e.path:r.path=function(e){if(e.endsWith("/.."))return e;let t=e.lastIndexOf("/");return e.slice(0,t+1)}(e.path)+r.path;case 5:r.user=e.user,r.host=e.host,r.port=e.port;case 6:r.scheme=e.scheme}i>n&&(n=i)}eh(r,n);let i=r.query+r.hash;switch(n){case 2:case 3:return i;case 4:{let n=r.path.slice(1);if(!n)return i||".";if(ec(t||e)&&!ec(n))return"./"+n+i;return n+i}case 5:return r.path+i;default:return r.scheme+"//"+r.user+r.host+r.port+r.path+i}}(e,t)}function eg(e,t){for(let r=t;r<e.length;r++)if(!function(e){for(let t=1;t<e.length;t++)if(e[t][0]<e[t-1][0])return!1;return!0}(e[r]))return r;return e.length}function ey(e,t){return e[0]-t[0]}let ew=!1,eS=function(e,t){let r=ez(e);if(!("sections"in r))return new ev(r,t);let n=[],i=[],s=[],o=[],l=[];return function e(t,r,n,i,s,o,l,a,u,c,p){let{sections:f}=t;for(let t=0;t<f.length;t++){let{map:d,offset:h}=f[t],m=c,g=p;if(t+1<f.length){let e=f[t+1].offset;(m=Math.min(c,a+e.line))===c?g=Math.min(p,u+e.column):m<c&&(g=u+e.column)}!function(t,r,n,i,s,o,l,a,u,c,p){let f=ez(t);if("sections"in f)return e(...arguments);let d=new ev(f,r),h=i.length,m=o.length,g=ex(d),{resolvedSources:y,sourcesContent:w,ignoreList:S}=d;if(eb(i,y),eb(o,d.names),w)eb(s,w);else for(let e=0;e<y.length;e++)s.push(null);if(S)for(let e=0;e<S.length;e++)l.push(S[e]+h);for(let e=0;e<g.length;e++){let t=a+e;if(t>c)return;let r=function(e,t){for(let r=e.length;r<=t;r++)e[r]=[];return e[t]}(n,t),i=0===e?u:0,s=g[e];for(let e=0;e<s.length;e++){let n=s[e],o=i+n[0];if(t===c&&o>=p)return;if(1===n.length){r.push([o]);continue}let l=h+n[1],a=n[2],u=n[3];r.push(4===n.length?[o,l,a,u]:[o,l,a,u,m+n[4]])}}}(d,r,n,i,s,o,l,a+h.line,u+h.column,m,g)}}(r,t,n,i,s,o,l,0,0,1/0,1/0),function(e,t){var r;let n=new ev((r=[],{version:e.version,file:e.file,names:e.names,sourceRoot:e.sourceRoot,sources:e.sources,sourcesContent:e.sourcesContent,mappings:r,ignoreList:e.ignoreList||e.x_google_ignoreList}),void 0);return n._decoded=e.mappings,n}({version:3,file:r.file,names:o,sources:i,sourcesContent:s,mappings:n,ignoreList:l})};function ez(e){return"string"==typeof e?JSON.parse(e):e}function eb(e,t){for(let r=0;r<t.length;r++)e.push(t[r])}class ev{constructor(e,t){let r="string"==typeof e;if(!r&&e._decodedMemo)return e;let n=r?JSON.parse(e):e,{version:i,file:s,names:o,sourceRoot:l,sources:a,sourcesContent:u}=n;this.version=i,this.file=s,this.names=o||[],this.sourceRoot=l,this.sources=a,this.sourcesContent=u,this.ignoreList=n.ignoreList||n.x_google_ignoreList||void 0;let c=em(l||"",function(e){if(!e)return"";let t=e.lastIndexOf("/");return e.slice(0,t+1)}(t));this.resolvedSources=a.map(e=>em(e||"",c));let{mappings:p}=n;"string"==typeof p?(this._encoded=p,this._decoded=void 0):(this._encoded=void 0,this._decoded=function(e,t){let r=eg(e,0);if(r===e.length)return e;t||(e=e.slice());for(let i=r;i<e.length;i=eg(e,i+1)){var n;e[i]=(n=e[i],t||(n=n.slice()),n.sort(ey))}return e}(p,r)),this._decodedMemo={lastKey:-1,lastNeedle:-1,lastIndex:-1},this._bySources=void 0,this._bySourceMemos=void 0}}function ex(e){return e._decoded||(e._decoded=function(e){let t=new Int32Array(5),r=[],n=0;do{let i=function(e,t){let r=e.indexOf(";",t);return -1===r?e.length:r}(e,n),s=[],o=!0,l=0;t[0]=0;for(let r=n;r<i;r++){let n;r=en(e,r,t,0);let a=t[0];a<l&&(o=!1),l=a,ei(e,r,i)?(r=en(e,r,t,1),r=en(e,r,t,2),r=en(e,r,t,3),ei(e,r,i)?(r=en(e,r,t,4),n=[a,t[1],t[2],t[3],t[4]]):n=[a,t[1],t[2],t[3]]):n=[a],s.push(n)}o||function(e){e.sort(es)}(s),r.push(s),n=i+1}while(n<=e.length);return r}(e._encoded))}function eO(e,t,r,n){return{source:e,line:t,column:r,name:n}}class e${constructor(e,t){let r=this._map=new eS(e,t);this.file=r.file,this.names=r.names,this.sourceRoot=r.sourceRoot,this.sources=r.resolvedSources,this.sourcesContent=r.sourcesContent,this.version=r.version}originalPositionFor(e){return function(e,t){var r,n,i;let s,{line:o,column:l,bias:a}=t;if(--o<0)throw Error("`line` must be greater than 0 (lines start at line 1)");if(l<0)throw Error("`column` must be greater than or equal to 0 (columns start at column 0)");let u=ex(e);if(o>=u.length)return eO(null,null,null,null);let c=u[o],p=(r=e._decodedMemo,n=o,i=a||1,s=function(e,t,r,n){let{lastKey:i,lastNeedle:s,lastIndex:o}=r,l=0,a=e.length-1;if(n===i){if(t===s)return ew=-1!==o&&e[o][0]===t,o;t>=s?l=-1===o?0:o:a=o}return r.lastKey=n,r.lastNeedle=t,r.lastIndex=function(e,t,r,n){for(;r<=n;){let i=r+(n-r>>1),s=e[i][0]-t;if(0===s)return ew=!0,i;s<0?r=i+1:n=i-1}return ew=!1,r-1}(e,t,l,a)}(c,l,r,n),(ew?s=(-1===i?function(e,t,r){for(let n=r+1;n<e.length&&e[n][0]===t;r=n++);return r}:function(e,t,r){for(let n=r-1;n>=0&&e[n][0]===t;r=n--);return r})(c,l,s):-1===i&&s++,-1===s||s===c.length)?-1:s);if(-1===p)return eO(null,null,null,null);let f=c[p];if(1===f.length)return eO(null,null,null,null);let{names:d,resolvedSources:h}=e;return eO(h[f[1]],f[2]+1,f[3],5===f.length?d[f[4]]:null)}(this._map,e)}sourceContentFor(e,t){let r=function(e,t){let{sourcesContent:r}=e;if(null==r)return null;let n=function(e,t){let{sources:r,resolvedSources:n}=e,i=r.indexOf(t);return -1===i&&(i=n.indexOf(t)),i}(e,t);return -1===n?null:r[n]}(this._map,e);if(null!=r)return r;if(t)return null;throw Error(`"${e}" is not in the SourceMap.`)}}class e_{kind;meta;filename;children;groups;isEndOfPath;constructor(e){this.kind=e?.kind||"stat",this.meta=e?.meta||{},this.filename=e?.filename||"",this.children=new Map,this.groups=[],this.isEndOfPath=!1}}class ek{root;constructor(e){this.root=new e_(e)}insert(e,t){let r=this.root,n=e.split("/").filter(Boolean),i="";for(let e of n)i=i?`${i}/${e}`:e,r.children.has(e)||r.children.set(e,new e_({...t})),(r=r.children.get(e)).filename=i;r.isEndOfPath=!0}mergePrefixSingleDirectory(e=this.root){for(let[t,r]of e.children.entries()){if(r.isEndOfPath)break;if(r.children.size>1){this.mergePrefixSingleDirectory(r);continue}for(let[n,i]of(e.children.delete(t),r.children.entries()))e.children.set(`${t}/${n}`,i),i.isEndOfPath||this.mergePrefixSingleDirectory(i)}}walk(e,t){if(e.children.size){for(let[r,n]of e.children.entries()){let i={...n.meta,label:r,groups:n.groups,filename:n.filename};if(n.isEndOfPath&&delete i.groups,t(i,e),this.walk(n,t),i.groups&&i.groups.length)switch(e.kind){case"stat":i.statSize=i.groups.reduce((e,t)=>e+=t.statSize,0);break;case"source":{let e=i.groups.reduce((e,t)=>(e.parsedSize+=t.parsedSize,e.gzipSize+=t.gzipSize,e.brotliSize+=t.brotliSize,e),{parsedSize:0,gzipSize:0,brotliSize:0});Object.assign(i,e)}}}e.children.clear()}}}let eM=[".mjs",".js",".cjs",".ts",".tsx",".vue",".svelte",".md",".mdx"],eE=process.cwd();function eC(e,t=eE){let n=function(e,t=eE){var r;return r=e,(e=/^\\\\\?\\/.test(r)?r:r.replace(/\\/g,"/")).replace(t,"").replace(/\0/,"")}(e,t);return r.isAbsolute(n)?n.replace("/",""):n}function eI(e,t,r){if(t in r)return r[t].source;throw Error(`[analyzer error]: Missing sourcemap for ${e}.`)}let ej=/\.(c|m)?js$/;async function eR(e,t){let[{byteLength:r},{byteLength:n}]=await Promise.all([t.gzip(e),t.brotli(e)]);return{gzipSize:r,brotliSize:n}}class eA{originalId;filename;label;parsedSize;mapSize;statSize;gzipSize;brotliSize;source;stats;imports;isAsset;isEntry;constructor(e){this.originalId=e,this.filename=e,this.label=e,this.parsedSize=0,this.statSize=0,this.gzipSize=0,this.brotliSize=0,this.mapSize=0,this.source=[],this.stats=[],this.imports=new Set,this.isAsset=!0,this.isEntry=!1}addImports(...e){e.forEach(e=>this.imports.add(e))}async setup(e,t,n,i){let s=new ek({meta:{statSize:0}}),o=new ek({kind:"source",meta:{gzipSize:0,brotliSize:0,parsedSize:0}});if("asset"===e.kind){this.statSize=e.code.byteLength,this.parsedSize=e.code.byteLength;let{brotliSize:t,gzipSize:r}=await eR(e.code,n);this.brotliSize=t,this.gzipSize=r}else{let{code:l,imports:a,dynamicImports:u,map:c,moduleIds:p}=e;for(let n of(this.addImports(...a,...u),this.mapSize=c.length,this.isEntry=e.isEntry,p.length?p.reduce((e,n)=>{let i=t.getModuleInfo(n);return i&&i.code&&(eM.includes(r.extname(i.id))||i.id.startsWith("\0"))&&e.push({id:i.id,code:i.code}),e},[]):function(e){let t=new e$(e);return t.sources.reduce((e,r)=>{if(r){let n=t.sourceContentFor(r,!0);n&&e.push({id:r,code:n})}return e},[])}(c))){"."===n.id[0]&&(n.id=r.resolve(i,n.id));let e=Q(n.code).byteLength;this.statSize+=e,s.insert(eC(n.id,i),{kind:"stat",meta:{statSize:e}})}if(c){let{grouped:e,files:t}=function(e,t,r){let n=new e$(t),i={},s=new Set,o=1,l=0,a="string"==typeof e?e:J.decode(e);for(let e=0;e<a.length;e++,l++){let{source:t}=n.originalPositionFor({line:o,column:l});if(null!=t){let n=r(t),o=a[e];n in i||(i[n]=""),i[n]+=o,s.add(n)}"\n"===a[e]&&(o+=1,l=-1)}return{grouped:i,files:s}}(l,c,e=>{let t=r.relative(i,e);return r.join(i,t)});for(let s in t.size||(e[this.originalId]=l,t.add(this.originalId)),e){if(!eM.includes(r.extname(s)))continue;let t=Q(e[s]),l=t.byteLength;o.insert(eC(s,i),{kind:"source",meta:{parsedSize:l,...await eR(t,n)}})}}}for(let e of(s.mergePrefixSingleDirectory(),s.walk(s.root,(e,t)=>t.groups.push(e)),o.mergePrefixSingleDirectory(),o.walk(o.root,(e,t)=>t.groups.push(e)),this.stats=s.root.groups,this.source=o.root.groups,this.source))this.gzipSize+=e.gzipSize,this.brotliSize+=e.brotliSize,this.parsedSize+=e.parsedSize}}class eN{compressAlorithm;modules;workspaceRoot;pluginContext;chunks;constructor(e={}){this.compressAlorithm=function(e){let{gzip:t,brotli:r}=e;return{gzip:function(e={}){return e=Object.assign(V,e),t=>Y(t,e)}(t),brotli:function(e={}){return e=Object.assign(Z,e),t=>K(t,e)}(r)}}(e),this.modules=[],this.pluginContext=null,this.chunks={},this.workspaceRoot=process.cwd()}installPluginContext(e){this.pluginContext||(this.pluginContext=e)}setupRollupChunks(e){Object.assign(this.chunks,e)}async addModule(e){if("map"===e.fileName.slice(-3))return;let t=function(e,t){if("asset"===e.type)return{code:Q(e.source),filename:e.fileName,kind:"asset"};let r="";if(ej.test(e.fileName)&&("sourcemapFileName"in e&&e.sourcemapFileName&&e.sourcemapFileName in t&&(r=eI(e.fileName,e.sourcemapFileName,t)),!r)){let n=e.fileName+".map";n in t&&(r=eI(e.fileName,n,t))}return{code:Q(e.code),filename:e.fileName,map:r,imports:e.imports,dynamicImports:e.dynamicImports,moduleIds:Object.keys(e.modules),isEntry:e.isEntry,kind:"chunk"}}(e,this.chunks),r=new eA(t.filename);await r.setup(t,this.pluginContext,this.compressAlorithm,this.workspaceRoot),this.modules.push(r)}processModule(){return this.modules.map(e=>{let{originalId:t,imports:r,...n}=e;return{...n,imports:[...r]}})}}async function eP(e,t){let{html:r}=await import("./html.mjs");return r(t.title,function(e,t){let{stringify:r}=JSON;return`var defaultSizes=${r(t)},analyzeModule=${r(e)};`}(e,t.mode))}async function eB(e){let t=()=>Math.floor(64512*Math.random())+1024,r=async e=>new Promise(t=>{let r=c.createServer();r.once("error",e=>{"EADDRINUSE"===e.code?t(!1):t(!0)}),r.once("listening",()=>{r.close(()=>{t(!0)})}),r.listen(e,"localhost")});if(0===e){let e=!1,n=0;for(;!e;)n=t(),e=await r(n);return n}let n=await r(e);if(n)return e;{let t=e+1;for(;;){if(n=await r(t))return t;t++}}}function eL(){let e=[],t={},r=(r,n)=>{let i=new URL(r.url||"",`http://${r.headers.host}`),s=i.pathname||"",o=Object.fromEntries(i.searchParams.entries()),l=[...e],a={req:r,res:n,query:o,params:{}},u=Object.keys(t).find(e=>RegExp(`^${e.replace(/:\w+/g,"\\w+")}$`).test(s));if(u){let e=RegExp(`^${u.replace(/:\w+/g,"(\\w+)")}$`),r=s.match(e);if(r){let e=u.split("/").filter(e=>e.startsWith(":")).map(e=>e.substring(1));a.params=e.reduce((e,t,n)=>(e[t]=r[n+1],e),{})}l.push(t[u])}let c=0,p=()=>{let e=l[c++];e&&e(a,p)};p()};return{use:t=>{e.push(t)},get:(e,r)=>{t[e]=r},listen:(e,t)=>{u.createServer(r).listen(e,t)}}}function eT(e){let t="head"===e.injectTo?/([ \t]*)<\/head>/i:/([ \t]*)<\/body>/i;e.descriptors=Array.isArray(e.descriptors)?e.descriptors:[e.descriptors];let r=e.descriptors.map(e=>e.attrs&&e.attrs.length>0?`<${e.kind} ${e.attrs.join(" ")}>${e.text}</${e.kind}>`:`<${e.kind}>${e.text}</${e.kind}>`);return e.html.replace(t,e=>`${r.join("\n")}${e}`)}class eF{activeStreams=[];serverEventStream(e,t){t.writeHead(200,{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","access-control-allow-origin":"*"}),t.write("retry: 500\n"),t.write(":\n\n"),t.flushHeaders();let r=new a;this.activeStreams.push(r);let n=setInterval(()=>{t.write(":\n\n"),t.flushHeaders()},3e3);r.on("message",e=>{t.write(`event: ${e.event}
data: ${e.data}

`),t.flushHeaders()}),e.on("close",()=>{clearInterval(n),this.removeStream(r),t.end()})}sendEvent(e,t){let r={event:e,data:t};this.activeStreams.forEach(e=>{e.emit("message",r)})}removeStream(e){let t=this.activeStreams.indexOf(e);-1!==t&&this.activeStreams.splice(t,1)}}let eD=["pnpm-workspace.yaml","lerna.json"];function eU(e,n=e){if(function(e){let n=r.join(e,"package.json");return t.existsSync(n)}(e))return e;let i=r.dirname(e);return i&&i!==e?eU(i,n):n}function eW(e,n=eU(e)){if(eD.some(n=>t.existsSync(r.join(e,n)))||function(e){let n=r.join(e,"package.json");if(!function(e){if(!function(e){try{return t.statSync(e,{throwIfNoEntry:!1})}catch{}}(e))return!1;try{return t.accessSync(e,t.constants.R_OK),!0}catch{return!1}}(n))return!1;try{return!!(JSON.parse(t.readFileSync(n,"utf-8"))||{}).workspaces}catch{return!1}}(e))return e;let i=r.dirname(e);return i&&i!==e?eW(i,n):n}function eH(e){let t=["name","generateBundle","closeBundle","api"].reduce((t,r)=>(t[r]=e[r],t),{}),r=process.cwd(),{store:n}=t.api;return{...t,outputOptions:e=>(e.dir&&(r=e.dir),n.analyzerModule.workspaceRoot=eW(r),"sourcemap"in e&&!n.hasSetupSourcemapOption&&(n.lastSourcemapOption="boolean"==typeof e.sourcemap?e.sourcemap:"hidden"===e.sourcemap),"boolean"==typeof e.sourcemap&&e.sourcemap?e.sourcemap=!0:e.sourcemap="hidden",n.hasSetupSourcemapOption=!0,e)}}let eG=!!process.env.CI,eq={analyzerMode:"server",defaultSizes:"stat",summary:!0};function eJ(e){!function(e){let[t,r]=function(e){switch(e){case"linux":if(-1!==l.release().toLocaleLowerCase().indexOf("microsoft"))return["win32","cmd.exe"];return["linux","xdg-open"];case"win32":return["win32","cmd.exe"];case"darwin":return["darwin","open"];default:return[e,"xdg-open"]}}(l.platform());"win32"===t&&(e=["/c","start",'""'].concat(e=e.map(e=>e.replace(/[&^]/g,"^$&")))),o.spawn(r,e)}([e])}let eY=e=>H.dim(H.bold(e+"")),eK=e=>eY(function(e){if(!e)return"0 Bytes";let t=parseInt(`${Math.floor(Math.log(e)/Math.log(1024))}`);return(e/(1<<10*t)).toFixed(2)+" "+["Bytes","KB","MB","GB","TB"][t]}(e)),eV=e=>{let t=e.length,r=e.reduce((e,t)=>(e.gzip+=t.gzipSize,e.parsed+=t.parsedSize,e.map+=t.mapSize,e),{gzip:0,parsed:0,map:0}),n=[r.gzip&&`gzip: ${eK(r.gzip)}`,r.map&&`map: ${eK(r.map)}`].filter(Boolean).join(" | ");return`${eY(t)} chunks of ${eK(r.parsed)} ${n?`(${n})`:""}`},eZ=0;function eQ(e){let s,o,l;let{reportTitle:a="vite-bundle-analyzer"}=e={...eq,...e},u=function(e={}){return new eN(e)}({gzip:e.gzipOptions,brotli:e.brotliOptions}),c={analyzerModule:u,lastSourcemapOption:!1,hasSetupSourcemapOption:!1},p=process.cwd(),f=!0,d=process.cwd(),h="server"===e.analyzerMode||"static"===e.analyzerMode,m="json"===e.analyzerMode||"static"===e.analyzerMode,g=(l=!1,{rs:new n,into(e){l||(this.rs.push(e),this.rs.push(null),o||(o=Q(e)),l=!0)},refresh(){l=!1,this.rs=new n,this.into(o)}});return{name:"vite-bundle-anlyzer",apply:"build",enforce:"post",api:{store:c,processModule:()=>u.processModule()},config:e=>(e.build||(e.build={}),"sourcemap"in e.build&&!c.hasSetupSourcemapOption&&(c.lastSourcemapOption="boolean"==typeof e.build.sourcemap?e.build.sourcemap:"hidden"===e.build.sourcemap,"inline"===e.build.sourcemap&&console.warn("vite-bundle-analyzer: sourcemap option is set to `inline`, it might cause the result inaccurate.")),e.build&&("boolean"==typeof e.build.sourcemap?e.build.sourcemap=!0:e.build.sourcemap="hidden",X(`plugin status is ${e.build.sourcemap?H.green("ok"):H.red("invalid")}`)),c.hasSetupSourcemapOption=!0,e),configResolved(t){if(p=r.resolve(t.root,t.build.outDir??""),s=t.logger,d=eW(t.root),u.workspaceRoot=d,e.summary){let e=t.plugins.find(e=>"vite:reporter"===e.name);if(f=!!e?.writeBundle,e?.writeBundle){let t="function"==typeof e.writeBundle?e.writeBundle:e.writeBundle?.handler,r=async function(...e){await t?.apply(this,e),s.info(eV(u.modules))};"function"!=typeof e.writeBundle?e.writeBundle.handler=r:e.writeBundle=r}}},async generateBundle(e,t){for(let e in u.installPluginContext(this),u.setupRollupChunks(t),t){let r=t[e];await u.addModule(r)}if(!c.lastSourcemapOption)for(let e in t){let r=t[e];if(ej.test(r.fileName)){let e=r.fileName+".map";e in t&&Reflect.deleteProperty(t,e),"chunk"===r.type&&Reflect.deleteProperty(r,"map")}}},async closeBundle(){if("function"==typeof e.analyzerMode){e.analyzerMode(u.processModule());return}e.summary&&!f&&s.info(eV(u.modules));let n=u.processModule();if(eZ++,m){let i="fileName"in e?e.fileName:"stats",s=r.join(p,`${i}.${"json"===e.analyzerMode?"json":"html"}`);if(t.existsSync(s)&&(s=r.join(p,`${i}-${eZ}.${"json"===e.analyzerMode?"json":"html"}`)),"json"===e.analyzerMode)return G.writeFile(s,JSON.stringify(n,null,2),"utf8");let o=await eP(n,{title:a,mode:e.defaultSizes||"stat"});if(await G.writeFile(s,o,"utf8"),g.into(o),"static"===e.analyzerMode&&!e.openAnalyzer)return}if(h){eZ--;let t=await eP(n,{title:a,mode:e.defaultSizes||"stat"});g.into(t);let r=await eB("analyzerPort"in e?"auto"===e.analyzerPort?0:e.analyzerPort||0:8888+eZ),s=eL();s.get("/",e=>{e.res.writeHead(200,{"Content-Type":"text/html; charset=utf8;","content-Encoding":"gzip"}),g.rs.pipe(i.createGzip()).pipe(e.res),g.refresh()}),s.listen(r,()=>{console.log("server run on ",H.hex("#5B45DE")(`http://localhost:${r}`))}),"openAnalyzer"in e&&!e.openAnalyzer||eG||eJ(`http://localhost:${r}`),eZ++}}}}export{eF as S,H as a,eQ as b,eU as c,eH as d,eB as e,eL as f,eT as i,eJ as o,eP as r,eW as s};
