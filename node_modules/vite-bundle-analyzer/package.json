{"name": "vite-bundle-analyzer", "version": "0.18.1", "description": "a modern vite bundle analyzer tool", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "bin": {"analyze": "dist/bin.js"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "devDependencies": {"@eslint-sukka/react": "^6.12.0", "@iconify-json/ph": "^1.1.12", "@jridgewell/source-map": "^0.3.6", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-esm-shim": "^0.1.7", "@rollup/plugin-node-resolve": "^15.2.3", "@stylex-extend/core": "^0.6.0", "@stylex-extend/react": "^0.6.0", "@stylex-extend/vite": "^0.6.0", "@stylexjs/stylex": "^0.9.1", "@svgr/core": "^8.1.0", "@svgr/plugin-jsx": "^8.1.0", "@swc/core": "^1.4.16", "@types/node": "^20.7.0", "@types/react": "^18.2.31", "@types/react-dom": "18.2.7", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.5", "ansis": "^3.3.2", "c8": "^8.0.1", "clsx": "^2.1.1", "dprint": "^0.46.0", "eslint": "^9.16.0", "eslint-config-kagura": "^3.0.1", "foxact": "^0.2.29", "lightningcss": "^1.24.1", "memdisk": "^1.2.1", "mri": "^1.2.0", "preact": "^10.19.2", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^4.13.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-swc3": "^0.11.2", "squarified": "^0.3.5", "tinyexec": "^0.3.1", "tsx": "^4.19.2", "typescript": "^5.2.2", "unplugin-icons": "^0.18.5", "vite": "^6", "vitest": "^2.1.5"}, "license": "MIT", "author": "kanno", "pnpm": {"overrides": {"@types/react": "^18.2.31", "is-core-module": "npm:@nolyfill/is-core-module@^1"}, "patchedDependencies": {"@rollup/plugin-esm-shim@0.1.7": "patches/@<EMAIL>", "@jridgewell/source-map@0.3.6": "patches/@j<PERSON><PERSON><EMAIL>"}}, "files": ["dist", "LICENSE", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/nonzzz/vite-bundle-analyzer.git"}, "keywords": ["vite", "rollup", "rollup-plugin", "vite-plugin", "bundle-analyzer", "rollup-bundle-analyzer"], "packageManager": "pnpm@9.11.0+sha512.0a203ffaed5a3f63242cd064c8fb5892366c103e328079318f78062f24ea8c9d50bc6a47aa3567cabefd824d170e78fa2745ed1f16b132e16436146b7688f19b"}