/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [["path", { d: "M12 20h.01", key: "zekei9" }]];
const WifiZero = createLucideIcon("wifi-zero", __iconNode);

export { __iconNode, WifiZero as default };
//# sourceMappingURL=wifi-zero.js.map
