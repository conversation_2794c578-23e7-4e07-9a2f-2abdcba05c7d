!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).cssTools={})}(this,function(t){"use strict";class e extends Error{reason;filename;line;column;source;constructor(t,e,s,i,n){super(`${t}:${s}:${i}: ${e}`),this.reason=e,this.filename=t,this.line=s,this.column=i,this.source=n}}class s{start;end;source;constructor(t,e,s){this.start=t,this.end=e,this.source=s}}var i;t.CssTypes=void 0,(i=t.CssTypes||(t.CssTypes={})).stylesheet="stylesheet",i.rule="rule",i.declaration="declaration",i.comment="comment",i.container="container",i.charset="charset",i.document="document",i.customMedia="custom-media",i.fontFace="font-face",i.host="host",i.import="import",i.keyframes="keyframes",i.keyframe="keyframe",i.layer="layer",i.media="media",i.namespace="namespace",i.page="page",i.startingStyle="starting-style",i.supports="supports";const n=(t,e,s)=>{let i=s,n=1e4;do{const s=e.map(e=>t.indexOf(e,i));s.push(t.indexOf("\\",i));const r=s.filter(t=>-1!==t);if(0===r.length)return-1;const o=Math.min(...r);if("\\"!==t[o])return o;i=o+2,n--}while(n>0);throw new Error("Too many escaping")},r=(t,e,s)=>{let i=s,o=1e4;do{const s=e.map(e=>t.indexOf(e,i));s.push(t.indexOf("(",i)),s.push(t.indexOf('"',i)),s.push(t.indexOf("'",i)),s.push(t.indexOf("\\",i));const c=s.filter(t=>-1!==t);if(0===c.length)return-1;const a=Math.min(...c);switch(t[a]){case"\\":i=a+2;break;case"(":{const e=r(t,[")"],a+1);if(-1===e)return-1;i=e+1}break;case'"':{const e=n(t,['"'],a+1);if(-1===e)return-1;i=e+1}break;case"'":{const e=n(t,["'"],a+1);if(-1===e)return-1;i=e+1}break;default:return a}o--}while(o>0);throw new Error("Too many escaping")},o=/\/\*[^]*?(?:\*\/|$)/g;function c(t){return t?t.trim():""}function a(t,e){const s=t&&"string"==typeof t.type,i=s?t:e;for(const e in t){const s=t[e];Array.isArray(s)?s.forEach(t=>{a(t,i)}):s&&"object"==typeof s&&a(s,i)}return s&&Object.defineProperty(t,"parent",{configurable:!0,writable:!0,enumerable:!1,value:e||null}),t}class h{level=0;indentation="  ";compress=!1;constructor(t){"string"==typeof t?.indent&&(this.indentation=t?.indent),t?.compress&&(this.compress=!0)}emit(t,e){return t}indent(t){return this.level=this.level||1,t?(this.level+=t,""):Array(this.level).join(this.indentation)}visit(e){switch(e.type){case t.CssTypes.stylesheet:return this.stylesheet(e);case t.CssTypes.rule:return this.rule(e);case t.CssTypes.declaration:return this.declaration(e);case t.CssTypes.comment:return this.comment(e);case t.CssTypes.container:return this.container(e);case t.CssTypes.charset:return this.charset(e);case t.CssTypes.document:return this.document(e);case t.CssTypes.customMedia:return this.customMedia(e);case t.CssTypes.fontFace:return this.fontFace(e);case t.CssTypes.host:return this.host(e);case t.CssTypes.import:return this.import(e);case t.CssTypes.keyframes:return this.keyframes(e);case t.CssTypes.keyframe:return this.keyframe(e);case t.CssTypes.layer:return this.layer(e);case t.CssTypes.media:return this.media(e);case t.CssTypes.namespace:return this.namespace(e);case t.CssTypes.page:return this.page(e);case t.CssTypes.startingStyle:return this.startingStyle(e);case t.CssTypes.supports:return this.supports(e)}}mapVisit(t,e){let s="";e=e||"";for(let i=0,n=t.length;i<n;i++)s+=this.visit(t[i]),e&&i<n-1&&(s+=this.emit(e));return s}compile(t){return this.compress?t.stylesheet.rules.map(this.visit,this).join(""):this.stylesheet(t)}stylesheet(t){return this.mapVisit(t.stylesheet.rules,"\n\n")}comment(t){return this.compress?this.emit("",t.position):this.emit(`${this.indent()}/*${t.comment}*/`,t.position)}container(t){return this.compress?this.emit(`@container ${t.container}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@container ${t.container}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}layer(t){return this.compress?this.emit(`@layer ${t.layer}`,t.position)+(t.rules?this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):";"):this.emit(`${this.indent()}@layer ${t.layer}`,t.position)+(t.rules?this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`):";")}import(t){return this.emit(`@import ${t.import};`,t.position)}media(t){return this.compress?this.emit(`@media ${t.media}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@media ${t.media}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}document(t){const e=`@${t.vendor||""}document ${t.document}`;return this.compress?this.emit(e,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(e,t.position)+this.emit(`  {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`${this.indent(-1)}\n}`)}charset(t){return this.emit(`@charset ${t.charset};`,t.position)}namespace(t){return this.emit(`@namespace ${t.namespace};`,t.position)}startingStyle(t){return this.compress?this.emit("@starting-style",t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@starting-style`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}supports(t){return this.compress?this.emit(`@supports ${t.supports}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@supports ${t.supports}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}keyframes(t){return this.compress?this.emit(`@${t.vendor||""}keyframes ${t.name}`,t.position)+this.emit("{")+this.mapVisit(t.keyframes)+this.emit("}"):this.emit(`@${t.vendor||""}keyframes ${t.name}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.keyframes,"\n")+this.emit(`${this.indent(-1)}}`)}keyframe(t){const e=t.declarations;return this.compress?this.emit(t.values.join(","),t.position)+this.emit("{")+this.mapVisit(e)+this.emit("}"):this.emit(this.indent())+this.emit(t.values.join(", "),t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(e,"\n")+this.emit(`${this.indent(-1)}\n${this.indent()}}\n`)}page(t){if(this.compress){const e=t.selectors.length?t.selectors.join(", "):"";return this.emit(`@page ${e}`,t.position)+this.emit("{")+this.mapVisit(t.declarations)+this.emit("}")}const e=t.selectors.length?`${t.selectors.join(", ")} `:"";return this.emit(`@page ${e}`,t.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(t.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")}fontFace(t){return this.compress?this.emit("@font-face",t.position)+this.emit("{")+this.mapVisit(t.declarations)+this.emit("}"):this.emit("@font-face ",t.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(t.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")}host(t){return this.compress?this.emit("@host",t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit("@host",t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`${this.indent(-1)}\n}`)}customMedia(t){return this.emit(`@custom-media ${t.name} ${t.media};`,t.position)}rule(t){const e=t.declarations;if(!e.length)return"";if(this.compress)return this.emit(t.selectors.join(","),t.position)+this.emit("{")+this.mapVisit(e)+this.emit("}");const s=this.indent();return this.emit(t.selectors.map(t=>s+t).join(",\n"),t.position)+this.emit(" {\n")+this.emit(this.indent(1))+this.mapVisit(e,"\n")+this.emit(this.indent(-1))+this.emit(`\n${this.indent()}}`)}declaration(t){return this.compress?this.emit(`${t.property}:${t.value}`,t.position)+this.emit(";"):"grid-template-areas"===t.property?this.emit(this.indent())+this.emit(t.property+": "+t.value.split("\n").join("\n".padEnd(22)+this.indent()),t.position)+this.emit(";"):this.emit(this.indent())+this.emit(`${t.property}: ${t.value}`,t.position)+this.emit(";")}}const m=(i,n)=>{n=n||{};let h=1,m=1;function u(){const t={line:h,column:m};return e=>(e.position=new s(t,{line:h,column:m},n?.source||""),$(),e)}const p=[];function l(t){const s=new e(n?.source||"",t,h,m,i);if(!n?.silent)throw s;p.push(s)}function f(){const t=/^{\s*/.exec(i);return!!t&&(g(t),!0)}function d(){const t=/^}/.exec(i);return!!t&&(g(t),!0)}function y(){let t;const e=[];for($(),T(e);i.length&&"}"!==i.charAt(0)&&(t=E()||M(),t);)e.push(t),T(e);return e}function g(t){const e=t[0];return function(t){const e=t.match(/\n/g);e&&(h+=e.length);const s=t.lastIndexOf("\n");m=~s?t.length-s:m+t.length}(e),i=i.slice(e.length),t}function $(){const t=/^\s*/.exec(i);t&&g(t)}function T(t){t=t||[];let e=C();for(;e;)t.push(e),e=C();return t}function C(){const e=u();if("/"!==i.charAt(0)||"*"!==i.charAt(1))return;const s=/^\/\*[^]*?\*\//.exec(i);return s?(g(s),e({type:t.CssTypes.comment,comment:s[0].slice(2,-2)})):l("End of comment missing")}function x(){const t=/^([^{]+)/.exec(i);if(!t)return;g(t);return((t,e)=>{const s=[];let i=0;for(;i<t.length;){const n=r(t,e,i);if(-1===n)return s.push(t.substring(i)),s;s.push(t.substring(i,n)),i=n+1}return s})(c(t[0]).replace(o,""),[","]).map(t=>c(t))}function V(){const e=u(),s=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/.exec(i);if(!s)return;g(s);const n=c(s[0]),a=/^:\s*/.exec(i);if(!a)return l("property missing ':'");g(a);let h="";const m=r(i,[";","}"]);if(-1!==m){h=i.substring(0,m);g([h]),h=c(h).replace(o,"")}const p=e({type:t.CssTypes.declaration,property:n.replace(o,""),value:h}),f=/^[;\s]*/.exec(i);return f&&g(f),p}function k(){const t=[];if(!f())return l("missing '{'");T(t);let e=V();for(;e;)t.push(e),T(t),e=V();return d()?t:l("missing '}'")}function v(){const e=[],s=u();let n=/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/.exec(i);for(;n;){const t=g(n);e.push(t[1]);const s=/^,\s*/.exec(i);s&&g(s),n=/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/.exec(i)}if(e.length)return s({type:t.CssTypes.keyframe,values:e,declarations:k()||[]})}const w=O("import"),b=O("charset"),j=O("namespace");function O(t){const e=new RegExp("^@"+t+"\\s*((?::?[^;'\"]|\"(?:\\\\\"|[^\"])*?\"|'(?:\\\\'|[^'])*?')+)(?:;|$)");return()=>{const s=u(),n=e.exec(i);if(!n)return;const r=g(n),o={type:t};return o[t]=r[1].trim(),s(o)}}function E(){if("@"===i[0])return function(){const e=u(),s=/^@([-\w]+)?keyframes\s*/.exec(i);if(!s)return;const n=g(s)[1],r=/^([-\w]+)\s*/.exec(i);if(!r)return l("@keyframes missing name");const o=g(r)[1];if(!f())return l("@keyframes missing '{'");let c=T(),a=v();for(;a;)c.push(a),c=c.concat(T()),a=v();return d()?e({type:t.CssTypes.keyframes,name:o,vendor:n,keyframes:c}):l("@keyframes missing '}'")}()||function(){const e=u(),s=/^@media *([^{]+)/.exec(i);if(!s)return;const n=c(g(s)[1]);if(!f())return l("@media missing '{'");const r=T().concat(y());return d()?e({type:t.CssTypes.media,media:n,rules:r}):l("@media missing '}'")}()||function(){const e=u(),s=/^@custom-media\s+(--\S+)\s+([^{;\s][^{;]*);/.exec(i);if(!s)return;const n=g(s);return e({type:t.CssTypes.customMedia,name:c(n[1]),media:c(n[2])})}()||function(){const e=u(),s=/^@supports *([^{]+)/.exec(i);if(!s)return;const n=c(g(s)[1]);if(!f())return l("@supports missing '{'");const r=T().concat(y());return d()?e({type:t.CssTypes.supports,supports:n,rules:r}):l("@supports missing '}'")}()||w()||b()||j()||function(){const e=u(),s=/^@([-\w]+)?document *([^{]+)/.exec(i);if(!s)return;const n=g(s),r=c(n[1]),o=c(n[2]);if(!f())return l("@document missing '{'");const a=T().concat(y());return d()?e({type:t.CssTypes.document,document:o,vendor:r,rules:a}):l("@document missing '}'")}()||function(){const e=u(),s=/^@page */.exec(i);if(!s)return;g(s);const n=x()||[];if(!f())return l("@page missing '{'");let r=T(),o=V();for(;o;)r.push(o),r=r.concat(T()),o=V();return d()?e({type:t.CssTypes.page,selectors:n,declarations:r}):l("@page missing '}'")}()||function(){const e=u(),s=/^@host\s*/.exec(i);if(!s)return;if(g(s),!f())return l("@host missing '{'");const n=T().concat(y());return d()?e({type:t.CssTypes.host,rules:n}):l("@host missing '}'")}()||function(){const e=u(),s=/^@font-face\s*/.exec(i);if(!s)return;if(g(s),!f())return l("@font-face missing '{'");let n=T(),r=V();for(;r;)n.push(r),n=n.concat(T()),r=V();return d()?e({type:t.CssTypes.fontFace,declarations:n}):l("@font-face missing '}'")}()||function(){const e=u(),s=/^@container *([^{]+)/.exec(i);if(!s)return;const n=c(g(s)[1]);if(!f())return l("@container missing '{'");const r=T().concat(y());return d()?e({type:t.CssTypes.container,container:n,rules:r}):l("@container missing '}'")}()||function(){const e=u(),s=/^@starting-style\s*/.exec(i);if(!s)return;if(g(s),!f())return l("@starting-style missing '{'");const n=T().concat(y());return d()?e({type:t.CssTypes.startingStyle,rules:n}):l("@starting-style missing '}'")}()||function(){const e=u(),s=/^@layer *([^{;@]+)/.exec(i);if(!s)return;const n=c(g(s)[1]);if(!f()){const s=/^[;\s]*/.exec(i);return s&&g(s),e({type:t.CssTypes.layer,layer:n})}const r=T().concat(y());return d()?e({type:t.CssTypes.layer,layer:n,rules:r}):l("@layer missing '}'")}()}function M(){const e=u(),s=x();return s?(T(),e({type:t.CssTypes.rule,selectors:s,declarations:k()||[]})):l("selector missing")}return a(function(){const e=y();return{type:t.CssTypes.stylesheet,stylesheet:{source:n?.source,rules:e,parsingErrors:p}}}())},u=(t,e)=>new h(e||{}).compile(t);var p={parse:m,stringify:u};t.default=p,t.parse=m,t.stringify=u,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=adobe-css-tools.js.map
