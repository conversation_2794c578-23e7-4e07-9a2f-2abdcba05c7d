/**
 * Custom Hook for Application Event Handlers
 * 
 * Centralizes all event handling logic for the main application component.
 * Provides consistent behavior across table implementations and maintains 
 * proper state management patterns.
 * 
 * Vision and Design by <PERSON><PERSON><PERSON><PERSON> | Built by Figma Make - Claude Sonnet
 */

import { useCallback } from 'react';
import { Customer, Vehicle, Deal } from '../types';
import { 
  createDealOpportunities, createVehicleDeal, generateCustomerId, generateDealId,
  getCustomerMatchCount, getVehicleMatchCount, getReservedCustomer 
} from '../utils/helpers';
import { toast } from 'sonner@2.0.3';
import { LucideIcon, Users, Car } from 'lucide-react';

interface UseAppHandlersProps {
  inventory: Vehicle[];
  customers: Customer[];
  deals: Deal[];
  setDeals: React.Dispatch<React.SetStateAction<Deal[]>>;
  setCustomers: React.Dispatch<React.SetStateAction<Customer[]>>;
  setInventory: React.Dispatch<React.SetStateAction<Vehicle[]>>;
  setSelectedVehicle: React.Dispatch<React.SetStateAction<Vehicle | null>>;
  setSelectedCustomer: React.Dispatch<React.SetStateAction<Customer | null>>;
  setSelectedCustomerForDetails: React.Dispatch<React.SetStateAction<Customer | null>>;
  setSelectedVehicleForDetails: React.Dispatch<React.SetStateAction<Vehicle | null>>;
  setCurrentView: React.Dispatch<React.SetStateAction<string>>;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
  setMatchingPanelOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setVehicleMatchingPanelOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setDeleteConfirmOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setItemToDelete: React.Dispatch<React.SetStateAction<Customer | Vehicle | null>>;
  setDeleteType: React.Dispatch<React.SetStateAction<'customer' | 'vehicle' | ''>>;
  setTriggerIcon: React.Dispatch<React.SetStateAction<LucideIcon | null>>;
  setTriggerAction: React.Dispatch<React.SetStateAction<string>>;
}

export function useAppHandlers({
  inventory,
  customers, 
  deals,
  setDeals,
  setCustomers,
  setInventory,
  setSelectedVehicle,
  setSelectedCustomer,
  setSelectedCustomerForDetails,
  setSelectedVehicleForDetails,
  setCurrentView,
  setActiveTab,
  setMatchingPanelOpen,
  setVehicleMatchingPanelOpen,
  setDeleteConfirmOpen,
  setItemToDelete,
  setDeleteType,
  setTriggerIcon,
  setTriggerAction
}: UseAppHandlersProps) {

  // ===== TABLE HANDLERS =====
  const tableHandlers = {
    // Vehicle operations
    handleVehicleClick: useCallback((vehicle: Vehicle) => {
      setSelectedVehicleForDetails(vehicle);
      setCurrentView('vehicle-details');
    }, [setSelectedVehicleForDetails, setCurrentView]),

    handleMatchCustomers: useCallback((vehicle: Vehicle) => {
      setSelectedVehicle(vehicle);
      setTriggerIcon(Users);
      setTriggerAction('Match Customers');
      setMatchingPanelOpen(true);
    }, [setSelectedVehicle, setTriggerIcon, setTriggerAction, setMatchingPanelOpen]),
    
    // Customer operations
    handleCustomerNameClick: useCallback((customer: Customer) => {
      setSelectedCustomerForDetails(customer);
      setCurrentView('customer-details');
    }, [setSelectedCustomerForDetails, setCurrentView]),

    handleMatchVehicles: useCallback((customer: Customer) => {
      setSelectedCustomer(customer);
      setTriggerIcon(Car);
      setTriggerAction('Match Vehicles');
      setVehicleMatchingPanelOpen(true);
    }, [setSelectedCustomer, setTriggerIcon, setTriggerAction, setVehicleMatchingPanelOpen]),
    
    // CRUD operations
    handleDeleteClick: useCallback((item: Customer | Vehicle, type: 'customer' | 'vehicle') => {
      setItemToDelete(item);
      setDeleteType(type);
      setDeleteConfirmOpen(true);
    }, [setItemToDelete, setDeleteType, setDeleteConfirmOpen]),
    
    // Business intelligence helper functions
    getCustomerMatchCount: useCallback((vehicleId: number) => getCustomerMatchCount(vehicleId, deals), [deals]),
    getVehicleMatchCount: useCallback((customerId: string) => getVehicleMatchCount(customerId, deals), [deals]),
    getReservedCustomer: useCallback((vehicleId: number) => getReservedCustomer(vehicleId, deals, customers), [deals, customers])
  };

  // ===== DEAL HANDLERS =====
  const dealHandlers = {
    handleDealClick: useCallback((deal: Deal) => {
      toast.info(`Opening deal details for ${deal.customerName}`, {
        description: `Deal ID: ${deal.id} • Status: ${deal.status} • ${deal.approvalEntities.length} approval entities`,
        duration: 3000
      });
    }, []),

    handleEditDeal: useCallback((deal: Deal) => {
      toast.info(`Edit deal functionality coming soon`, {
        description: `Deal ${deal.id} for ${deal.customerName}`,
        duration: 3000
      });
    }, []),

    handleDeleteDeal: useCallback((deal: Deal) => {
      toast.warning(`Delete deal confirmation required`, {
        description: `This will permanently delete deal ${deal.id} for ${deal.customerName}`,
        duration: 4000
      });
    }, []),

    handleContactCustomer: useCallback((deal: Deal) => {
      toast.success(`Contacting ${deal.customerName}`, {
        description: `Phone: ${deal.customerPhone} • Email: ${deal.customerEmail}`,
        duration: 4000
      });
    }, [])
  };

  // ===== NAVIGATION HANDLERS =====
  const handleTabChange = useCallback((newTab: string): void => {
    setCurrentView('dashboard');
    setSelectedCustomerForDetails(null);
    setSelectedVehicleForDetails(null);
    setActiveTab(newTab);
  }, [setCurrentView, setSelectedCustomerForDetails, setSelectedVehicleForDetails, setActiveTab]);

  const handleBackToCustomers = useCallback((): void => {
    setCurrentView('dashboard');
    setActiveTab('customers');
    setSelectedCustomerForDetails(null);
  }, [setCurrentView, setActiveTab, setSelectedCustomerForDetails]);

  const handleBackToInventory = useCallback((): void => {
    setCurrentView('dashboard');
    setActiveTab('inventory');
    setSelectedVehicleForDetails(null);
  }, [setCurrentView, setActiveTab, setSelectedVehicleForDetails]);

  const handleBackToDeals = useCallback((): void => {
    setCurrentView('dashboard');
    setActiveTab('deals');
  }, [setCurrentView, setActiveTab]);

  const handleAddCustomer = useCallback((): void => {
    setCurrentView('add-customer');
  }, [setCurrentView]);

  const handleAddVehicle = useCallback((): void => {
    setCurrentView('add-vehicle');
  }, [setCurrentView]);

  const handleAddDeal = useCallback((): void => {
    setCurrentView('add-deal');
  }, [setCurrentView]);

  const handleFloatingDealBuilder = useCallback((): void => {
    setCurrentView('add-deal');
    setActiveTab('deals');
    
    toast.success(
      'Deal Builder opened!',
      {
        description: 'Start creating a new deal with the 5-step guided process.',
        duration: 3000,
      }
    );
  }, [setCurrentView, setActiveTab]);

  // ===== BUSINESS LOGIC HANDLERS =====
  const handleSaveMatches = useCallback((vehicleId: number, selectedCustomerIds: string[]): void => {
    const vehicle = inventory.find(v => v.id === vehicleId);
    if (!vehicle) return;

    const newOpportunities = createDealOpportunities(vehicle, customers, selectedCustomerIds);
    setDeals(prev => [...prev, ...newOpportunities]);

    toast.success(
      `Successfully created ${selectedCustomerIds.length} deal${selectedCustomerIds.length === 1 ? '' : 's'}!`,
      {
        description: `Matched ${vehicle.year} ${vehicle.make} ${vehicle.model} with ${selectedCustomerIds.length} customer${selectedCustomerIds.length === 1 ? '' : 's'}.`,
        duration: 4000,
      }
    );
  }, [inventory, customers, setDeals]);

  const handleSaveVehicleMatches = useCallback((customerId: string, selectedVehicleIds: number[]): void => {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) return;

    const newDeal = createVehicleDeal(customer, inventory, selectedVehicleIds);
    setDeals(prev => [...prev, newDeal]);

    toast.success(
      `Deal created successfully!`,
      {
        description: `Created deal for ${customer.name} with ${selectedVehicleIds.length} vehicle option${selectedVehicleIds.length === 1 ? '' : 's'}.`,
        duration: 4000,
      }
    );
  }, [customers, inventory, setDeals]);

  // ===== FORM SUBMISSION HANDLERS =====
  const handleCustomerSubmit = useCallback((customerData: Partial<Customer>): void => {
    const newId = generateCustomerId(customers.length);
    const newCustomer: Customer = {
      ...customerData as Customer,
      id: newId,
      joinDate: new Date().toISOString().split('T')[0],
      lastContact: new Date().toISOString().split('T')[0],
      lifetimeValue: '$0',
      totalPurchases: 0
    };
    
    setCustomers(prev => [...prev, newCustomer]);
    setCurrentView('dashboard');
    setActiveTab('customers');

    toast.success(
      `Customer "${customerData.name}" created successfully!`,
      {
        description: `New customer added to ${customerData.assignedDealer} with ID ${newId}.`,
        duration: 4000,
      }
    );
  }, [customers.length, setCustomers, setCurrentView, setActiveTab]);

  const handleVehicleSubmit = useCallback((vehicleData: any): void => {
    const newVehicle: Vehicle = {
      ...vehicleData,
      exterior: vehicleData.exteriorColor,
      interior: vehicleData.interiorColor,
      mileage: vehicleData.mileage
    };
    
    setInventory(prev => [...prev, newVehicle]);
    setCurrentView('dashboard');
    setActiveTab('inventory');

    toast.success(
      `Vehicle "${vehicleData.year} ${vehicleData.make} ${vehicleData.model}" added successfully!`,
      {
        description: `New vehicle added to inventory at ${vehicleData.location} (VIN: ${vehicleData.vin}).`,
        duration: 4000,
      }
    );
  }, [setInventory, setCurrentView, setActiveTab]);

  const handleDealSubmit = useCallback((dealData: any): void => {
    const newId = generateDealId();
    const newDeal: Deal = {
      id: newId,
      type: 'deal',
      customerId: generateCustomerId(customers.length),
      customerName: dealData.customerName,
      customerPhoto: dealData.customerPhoto || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
      customerType: dealData.customerType,
      customerEmail: dealData.email,
      customerPhone: dealData.phone,
      customerLocation: `${dealData.city}, ${dealData.province}`,
      approvalEntities: dealData.approvalEntities || [],
      vehicleOptions: [],
      status: 'Pending',
      priority: 'Medium',
      salesRep: dealData.salesRep || 'John Smith',
      createdDate: new Date().toISOString().split('T')[0],
      lastContact: null,
      selectedVehicle: null,
      notes: dealData.dealNotes || `Deal created for ${dealData.customerName} - Customer type: ${dealData.customerType}`
    };

    setDeals(prev => [...prev, newDeal]);
    setCurrentView('dashboard');
    setActiveTab('deals');

    const entityCount = dealData.approvalEntities?.length || 0;
    const entityText = entityCount === 1 ? '1 approval entity' : `${entityCount} approval entities`;

    toast.success(
      `Deal created successfully!`,
      {
        description: `New deal started for ${dealData.customerName} with ${entityText} and ${dealData.selectedVehicles?.length || 0} vehicle options.`,
        duration: 4000,
      }
    );
  }, [customers.length, setDeals, setCurrentView, setActiveTab]);

  return {
    tableHandlers,
    dealHandlers,
    handleTabChange,
    handleBackToCustomers,
    handleBackToInventory,
    handleBackToDeals,
    handleAddCustomer,
    handleAddVehicle,
    handleAddDeal,
    handleFloatingDealBuilder,
    handleSaveMatches,
    handleSaveVehicleMatches,
    handleCustomerSubmit,
    handleVehicleSubmit,
    handleDealSubmit
  };
}