@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@custom-variant dark (&:is(.dark *));

/**
 * SubPrime Pro - Streamlined Design System
 * Optimized for Deal Builder Focus application - 1366x768 resolution
 * Material Design 3 with orange brand palette
 */

:root {
  /* Typography - Optimized for 1366x768 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --font-family-base: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --line-height-normal: 1.4;

  /* Material Design 3 Core Colors */
  --primary-0: #000000;
  --primary-10: #3c1d01;
  --primary-20: #b44600;
  --primary-30: #ca5200;
  --primary-40: #da5a00;
  --primary-50: #f16700;
  --primary-60: #f37a35;
  --primary-70: #f6af8b;
  --primary-80: #facfbb;
  --primary-90: #ffdecd;
  --primary-95: #ffeee5;
  --primary-99: #fffbfe;
  --primary-100: #ffffff;

  --neutral-0: #000000;
  --neutral-10: #1d1b20;
  --neutral-20: #322f35;
  --neutral-30: #48464c;
  --neutral-40: #605d64;
  --neutral-50: #79767d;
  --neutral-60: #938f96;
  --neutral-70: #aea9b1;
  --neutral-80: #cac5cd;
  --neutral-90: #e6e0e9;
  --neutral-95: #f5eff7;
  --neutral-99: #fffbff;
  --neutral-100: #ffffff;

  --neutral-variant-30: #49454f;
  --neutral-variant-40: #605d66;
  --neutral-variant-50: #79747e;
  --neutral-variant-60: #938f99;
  --neutral-variant-70: #aea9b4;
  --neutral-variant-80: #cac4d0;
  --neutral-variant-90: #e7e0ec;

  --error-40: #b3261e;
  --error-50: #dc362e;
  --error-80: #f2b8b5;
  --error-90: #f9dedc;
  --error-99: #fffbf9;

  /* Semantic Colors */
  --background: var(--neutral-99);
  --foreground: var(--neutral-10);
  --card: var(--neutral-100);
  --card-foreground: var(--neutral-10);
  --popover: var(--neutral-100);
  --popover-foreground: var(--neutral-10);
  --primary: var(--primary-40);
  --primary-foreground: var(--primary-100);
  --secondary: var(--neutral-90);
  --secondary-foreground: var(--neutral-20);
  --muted: var(--neutral-95);
  --muted-foreground: var(--neutral-50);
  --accent: var(--neutral-95);
  --accent-foreground: var(--neutral-20);
  --destructive: var(--error-50);
  --destructive-foreground: var(--error-99);
  --border: var(--neutral-variant-80);
  --input: transparent;
  --input-background: var(--neutral-100);
  --ring: var(--primary-50);
  --radius: 0.5rem;

  /* Surface Colors */
  --surface: var(--neutral-99);
  --surface-bright: var(--neutral-100);
  --surface-container-lowest: var(--neutral-100);
  --surface-container-low: var(--neutral-95);
  --surface-container: var(--neutral-90);
  --on-surface: var(--neutral-10);
  --on-surface-variant: var(--neutral-variant-30);
  --outline: var(--neutral-variant-50);
  --outline-variant: var(--neutral-variant-80);

  /* Brand Colors */
  --brand-orange: var(--primary-50);
  --brand-orange-light: var(--primary-60);
  --brand-orange-dark: var(--primary-30);

  /* Form Colors */
  --input-text: var(--neutral-10);
  --input-placeholder: var(--neutral-variant-40);
  --input-label: var(--neutral-variant-30);

  /* Animation */
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 200ms;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);

  /* Interactive States */
  --button-primary-idle: var(--primary-50);
  --button-primary-hover: var(--primary-60);
  --button-primary-active: var(--primary-40);

  --input-idle-border: var(--neutral-variant-80);
  --input-hover-border: var(--neutral-variant-70);
  --input-focus-border: var(--primary-50);

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);

  --shadow-orange-sm: 0 2px 4px rgba(241, 103, 0, 0.15);
  --shadow-orange-md: 0 4px 8px rgba(241, 103, 0, 0.15);
  --shadow-orange-lg: 0 8px 16px rgba(241, 103, 0, 0.15);

  /* Focus */
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
  --focus-ring-color: rgba(241, 103, 0, 0.3);

  /* Hover Effects */
  --hover-translate-y: -1px;
  --hover-scale-sm: 1.01;
  --click-scale: 0.98;

  /* Notes Panel */
  --notes-panel-bg: rgba(20, 18, 24, 0.95);
  --notes-panel-border: rgba(73, 69, 79, 0.6);
  --notes-panel-header-bg: rgba(30, 27, 35, 0.98);
  --notes-panel-content-bg: rgba(20, 18, 24, 0.95);
}

.dark {
  --background: var(--neutral-10);
  --foreground: var(--neutral-95);
  --card: var(--neutral-10);
  --card-foreground: var(--neutral-95);
  --primary: var(--primary-80);
  --primary-foreground: var(--primary-20);
  --secondary: var(--neutral-30);
  --secondary-foreground: var(--neutral-90);
  --muted: var(--neutral-20);
  --muted-foreground: var(--neutral-60);
  --border: var(--neutral-variant-30);
  --input-background: var(--neutral-20);
  --ring: var(--primary-60);
  --brand-orange: var(--primary-60);
}

@theme inline {
  --text-xs: var(--font-size-xs);
  --text-sm: var(--font-size-sm);
  --text-base: var(--font-size-base);
  --text-lg: var(--font-size-lg);
  --text-xl: var(--font-size-xl);
  --text-2xl: var(--font-size-2xl);
  --text-3xl: var(--font-size-3xl);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-ring: var(--ring);
  --radius-sm: calc(var(--radius) - 2px);
  --radius-md: var(--radius);
  --radius-lg: calc(var(--radius) + 2px);

  --color-brand-orange: var(--brand-orange);
  --color-brand-orange-light: var(--brand-orange-light);
  --color-brand-orange-dark: var(--brand-orange-dark);

  --color-primary-50: var(--primary-50);
  --color-primary-90: var(--primary-90);
  --color-primary-95: var(--primary-95);
  --color-primary-99: var(--primary-99);

  --color-surface: var(--surface);
  --color-surface-bright: var(--surface-bright);
  --color-surface-container-lowest: var(--surface-container-lowest);
  --color-surface-container-low: var(--surface-container-low);
  --color-on-surface: var(--on-surface);
  --color-outline: var(--outline);
  --color-outline-variant: var(--outline-variant);
}

@layer base {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    font-size: var(--font-size-base);
    font-family: var(--font-family-base);
    font-display: swap;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    min-font-size: var(--font-size-xs);
  }

  button, input, select, textarea {
    font-family: inherit;
  }
}

/**
 * INTERACTIVE STATES SYSTEM
 */

/* Button States */
button,
.btn,
[role="button"] {
  position: relative;
  transition: all var(--animation-duration-normal) var(--animation-easing);
  cursor: pointer;
  user-select: none;
  
  &:focus {
    outline: none;
  }
  
  &:focus-visible {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  &:active:not(:disabled) {
    transform: scale(var(--click-scale));
    transition-duration: var(--animation-duration-fast);
  }
}

/* Primary Button States */
.bg-brand-orange {
  background-color: var(--button-primary-idle);
  color: white;
  box-shadow: var(--shadow-orange-sm);
  
  &:hover:not(:disabled) {
    background-color: var(--button-primary-hover);
    box-shadow: var(--shadow-orange-md);
    transform: translateY(var(--hover-translate-y));
  }
  
  &:active:not(:disabled) {
    background-color: var(--button-primary-active);
    transform: scale(var(--click-scale));
  }
}

/* Form Element States */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="date"],
textarea,
select,
.form-input {
  background-color: var(--input-background);
  border: 1px solid var(--input-idle-border);
  color: var(--input-text);
  transition: all var(--animation-duration-normal) var(--animation-easing);
  
  &::placeholder {
    color: var(--input-placeholder);
    opacity: 1;
  }
  
  &:hover:not(:disabled):not(:focus) {
    border-color: var(--input-hover-border);
    box-shadow: var(--shadow-xs);
  }
  
  &:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 var(--focus-ring-width) var(--focus-ring-color);
    outline: none;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
}

/* Card States */
.card {
  background-color: var(--card);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all var(--animation-duration-normal) var(--animation-easing);
}

.card-interactive {
  cursor: pointer;
  
  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(var(--hover-translate-y));
  }
  
  &:active {
    transform: scale(var(--click-scale));
    box-shadow: var(--shadow-sm);
  }
}

/**
 * NOTES PANEL DARK THEME
 */

.notes-panel-overlay {
  z-index: 9999;
}

.notes-panel-container {
  background: var(--notes-panel-bg);
  border-left: 1px solid var(--notes-panel-border);
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(12px);
}

.notes-panel-header {
  background: var(--notes-panel-header-bg);
  border-bottom: 1px solid var(--notes-panel-border);
}

.notes-panel-content {
  background: var(--notes-panel-content-bg);
}

.notes-textarea {
  background: rgba(29, 27, 32, 0.8) !important;
  border: 1px solid rgba(73, 69, 79, 0.4) !important;
  color: #ffffff !important;
  
  &::placeholder {
    color: rgba(147, 143, 153, 0.7) !important;
  }
  
  &:focus {
    border-color: rgba(241, 103, 0, 0.6) !important;
    box-shadow: 0 0 0 2px rgba(241, 103, 0, 0.15) !important;
  }
}

/**
 * TOOLTIP SYSTEM
 */

[data-radix-tooltip-content],
.tooltip-content,
[role="tooltip"] {
  background-color: var(--neutral-10) !important;
  color: var(--primary-95) !important;
  border: 1px solid var(--neutral-20) !important;
  font-family: var(--font-family-base) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  padding: 8px 12px !important;
  border-radius: 8px !important;
  box-shadow: var(--shadow-lg) !important;
  z-index: 9999 !important;
  max-width: 280px !important;
  animation: tooltip-fade-in var(--animation-duration-fast) var(--animation-easing);
}

@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/**
 * LOADING STATES
 */

.loading-spinner {
  animation: spin 1s linear infinite;
  color: var(--brand-orange);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/**
 * STREAMLINED UTILITY CLASSES
 */

/* Core interactive utilities */
.hover-scale-sm:hover {
  transform: scale(var(--hover-scale-sm));
}

.click-scale:active {
  transform: scale(var(--click-scale));
}

.focus-orange:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

/* Shadow utilities */
.shadow-orange-sm {
  box-shadow: var(--shadow-orange-sm);
}

.shadow-orange-md {
  box-shadow: var(--shadow-orange-md);
}

.shadow-orange-lg {
  box-shadow: var(--shadow-orange-lg);
}

/* Badge styling - consistent outline style */
.badge-orange {
  background-color: var(--primary-95);
  color: var(--primary-50);
  border: 1px solid var(--primary-80);
}

/* Transition utility */
.transition-transform {
  transition: transform var(--animation-duration-normal) var(--animation-easing);
}

/* Skip Link for Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-50);
  color: white;
  padding: 8px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  z-index: 9999;
  transition: top var(--animation-duration-fast) var(--animation-easing);
  
  &:focus {
    top: 6px;
  }
}

/**
 * RESPONSIVE DESIGN - 1366x768 Optimized
 */

@media (max-width: 768px) {
  :root {
    --hover-translate-y: 0px;
    --hover-scale-sm: 1;
  }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .notes-panel-container {
    backdrop-filter: none;
  }
}

@media (prefers-contrast: high) {
  :root {
    --focus-ring-width: 3px;
    --focus-ring-color: rgba(241, 103, 0, 0.8);
  }
}