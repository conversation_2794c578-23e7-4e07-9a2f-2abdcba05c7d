function TonalPalettes() {
  return (
    <div className="relative size-full" data-name=".Tonal palettes">
      <div className="absolute bottom-[97.108%] font-['Montserrat:Medium',_sans-serif] font-medium leading-[0] left-[15.005%] right-[72.562%] text-[#201b12] text-[16px] text-left text-nowrap top-0 tracking-[0.15px]">
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          Tonal Palettes
        </p>
      </div>
      <div className="absolute bottom-[86.747%] font-['Montserrat:Regular',_sans-serif] font-normal leading-[0] left-0 right-[93.784%] text-[#201b12] text-[14px] text-left text-nowrap top-[10.843%] tracking-[0.25px]">
        <p className="adjustLetterSpacing block leading-[20px] whitespace-pre">
          Primary
        </p>
      </div>
      <div
        className="absolute bg-[#000000] bottom-[77.349%] left-[15.005%] right-[78.457%] top-[9.639%]"
        data-name="primary0"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[17.685%] right-[81.243%] text-[#ffffff] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          0
        </p>
      </div>
      <div
        className="absolute bg-[#3c1d01] bottom-[77.349%] left-[21.543%] right-[71.918%] top-[9.639%]"
        data-name="primary10"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[23.848%] right-[74.116%] text-[#ffffff] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          10
        </p>
      </div>
      <div
        className="absolute bg-[#b44600] bottom-[77.349%] left-[28.081%] right-[65.38%] top-[9.639%]"
        data-name="primary20"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[30.279%] right-[67.685%] text-[#ffffff] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          20
        </p>
      </div>
      <div
        className="absolute bg-[#ca5200] bottom-[77.349%] left-[34.62%] right-[58.842%] top-[9.639%]"
        data-name="primary30"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[36.817%] right-[61.147%] text-[#ffffff] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          30
        </p>
      </div>
      <div
        className="absolute bg-[#da5a00] bottom-[77.349%] left-[41.158%] right-[52.304%] top-[9.639%]"
        data-name="primary40"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[43.301%] right-[54.662%] text-[#ffffff] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          40
        </p>
      </div>
      <div
        className="absolute bg-[#f16700] bottom-[77.349%] left-[47.696%] right-[45.766%] top-[9.639%]"
        data-name="primary50"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[49.839%] right-[48.124%] text-[#ffffff] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          50
        </p>
      </div>
      <div
        className="absolute bg-[#f37a35] bottom-[77.349%] left-[54.234%] right-[39.228%] top-[9.639%]"
        data-name="primary60"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[56.377%] right-[41.586%] text-[#000000] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          60
        </p>
      </div>
      <div
        className="absolute bg-[#f6af8b] bottom-[77.349%] left-[60.772%] right-[32.69%] top-[9.639%]"
        data-name="primary70"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[62.969%] right-[34.995%] text-[#000000] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          70
        </p>
      </div>
      <div
        className="absolute bg-[#facfbb] bottom-[77.349%] left-[67.31%] right-[26.152%] top-[9.639%]"
        data-name="primary80"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[69.507%] right-[28.457%] text-[#000000] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          80
        </p>
      </div>
      <div
        className="absolute bg-[#ffdecd] bottom-[77.349%] left-[73.848%] right-[19.614%] top-[9.639%]"
        data-name="primary90"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[75.991%] right-[21.972%] text-[#000000] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          90
        </p>
      </div>
      <div
        className="absolute bg-[#ffeee5] bottom-[77.349%] left-[80.386%] right-[13.076%] top-[9.639%]"
        data-name="primary95"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[82.637%] right-[15.327%] text-[#000000] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          95
        </p>
      </div>
      <div
        className="absolute bg-[#fffbfe] bottom-[77.349%] left-[86.924%] right-[6.538%] top-[9.639%]"
        data-name="primary99"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[89.175%] right-[8.789%] text-[#000000] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          99
        </p>
      </div>
      <div
        className="absolute bg-[#ffffff] bottom-[77.349%] left-[93.462%] right-0 top-[9.639%]"
        data-name="primary100"
      />
      <div
        className="absolute bottom-[86.265%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[95.177%] right-[1.822%] text-[#000000] text-[16px] text-left text-nowrap top-[10.843%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          100
        </p>
      </div>
      <div className="absolute bottom-[71.325%] font-['Montserrat:Regular',_sans-serif] font-normal leading-[0] left-0 right-[91.747%] text-[#201b12] text-[14px] text-left text-nowrap top-[26.265%] tracking-[0.25px]">
        <p className="adjustLetterSpacing block leading-[20px] whitespace-pre">
          Secondary
        </p>
      </div>
      <div
        className="absolute bg-[#000000] bottom-[61.928%] left-[15.005%] right-[78.457%] top-[25.06%]"
        data-name="secondary0"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[17.685%] right-[81.243%] text-[#ffffff] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          0
        </p>
      </div>
      <div
        className="absolute bg-[#1d192b] bottom-[61.928%] left-[21.543%] right-[71.918%] top-[25.06%]"
        data-name="secondary10"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[23.848%] right-[74.116%] text-[#ffffff] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          10
        </p>
      </div>
      <div
        className="absolute bg-[#332d41] bottom-[61.928%] left-[28.081%] right-[65.38%] top-[25.06%]"
        data-name="secondary20"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[30.279%] right-[67.685%] text-[#ffffff] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          20
        </p>
      </div>
      <div
        className="absolute bg-[#4a4458] bottom-[61.928%] left-[34.62%] right-[58.842%] top-[25.06%]"
        data-name="secondary30"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[36.817%] right-[61.147%] text-[#ffffff] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          30
        </p>
      </div>
      <div
        className="absolute bg-[#625b71] bottom-[61.928%] left-[41.158%] right-[52.304%] top-[25.06%]"
        data-name="secondary40"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[43.301%] right-[54.662%] text-[#ffffff] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          40
        </p>
      </div>
      <div
        className="absolute bg-[#7a7289] bottom-[61.928%] left-[47.696%] right-[45.766%] top-[25.06%]"
        data-name="secondary50"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[49.839%] right-[48.124%] text-[#ffffff] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          50
        </p>
      </div>
      <div
        className="absolute bg-[#958da5] bottom-[61.928%] left-[54.234%] right-[39.228%] top-[25.06%]"
        data-name="secondary60"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[56.377%] right-[41.586%] text-[#000000] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          60
        </p>
      </div>
      <div
        className="absolute bg-[#b0a7c0] bottom-[61.928%] left-[60.772%] right-[32.69%] top-[25.06%]"
        data-name="secondary70"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[62.969%] right-[34.995%] text-[#000000] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          70
        </p>
      </div>
      <div
        className="absolute bg-[#ccc2dc] bottom-[61.928%] left-[67.31%] right-[26.152%] top-[25.06%]"
        data-name="secondary80"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[69.507%] right-[28.457%] text-[#000000] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          80
        </p>
      </div>
      <div
        className="absolute bg-[#e8def8] bottom-[61.928%] left-[73.848%] right-[19.614%] top-[25.06%]"
        data-name="secondary90"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[75.991%] right-[21.972%] text-[#000000] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          90
        </p>
      </div>
      <div
        className="absolute bg-[#f6edff] bottom-[61.928%] left-[80.386%] right-[13.076%] top-[25.06%]"
        data-name="secondary95"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[82.637%] right-[15.327%] text-[#000000] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          95
        </p>
      </div>
      <div
        className="absolute bg-[#fffbfe] bottom-[61.928%] left-[86.924%] right-[6.538%] top-[25.06%]"
        data-name="secondary99"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[89.175%] right-[8.789%] text-[#000000] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          99
        </p>
      </div>
      <div
        className="absolute bg-[#ffffff] bottom-[61.928%] left-[93.462%] right-0 top-[25.06%]"
        data-name="secondary100"
      />
      <div
        className="absolute bottom-[70.843%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[95.177%] right-[1.822%] text-[#000000] text-[16px] text-left text-nowrap top-[26.265%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          100
        </p>
      </div>
      <div className="absolute bottom-[55.904%] font-['Montserrat:Regular',_sans-serif] font-normal leading-[0] left-0 right-[94.105%] text-[#201b12] text-[14px] text-left text-nowrap top-[41.687%] tracking-[0.25px]">
        <p className="adjustLetterSpacing block leading-[20px] whitespace-pre">
          Tertiary
        </p>
      </div>
      <div
        className="absolute bg-[#000000] bottom-[46.506%] left-[15.005%] right-[78.457%] top-[40.482%]"
        data-name="tertiary0"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[17.685%] right-[81.243%] text-[#ffffff] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          0
        </p>
      </div>
      <div
        className="absolute bg-[#252525] bottom-[46.506%] left-[21.543%] right-[71.918%] top-[40.482%]"
        data-name="tertiary10"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[23.848%] right-[74.116%] text-[#ffffff] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          10
        </p>
      </div>
      <div
        className="absolute bg-[#3e3e3e] bottom-[46.506%] left-[28.081%] right-[65.38%] top-[40.482%]"
        data-name="tertiary20"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[30.279%] right-[67.685%] text-[#ffffff] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          20
        </p>
      </div>
      <div
        className="absolute bg-[#565656] bottom-[46.506%] left-[34.62%] right-[58.842%] top-[40.482%]"
        data-name="tertiary30"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[36.817%] right-[61.147%] text-[#ffffff] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          30
        </p>
      </div>
      <div
        className="absolute bg-[#797979] bottom-[46.506%] left-[41.158%] right-[52.304%] top-[40.482%]"
        data-name="tertiary40"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[43.301%] right-[54.662%] text-[#ffffff] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          40
        </p>
      </div>
      <div
        className="absolute bg-[#8a8a8a] bottom-[46.506%] left-[47.696%] right-[45.766%] top-[40.482%]"
        data-name="tertiary50"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[49.839%] right-[48.124%] text-[#ffffff] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          50
        </p>
      </div>
      <div
        className="absolute bg-[#b1b1b1] bottom-[46.506%] left-[54.234%] right-[39.228%] top-[40.482%]"
        data-name="tertiary60"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[56.377%] right-[41.586%] text-[#000000] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          60
        </p>
      </div>
      <div
        className="absolute bg-[#c9c9c9] bottom-[46.506%] left-[60.772%] right-[32.69%] top-[40.482%]"
        data-name="tertiary70"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[62.969%] right-[34.995%] text-[#000000] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          70
        </p>
      </div>
      <div
        className="absolute bg-[#d9d9d9] bottom-[46.506%] left-[67.31%] right-[26.152%] top-[40.482%]"
        data-name="tertiary80"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[69.507%] right-[28.457%] text-[#000000] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          80
        </p>
      </div>
      <div
        className="absolute bg-[#e8e8e8] bottom-[46.506%] left-[73.848%] right-[19.614%] top-[40.482%]"
        data-name="tertiary90"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[75.991%] right-[21.972%] text-[#000000] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          90
        </p>
      </div>
      <div
        className="absolute bg-[#fbfbfb] bottom-[46.506%] left-[80.386%] right-[13.076%] top-[40.482%]"
        data-name="tertiary95"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[82.637%] right-[15.327%] text-[#000000] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          95
        </p>
      </div>
      <div
        className="absolute bg-[#fffbfa] bottom-[46.506%] left-[86.924%] right-[6.538%] top-[40.482%]"
        data-name="tertiary99"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[89.175%] right-[8.789%] text-[#000000] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          99
        </p>
      </div>
      <div
        className="absolute bg-[#ffffff] bottom-[46.506%] left-[93.462%] right-0 top-[40.482%]"
        data-name="tertiary100"
      />
      <div
        className="absolute bottom-[55.422%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[95.177%] right-[1.822%] text-[#000000] text-[16px] text-left text-nowrap top-[41.687%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          100
        </p>
      </div>
      <div className="absolute bottom-[40.482%] font-['Montserrat:Regular',_sans-serif] font-normal leading-[0] left-0 right-[96.141%] text-[#201b12] text-[14px] text-left text-nowrap top-[57.108%] tracking-[0.25px]">
        <p className="adjustLetterSpacing block leading-[20px] whitespace-pre">
          Error
        </p>
      </div>
      <div
        className="absolute bg-[#000000] bottom-[31.084%] left-[15.005%] right-[78.457%] top-[55.904%]"
        data-name="error0"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[17.685%] right-[81.243%] text-[#ffffff] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          0
        </p>
      </div>
      <div
        className="absolute bg-[#410e0b] bottom-[31.084%] left-[21.543%] right-[71.918%] top-[55.904%]"
        data-name="error10"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[23.848%] right-[74.116%] text-[#ffffff] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          10
        </p>
      </div>
      <div
        className="absolute bg-[#601410] bottom-[31.084%] left-[28.081%] right-[65.38%] top-[55.904%]"
        data-name="error20"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[30.279%] right-[67.685%] text-[#ffffff] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          20
        </p>
      </div>
      <div
        className="absolute bg-[#8c1d18] bottom-[31.084%] left-[34.62%] right-[58.842%] top-[55.904%]"
        data-name="error30"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[36.817%] right-[61.147%] text-[#ffffff] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          30
        </p>
      </div>
      <div
        className="absolute bg-[#b3261e] bottom-[31.084%] left-[41.158%] right-[52.304%] top-[55.904%]"
        data-name="error40"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[43.301%] right-[54.662%] text-[#ffffff] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          40
        </p>
      </div>
      <div
        className="absolute bg-[#dc362e] bottom-[31.084%] left-[47.696%] right-[45.766%] top-[55.904%]"
        data-name="error50"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[49.839%] right-[48.124%] text-[#ffffff] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          50
        </p>
      </div>
      <div
        className="absolute bg-[#e46962] bottom-[31.084%] left-[54.234%] right-[39.228%] top-[55.904%]"
        data-name="error60"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[56.377%] right-[41.586%] text-[#000000] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          60
        </p>
      </div>
      <div
        className="absolute bg-[#ec928e] bottom-[31.084%] left-[60.772%] right-[32.69%] top-[55.904%]"
        data-name="error70"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[62.969%] right-[34.995%] text-[#000000] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          70
        </p>
      </div>
      <div
        className="absolute bg-[#f2b8b5] bottom-[31.084%] left-[67.31%] right-[26.152%] top-[55.904%]"
        data-name="error80"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[69.507%] right-[28.457%] text-[#000000] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          80
        </p>
      </div>
      <div
        className="absolute bg-[#f9dedc] bottom-[31.084%] left-[73.848%] right-[19.614%] top-[55.904%]"
        data-name="error90"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[75.991%] right-[21.972%] text-[#000000] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          90
        </p>
      </div>
      <div
        className="absolute bg-[#fceeee] bottom-[31.084%] left-[80.386%] right-[13.076%] top-[55.904%]"
        data-name="error95"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[82.637%] right-[15.327%] text-[#000000] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          95
        </p>
      </div>
      <div
        className="absolute bg-[#fffbf9] bottom-[31.084%] left-[86.924%] right-[6.538%] top-[55.904%]"
        data-name="error99"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[89.175%] right-[8.789%] text-[#000000] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          99
        </p>
      </div>
      <div
        className="absolute bg-[#ffffff] bottom-[31.084%] left-[93.462%] right-0 top-[55.904%]"
        data-name="error100"
      />
      <div
        className="absolute bottom-[40%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[95.177%] right-[1.822%] text-[#000000] text-[16px] text-left text-nowrap top-[57.108%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          100
        </p>
      </div>
      <div className="absolute bottom-[20.241%] font-['Montserrat:Regular',_sans-serif] font-normal leading-[0] left-0 right-[94.212%] text-[#201b12] text-[14px] text-left text-nowrap top-[77.349%] tracking-[0.25px]">
        <p className="adjustLetterSpacing block leading-[20px] whitespace-pre">
          Neutral
        </p>
      </div>
      <div
        className="absolute bg-[#000000] bottom-[10.843%] left-[15.005%] right-[78.457%] top-[76.145%]"
        data-name="neutral0"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[17.685%] right-[81.243%] text-[#ffffff] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          0
        </p>
      </div>
      <div
        className="absolute bg-[#1d1b20] bottom-[10.843%] left-[21.543%] right-[71.918%] top-[76.145%]"
        data-name="neutral10"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[23.848%] right-[74.116%] text-[#ffffff] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          10
        </p>
      </div>
      <div
        className="absolute bg-[#322f35] bottom-[10.843%] left-[28.081%] right-[65.38%] top-[76.145%]"
        data-name="neutral20"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[30.279%] right-[67.685%] text-[#ffffff] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          20
        </p>
      </div>
      <div
        className="absolute bg-[#48464c] bottom-[10.843%] left-[34.62%] right-[58.842%] top-[76.145%]"
        data-name="neutral30"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[36.817%] right-[61.147%] text-[#ffffff] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          30
        </p>
      </div>
      <div
        className="absolute bg-[#605d64] bottom-[10.843%] left-[41.158%] right-[52.304%] top-[76.145%]"
        data-name="neutral40"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[43.301%] right-[54.662%] text-[#ffffff] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          40
        </p>
      </div>
      <div
        className="absolute bg-[#79767d] bottom-[10.843%] left-[47.696%] right-[45.766%] top-[76.145%]"
        data-name="neutral50"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[49.839%] right-[48.124%] text-[#ffffff] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          50
        </p>
      </div>
      <div
        className="absolute bg-[#938f96] bottom-[10.843%] left-[54.234%] right-[39.228%] top-[76.145%]"
        data-name="neutral60"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[56.377%] right-[41.586%] text-[#000000] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          60
        </p>
      </div>
      <div
        className="absolute bg-[#aea9b1] bottom-[10.843%] left-[60.772%] right-[32.69%] top-[76.145%]"
        data-name="neutral70"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[62.969%] right-[34.995%] text-[#000000] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          70
        </p>
      </div>
      <div
        className="absolute bg-[#cac5cd] bottom-[10.843%] left-[67.31%] right-[26.152%] top-[76.145%]"
        data-name="neutral80"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[69.507%] right-[28.457%] text-[#000000] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          80
        </p>
      </div>
      <div
        className="absolute bg-[#e6e0e9] bottom-[10.843%] left-[73.848%] right-[19.614%] top-[76.145%]"
        data-name="neutral90"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[75.991%] right-[21.972%] text-[#000000] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          90
        </p>
      </div>
      <div
        className="absolute bg-[#f5eff7] bottom-[10.843%] left-[80.386%] right-[13.076%] top-[76.145%]"
        data-name="neutral95"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[82.637%] right-[15.327%] text-[#000000] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          95
        </p>
      </div>
      <div
        className="absolute bg-[#fffbff] bottom-[10.843%] left-[86.924%] right-[6.538%] top-[76.145%]"
        data-name="neutral99"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[89.175%] right-[8.789%] text-[#000000] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          99
        </p>
      </div>
      <div
        className="absolute bg-[#ffffff] bottom-[10.843%] left-[93.462%] right-0 top-[76.145%]"
        data-name="neutral100"
      />
      <div
        className="absolute bottom-[19.759%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[95.177%] right-[1.822%] text-[#000000] text-[16px] text-left text-nowrap top-[77.349%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          100
        </p>
      </div>
      <div className="absolute bottom-[4.819%] font-['Montserrat:Regular',_sans-serif] font-normal leading-[0] left-0 right-[88.21%] text-[#201b12] text-[14px] text-left text-nowrap top-[92.771%] tracking-[0.25px]">
        <p className="adjustLetterSpacing block leading-[20px] whitespace-pre">
          Neutral Variant
        </p>
      </div>
      <div
        className="absolute bg-[#000000] bottom-0 left-[15.005%] right-[78.457%] top-[91.566%]"
        data-name="neutral-variant0"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[17.685%] right-[81.243%] text-[#ffffff] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          0
        </p>
      </div>
      <div
        className="absolute bg-[#1d1a22] bottom-0 left-[21.543%] right-[71.918%] top-[91.566%]"
        data-name="neutral-variant10"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[23.848%] right-[74.116%] text-[#ffffff] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          10
        </p>
      </div>
      <div
        className="absolute bg-[#322f37] bottom-0 left-[28.081%] right-[65.38%] top-[91.566%]"
        data-name="neutral-variant20"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[30.279%] right-[67.685%] text-[#ffffff] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          20
        </p>
      </div>
      <div
        className="absolute bg-[#49454f] bottom-0 left-[34.62%] right-[58.842%] top-[91.566%]"
        data-name="neutral-variant30"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[36.817%] right-[61.147%] text-[#ffffff] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          30
        </p>
      </div>
      <div
        className="absolute bg-[#605d66] bottom-0 left-[41.158%] right-[52.304%] top-[91.566%]"
        data-name="neutral-variant40"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[43.301%] right-[54.662%] text-[#ffffff] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          40
        </p>
      </div>
      <div
        className="absolute bg-[#79747e] bottom-0 left-[47.696%] right-[45.766%] top-[91.566%]"
        data-name="neutral-variant50"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[49.839%] right-[48.124%] text-[#ffffff] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          50
        </p>
      </div>
      <div
        className="absolute bg-[#938f99] bottom-0 left-[54.234%] right-[39.228%] top-[91.566%]"
        data-name="neutral-variant60"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[56.377%] right-[41.586%] text-[#000000] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          60
        </p>
      </div>
      <div
        className="absolute bg-[#aea9b4] bottom-0 left-[60.772%] right-[32.69%] top-[91.566%]"
        data-name="neutral-variant70"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[62.969%] right-[34.995%] text-[#000000] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          70
        </p>
      </div>
      <div
        className="absolute bg-[#cac4d0] bottom-0 left-[67.31%] right-[26.152%] top-[91.566%]"
        data-name="neutral-variant80"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[69.507%] right-[28.457%] text-[#000000] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          80
        </p>
      </div>
      <div
        className="absolute bg-[#e7e0ec] bottom-0 left-[73.848%] right-[19.614%] top-[91.566%]"
        data-name="neutral-variant90"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[75.991%] right-[21.972%] text-[#000000] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          90
        </p>
      </div>
      <div
        className="absolute bg-[#f5eefa] bottom-0 left-[80.386%] right-[13.076%] top-[91.566%]"
        data-name="neutral-variant95"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[82.637%] right-[15.327%] text-[#000000] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          95
        </p>
      </div>
      <div
        className="absolute bg-[#fffbfe] bottom-0 left-[86.924%] right-[6.538%] top-[91.566%]"
        data-name="neutral-variant99"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[89.175%] right-[8.789%] text-[#000000] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          99
        </p>
      </div>
      <div
        className="absolute bg-[#ffffff] bottom-0 left-[93.462%] right-0 top-[91.566%]"
        data-name="neutral-variant100"
      />
      <div
        className="absolute bottom-[4.337%] font-['Roboto:Medium',_sans-serif] font-medium leading-[0] left-[95.177%] right-[1.822%] text-[#000000] text-[16px] text-left text-nowrap top-[92.771%] tracking-[0.1px]"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="adjustLetterSpacing block leading-[24px] whitespace-pre">
          100
        </p>
      </div>
    </div>
  );
}

export default function TonalPalettes1() {
  return (
    <div className="relative size-full" data-name=".Tonal palettes">
      <TonalPalettes />
    </div>
  );
}