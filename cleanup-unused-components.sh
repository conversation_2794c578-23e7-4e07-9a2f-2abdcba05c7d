#!/bin/bash
# Script to remove unused components - for reference only

# Components that are NOT used in the current Deal Builder Focus application:
# - AddCustomerForm.tsx
# - AddVehicleForm.tsx  
# - AppHeader.tsx
# - AppSidebar.tsx
# - CustomerDetails.tsx
# - CustomerMatchingModal.tsx
# - CustomerMatchingPanel.tsx
# - DashboardContent.tsx
# - DealsContent.tsx
# - HorizontalDealForm.tsx
# - StatCard.tsx
# - StatsIcon.tsx
# - StepTracker.tsx (we have StepNotesButton instead)
# - StepTrackerDemo.tsx
# - VehicleDetails.tsx
# - VehicleMatchingModal.tsx
# - VehicleMatchingPanel.tsx

# Config files that may not be needed:
# - config/dealColumns.tsx
# - config/tableColumns.tsx

# Hooks that may not be needed:
# - hooks/useAppHandlers.ts

echo "These components should be removed as they're not used in the current application"